package com.gumtree.integration.repository;

import com.gumtree.seller.domain.sellertype.dto.SellerTypeEnum;
import com.gumtree.seller.domain.sellertype.entity.SellerType;
import com.gumtree.seller.repository.sellertype.SellerTypeRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/seller-type.xml", dataSourceName = "sellerDatasource")
public class SellerTypeRepositoryTest extends BaseRepositoryTest {

    private static final String PRIVATE_TYPE = "PRIVATE";
    private static final String TRADE_TYPE = "TRADE";

    private static final long ACCOUNT_ID_3 = 97302155L;
    private static final long ACCOUNT_ID_4 = 80016239L;

    private static final long PROPERTY_CAT_ID = 10201L;
    private static final long MATTRESSES_CAT_ID = 4610L;

    @Autowired
    private SellerTypeRepository repository;

    @Test
    public void testFindAll() {
        assertThat(repository.findAll().size(), equalTo(6));
    }

    @Test
    public void testFindByAccountAndCategoryId() {
        assertFindByAccountAndCatId(ACCOUNT_ID_3, PROPERTY_CAT_ID, TRADE_TYPE);
        assertFindByAccountAndCatId(ACCOUNT_ID_3, MATTRESSES_CAT_ID, TRADE_TYPE);
        assertFindByAccountAndCatId(ACCOUNT_ID_4, MATTRESSES_CAT_ID, PRIVATE_TYPE);
    }

    @Test
    public void testUpdateSellerType() {
        assertFindByAccountAndCatId(ACCOUNT_ID_4, MATTRESSES_CAT_ID, PRIVATE_TYPE);
        SellerType sellerType = new SellerType();
        sellerType.setAccountId(ACCOUNT_ID_4);
        sellerType.setCategoryId(MATTRESSES_CAT_ID);
        sellerType.setType(SellerTypeEnum.TRADE);
        repository.saveAndFlush(sellerType);
        assertFindByAccountAndCatId(ACCOUNT_ID_4, MATTRESSES_CAT_ID, TRADE_TYPE);
    }

    @Test
    public void testInsertSellerType() {
        Optional<SellerType> stOptional =repository.findByAccountAndCategoryId(ACCOUNT_ID_4, PROPERTY_CAT_ID);
        Assert.assertFalse(stOptional.isPresent());

        SellerType sellerType = new SellerType();
        sellerType.setAccountId(ACCOUNT_ID_4);
        sellerType.setCategoryId(PROPERTY_CAT_ID);
        sellerType.setType(SellerTypeEnum.PRIVATE);
        repository.saveAndFlush(sellerType);
        assertFindByAccountAndCatId(ACCOUNT_ID_4, PROPERTY_CAT_ID, PRIVATE_TYPE);
    }

    private void assertFindByAccountAndCatId(long accountId, long categoryId, String type) {
        Optional<SellerType> stOptional = repository.findByAccountAndCategoryId(accountId, categoryId);
        Assert.assertTrue(stOptional.isPresent());
        SellerType st = stOptional.get();
        Assert.assertEquals(accountId, (long) st.getAccountId());
        Assert.assertEquals(categoryId, (long) st.getCategoryId());
        Assert.assertEquals(type, st.getType().name());
    }
}
