package com.gumtree.integration.repository.security;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.user.entity.AbstractPermission;
import com.gumtree.seller.domain.user.entity.Role;
import com.gumtree.seller.repository.security.RoleRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.gumtree.seller.util.DomainObjectMatchers.containsPermission;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsNull.notNullValue;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/users-and-roles-and-permissions.xml", dataSourceName="sellerDatasource")
public class RoleRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private RoleRepository roleRepository;

    @Test
    @Transactional(readOnly = true)
    public void findByNameFetchesCorrectRoleFromDatabase() {
        Role role = roleRepository.findByName(Role.RoleName.API_USER);
        assertThat(role, notNullValue());
        assertThat(role.getName(), equalTo(Role.RoleName.API_USER));
        List<AbstractPermission> permissions = new ArrayList<AbstractPermission>(role.getPermissions());
        assertThat(permissions, containsPermission("domain1", "add", "*"));
        assertThat(permissions, containsPermission("domain1", "delete", "*"));
        assertThat(permissions, containsPermission("domain1", "update", "*"));
    }
}
