package com.gumtree.integration.repository.packages;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.wallet.packages.repository.CreditPackageRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/packages.xml", dataSourceName="sellerDatasource")
public class CreditPackageRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private CreditPackageRepository creditPackageRepository;

    @Test
    @Transactional
    public void canFindAPackageById() {
        assertThat(creditPackageRepository.findOne(6L).getInitialCredits(), equalTo(15L));
    }

    @Test
    @Transactional
    public void canFindBySalesforceId() {
        assertThat(creditPackageRepository.findBySalesforceId("ASALESFORCEID").getId(), equalTo(1L));
    }

    @Test
    @Transactional
    public void aDeletedPackageReturnsNull(){
        assertThat(creditPackageRepository.findBySalesforceId("DELETEDPACKAGE"), equalTo(null));
    }
    
    @Test
    @Transactional
    public void testGetByAccountIdNoCapabilities() {
        assertThat(creditPackageRepository.findByAccountId(15L).size(), equalTo(1));
    }

    @Test
    @Transactional
    public void testGetByAccountIdWithCapabilities() {
        assertThat(creditPackageRepository.findByAccountIdWithCapabilities(15L).size(), equalTo(2));
    }
}
