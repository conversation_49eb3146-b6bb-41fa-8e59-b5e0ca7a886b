package com.gumtree.integration.repository.account;


import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.repository.account.AccountImageRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import static org.fest.assertions.api.Assertions.assertThat;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/account-image.xml", dataSourceName="sellerDatasource")
public class AccountImageRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private AccountImageRepository repository;

    @Test
    public void getDefaultImageShouldFindOneWhenOnlyImageColumnPresent() {
        Image defaultImage = repository.getDefaultImage(100001L);
        compareData(defaultImage, 11L, "https://domain.com/01.JPG", "2022-12-01T11:22:35", "PROCESSING", null);
    }

    @Test
    public void getDefaultImageShouldFindOneWhenUrlPopulatedInBothColumns() {
        Image defaultImage = repository.getDefaultImage(100002L);
        compareData(defaultImage, 22L, "https://imagedelivery.net/GT/02/1", "2022-12-02T11:22:36", "", "2023-12-02T11:22:36");
    }

    @Test
    public void getDefaultImageShouldFindOneWhenUrlPopulatedInOnlyInImageColumn() {
        Image defaultImage = repository.getDefaultImage(100003L);
        compareData(defaultImage, 33L, "https://domain.com/03.JPG", "2022-12-03T11:22:35", "UPLOADED", null);
    }

    @Test
    public void getDefaultImageShouldFindOneWhenUrlPopulatedInOnlyInMediaColumn() {
        Image defaultImage = repository.getDefaultImage(100004L);
        compareData(defaultImage, 44L, "https://imagedelivery.net/GT/04/1", "2022-12-04T11:22:36", "PROCESSING", "2022-12-04T11:12:36");
    }

    @Test
    public void getDefaultImageShouldNotFindOne() {
        Image defaultImage = repository.getDefaultImage(101L);
        assertThat(defaultImage).isNull();
    }

    private static void compareData(Image defaultImage, Long id, String url, String uploadDate, String status, String createDate) {
        assertThat(defaultImage.getId()).isEqualTo(id);
        assertThat(defaultImage.getUrl()).isEqualTo(url);
        assertThat(defaultImage.getUploadDate()).isEqualTo(Date
                .from(LocalDateTime.parse(uploadDate).atZone(ZoneId.systemDefault())
                        .toInstant()));
        assertThat(defaultImage.getImageStatus()).isEqualTo(status);
        Date createDateObj = createDate != null
                ? Date.from(LocalDateTime.parse(createDate).atZone(ZoneId.systemDefault()).toInstant())
                : null;
        assertThat(defaultImage.getCreatedDate()).isEqualTo(createDateObj);
    }
}
