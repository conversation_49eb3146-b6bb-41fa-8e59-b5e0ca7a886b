package com.gumtree.integration.repository;

import com.gumtree.api.mvc.profile.IntegrationTests;
import com.gumtree.config.BapiServerContextInitializer;
import com.gumtree.test.CleanInsertTestExecutionListener;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;

/**
 * Base class for integration tests
 */
@Category({IntegrationTests.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        initializers = BapiServerContextInitializer.class,
        locations = "/com/gumtree/integration/repository/repository-application-context.xml"
)
@TestExecutionListeners(
        {
                DependencyInjectionTestExecutionListener.class,
                CleanInsertTestExecutionListener.class,
                TransactionalTestExecutionListener.class
        }
)
public abstract class BaseRepositoryTest {

}