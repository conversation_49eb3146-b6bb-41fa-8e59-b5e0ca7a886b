package com.gumtree.integration.repository.email;

import com.gumtree.api.EmailStatus;
import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.email.entity.ContactEmail;
import com.gumtree.seller.repository.email.ContactEmailRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.fest.assertions.api.Assertions.assertThat;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/contact-email.xml", dataSourceName="sellerDatasource")
public class ContactEmailRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private ContactEmailRepository repository;

    @Test
    public void verifyFindByEmailAddressReturnsRecognisedEmail() {
        // when
        List<ContactEmail> emails = repository.findByEmailAndStatus("<EMAIL>", EmailStatus.VERIFIED);

        // then
        assertThat(emails.stream().map(ContactEmail::getEmail).collect(Collectors.toList()))
                .isEqualTo(Collections.singletonList("<EMAIL>"));
    }

    @Test
    public void verifyFindByEMailReturnsNothingIfEmailIsNotInRequiredStatus() {
        // when
        List<ContactEmail> emails = repository.findByEmailAndStatus("<EMAIL>", EmailStatus.DELETED);

        // then
        assertThat(emails).isEmpty();
    }

    @Test
    public void findByUserIdAndEmail() {
        // when
        ContactEmail email = repository.findByUsrIdAndEmail(1L, "<EMAIL>");

        // then
        assertThat(email.getEmail()).isEqualTo("<EMAIL>");
        assertThat(email.getUsrId()).isEqualTo(1L);
    }

    @Test
    public void findByUserIdAndEmailIsCaseSensitive() {
        // when
        ContactEmail email = repository.findByUsrIdAndEmail(1L, "<EMAIL>");

        // then
        assertThat(email).isNull();
    }

    @Test(expected = DataIntegrityViolationException.class)
    public void emailAndUserIdCombinationMustBeUnique() {
        // given
        String email = "<EMAIL>";
        ContactEmail.Builder eBuilder = ContactEmail.builder().usrId(1L).preferred(false).email(email).status(EmailStatus.VERIFICATION_SENT);
        repository.save(eBuilder.email(email.toUpperCase()).build());

        // when
        repository.save(eBuilder.build());
    }

    @Test
    public void sameEmailCanBelongToMultipleUsers() {
        // given
        String email = "<EMAIL>";
        ContactEmail.Builder eBuilder = ContactEmail.builder().preferred(false).email(email).status(EmailStatus.VERIFICATION_SENT);

        // when
        repository.save(eBuilder.id(1L).usrId(2L).build());
        repository.save(eBuilder.id(2L).usrId(3L).build());

        // then
        assertThat(repository.findByUsrId(2L).get(0).getEmail()).isEqualTo(email);
        assertThat(repository.findByUsrId(2L)).hasSize(1);
        assertThat(repository.findByUsrId(3L).get(0).getEmail()).isEqualTo(email);
        assertThat(repository.findByUsrId(3L)).hasSize(1);
    }
}
