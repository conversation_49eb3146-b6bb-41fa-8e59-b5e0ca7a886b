package com.gumtree.integration.repository.user;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.user.entity.ApiKey;
import com.gumtree.seller.domain.user.entity.Role;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.repository.security.RoleRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.test.DataSetLocation;
import org.hamcrest.CoreMatchers;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.List;
import java.util.Set;

import static com.gumtree.seller.util.DomainObjectBuilders.anApiKey;
import static com.gumtree.seller.util.DomainObjectMatchers.isAnApiKeyListContaining;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/users-and-roles-and-permissions.xml", dataSourceName="sellerDatasource")
public class UserRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private UserRepository repository;

    @Autowired
    private RoleRepository roleRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Test
    public void verifyFindByUsernameReturnsRecognisedUsername() {
        User user = repository.findByUsername("user1");
        assertThat(user, CoreMatchers.notNullValue());
        assertThat(user.getId(), equalTo(1L));
        assertThat(user.getUsername(), equalTo("user1"));
        assertThat(user.getPassword(), equalTo("password1"));
        assertThat(user.getSalt(), equalTo("salt1"));
    }

    @Test
    public void verifyFindByUsernameReturnsNulLForUnrecognisedUsername() {
        assertThat(repository.findByUsername("user2"), CoreMatchers.nullValue());
    }

    @Test
    @Transactional(readOnly = true)
    public void verifyRolesForRecognisedUsername() {
        User user = repository.findByUsername("user1");
        List<Role> roles = user.getRoles();
        assertThat(roles, CoreMatchers.notNullValue());
        assertThat(roles.size(), equalTo(2));
        assertThat(roles.get(0).getName(), equalTo(Role.RoleName.API_USER));
        assertThat(roles.get(0).getDescription(), equalTo("User can post adverts via the Write API"));
        assertThat(roles.get(1).getName(), equalTo(Role.RoleName.SUPER_USER));
        assertThat(roles.get(1).getDescription(), equalTo("User can do anything"));
    }

    @Test
    @Transactional(readOnly = true)
    public void verifyPermissionsForRecognisedUsername() {
        User user = repository.findByUsername("user1");
        Set<String> permissions = user.getPermissions();
        assertThat(permissions, CoreMatchers.notNullValue());
        assertThat(permissions.size(), equalTo(6));
        assertThat(permissions.contains("domain1:add:*"), equalTo(true));
        assertThat(permissions.contains("domain1:delete:*"), equalTo(true));
        assertThat(permissions.contains("domain1:update:*"), equalTo(true));
        assertThat(permissions.contains("domain2:add:1"), equalTo(true));
        assertThat(permissions.contains("domain2:delete:1"), equalTo(true));
        assertThat(permissions.contains("domain2:update:1"), equalTo(true));
    }

    @Test
    @Transactional(readOnly = true)
    public void userContainsExpectedApiKeys() {
        User user = repository.findByUsername("user1");
        List<ApiKey> apiKeys = user.getApiKeys();
        assertThat(apiKeys.size(), equalTo(2));
        assertThat(apiKeys, isAnApiKeyListContaining(anApiKey().forUser(user).withAccessKey("testApiUser1AccessKey1").withPrivateKey("testApiUser1PrivateKey1")));
        assertThat(apiKeys, isAnApiKeyListContaining(anApiKey().forUser(user).withAccessKey("testApiUser1AccessKey2").withPrivateKey("testApiUser1PrivateKey2")));
    }

    @Test
    @Transactional
    public void savingUserSavesAnyNewlyAssociatedRoles() {
        User user = repository.findByUsername("user-with-no-roles");
        Role role = roleRepository.findByName(Role.RoleName.API_USER);
        user.addRole(role);
        repository.save(user);
        repository.flush();
        user = repository.findByUsername("user-with-no-roles");
        assertThat(user.getRoles().size(), equalTo(1));
        assertThat(user.getRoles().get(0).getName(), equalTo(Role.RoleName.API_USER));
    }

    @Test
    @Transactional
    public void testDeleteUserGender() {
        User user1 = repository.findByUsername("delete1");
        User user2 = repository.findByUsername("delete2");
        assertThat(user1.getGender().getGender(), equalTo("male"));
        assertThat(user2.getGender().getGender(), equalTo("male"));

        repository.deleteUserGender(user1.getId());
        repository.flush();
        resetHibernateCache();

        user1 = repository.findByUsername("delete1");
        user2 = repository.findByUsername("delete2");
        assertThat(user1.getGender(), is(nullValue()));
        assertThat(user2.getGender().getGender(), equalTo("male"));
    }

    @Test
    @Transactional
    public void testDeleteSocialData() {
        User user1 = repository.findByUsername("delete1");
        User user2 = repository.findByUsername("delete2");
        assertThat(user1.getSocialDataList(), hasSize(1));
        assertThat(user2.getSocialDataList(), hasSize(1));

        repository.deleteSocialDataByUser(user1);
        repository.flush();
        resetHibernateCache();

        user1 = repository.findByUsername("delete1");
        user2 = repository.findByUsername("delete2");
        assertThat(user1.getSocialDataList(), hasSize(0));
        assertThat(user2.getSocialDataList(), hasSize(1));
    }

    @Test
    @Transactional
    public void testDeleteRoles() {
        User user1 = repository.findByUsername("delete1");
        User user2 = repository.findByUsername("delete2");
        assertThat(user1.getRoles(), hasSize(2));
        assertThat(user2.getRoles(), hasSize(2));

        repository.deleteUserRoles(user1.getId());
        repository.flush();
        resetHibernateCache();

        user1 = repository.findByUsername("delete1");
        user2 = repository.findByUsername("delete2");
        assertThat(user1.getRoles(), hasSize(0));
        assertThat(user2.getRoles(), hasSize(2));
    }

    @Test
    @Transactional
    public void testDeleteByUser() {
        User user1 = repository.findByUsername("delete1");
        User user2 = repository.findByUsername("delete2");

        assertThat(user1, is(not(nullValue())));
        assertThat(user2, is(not(nullValue())));
        assertThat(user1.getId(),  is(not(equalTo(user2.getId()))));

        // needs to delete dependencies first
        repository.deleteSocialDataByUser(user1);
        repository.deleteUserGender(user1.getId());
        repository.deleteUserRoles(user1.getId());

        repository.deleteByUser(user1);
        repository.flush();
        resetHibernateCache();

        user1 = repository.findByUsername("delete1");
        user2 = repository.findByUsername("delete2");
        assertThat(user1, is(nullValue()));
        assertThat(user2, is(not(nullValue())));
    }

    private void resetHibernateCache() {
        entityManager.clear();
    }
}
