package com.gumtree.integration.repository.marketing;


import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.marketing.entity.UserMarketingPreference;
import com.gumtree.seller.domain.marketing.entity.UserMarketingPreferenceCategory;
import com.gumtree.seller.repository.marketing.UserMarketingPreferenceCategoryRepository;
import com.gumtree.seller.repository.marketing.UserMarketingPreferenceRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/users-and-roles-and-permissions.xml", dataSourceName="sellerDatasource")
public class UserMarketingPreferenceRepositoryTest extends BaseRepositoryTest {


    @Autowired
    private UserMarketingPreferenceRepository repository;

    @Autowired
    private UserMarketingPreferenceCategoryRepository categoryRepository;

    @Test(expected = DataIntegrityViolationException.class)
    @Transactional
    public void cannotSaveWithNoAssociatedEmailAddress() {

        UserMarketingPreference pref = new UserMarketingPreference();
        repository.saveAndFlush(pref);

    }

    @Test
    @Transactional
    public void successfullySavesUserMarketingPreference() {

        UserMarketingPreference pref = new UserMarketingPreference();
        EmailAddress address = new EmailAddress();
        address.setEmail("<EMAIL>");
        pref.setEmailAddress(address);
        repository.saveAndFlush(pref);

    }

    @Test
    @Transactional
    public void cannotLoadSavedUserMarketingPreferenceWithUnknownEmail() {

        UserMarketingPreference userMarketingPreference = repository.findByUsername("<EMAIL>");
        assertThat(userMarketingPreference, nullValue());

    }

    @Test
    @Transactional
    public void canLoadSavedUserMarketingPreferenceWithKnownEmail() {

        UserMarketingPreference userMarketingPreference = repository.findByUsername("<EMAIL>");
        assertThat(userMarketingPreference.getEmailAddress().getEmail(), equalTo("<EMAIL>"));
        assertThat(userMarketingPreference.getEmailAddress().getId(), equalTo(3L));
        assertThat(userMarketingPreference.getId(), equalTo(1L));
        assertThat(userMarketingPreference.isOptInGumtree(), equalTo(false));
        assertThat(userMarketingPreference.isOptInEbay(), equalTo(true));
        assertThat(userMarketingPreference.isOptInThirdParty(), equalTo(false));

    }

    @Test
    @Transactional
    public void canLoadPreferenceAndPersistWithUpdatedSettings() {
        UserMarketingPreference userMarketingPreference = repository.findByUsername("<EMAIL>");
        userMarketingPreference.setOptInEbay(false);
        repository.saveAndFlush(userMarketingPreference);

        UserMarketingPreference loadedPreference = repository.findByUsername("<EMAIL>");
        assertThat(loadedPreference.isOptInEbay(), equalTo(false));

    }

    @Test
    @Transactional
    public void canSavePreferenceWithSelectedCategoriesAndLocation() {

        EmailAddress address = new EmailAddress();
        address.setEmail("<EMAIL>");

        UserMarketingPreferenceCategory category = new UserMarketingPreferenceCategory();
        category.setCategoryId(1000L);


        List<UserMarketingPreferenceCategory> categoryList = new ArrayList();
        categoryList.add(category);
        UserMarketingPreference pref = new UserMarketingPreference();
        pref.setEmailAddress(address);
        pref.setUserMarketingPreferenceCategories(categoryList);
        pref.setPostcodeLocationId(3L);

        category.setUserMarketingPreference(pref);
        repository.saveAndFlush(pref);

    }

    @Test
    @Transactional
    public void canLoadPreferenceWithSelectedCategoriesAndLocation() {
        UserMarketingPreference preference = repository.findByUsername("<EMAIL>");
        List<UserMarketingPreferenceCategory> userMarketingPreferenceCategories = preference.getUserMarketingPreferenceCategories();
        assertThat(preference.getPostcodeLocationId(), equalTo(8888L));
        assertThat(userMarketingPreferenceCategories.size(), equalTo(2));
        assertThat(userMarketingPreferenceCategories.get(0).getCategoryId(), equalTo(1234L));
        assertThat(userMarketingPreferenceCategories.get(1).getCategoryId(), equalTo(5678L));

    }

    @Test
    @Transactional
    public void canUpdatePreferenceWithSelectedCategoriesAndLocationAddingCategory() {
        UserMarketingPreference preference = repository.findByUsername("<EMAIL>");
        List<UserMarketingPreferenceCategory> userMarketingPreferenceCategories = preference.getUserMarketingPreferenceCategories();

        preference.setPostcodeLocationId(9999L);
        UserMarketingPreferenceCategory category = userMarketingPreferenceCategories.get(0);
        category.setCategoryId(1000L);

        UserMarketingPreferenceCategory category2 = userMarketingPreferenceCategories.get(1);
        category2.setCategoryId(2000L);

        UserMarketingPreferenceCategory category3 = new UserMarketingPreferenceCategory();
        category3.setCategoryId(3000L);
        category3.setUserMarketingPreference(preference);
        userMarketingPreferenceCategories.add(category3);

        repository.saveAndFlush(preference);

        UserMarketingPreference prefLoaded = repository.findByUsername("<EMAIL>");

        assertThat(prefLoaded.getPostcodeLocationId(), equalTo(9999L));
        assertThat(prefLoaded.getUserMarketingPreferenceCategories().size(), equalTo(3));

    }

    @Test
    @Transactional
    public void canUpdatePreferenceWithSelectedCategoriesAndLocationRemovingCategory() {
        UserMarketingPreference preference = repository.findByUsername("<EMAIL>");
        List<UserMarketingPreferenceCategory> userMarketingPreferenceCategories = preference.getUserMarketingPreferenceCategories();

        userMarketingPreferenceCategories.remove(0);

        repository.saveAndFlush(preference);

        UserMarketingPreference prefLoaded = repository.findByUsername("<EMAIL>");

        assertThat(prefLoaded.getUserMarketingPreferenceCategories().size(), equalTo(1));

    }

    @Test
    @Transactional
    public void canUpdatePreferenceWithNoCategories() {
        UserMarketingPreference preference = repository.findByUsername("<EMAIL>");

        categoryRepository.deleteInBatch(preference.getUserMarketingPreferenceCategories());
        preference.setUserMarketingPreferenceCategories(new ArrayList<UserMarketingPreferenceCategory>());

        repository.saveAndFlush(preference);

        UserMarketingPreference prefLoaded = repository.findByUsername("<EMAIL>");
        assertThat(prefLoaded.getUserMarketingPreferenceCategories().size(), equalTo(0));

    }

    @Test
    @Transactional
    public void canUpdatePreferenceWithNullCategoriesAndNullLocation() {

        UserMarketingPreference preference = repository.findByUsername("<EMAIL>");
        categoryRepository.deleteInBatch(preference.getUserMarketingPreferenceCategories());

        preference.setUserMarketingPreferenceCategories(null);
        preference.setPostcodeLocationId(null);
        repository.saveAndFlush(preference);

        UserMarketingPreference prefLoaded = repository.findByUsername("<EMAIL>");
        assertThat(prefLoaded.getUserMarketingPreferenceCategories(), equalTo(null));
        assertThat(prefLoaded.getPostcodeLocationId(), equalTo(null));

    }
}
