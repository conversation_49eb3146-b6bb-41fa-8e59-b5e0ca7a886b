package com.gumtree.integration.repository.user;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.user.entity.UserCredentials;
import com.gumtree.seller.repository.user.UserCredentialsRepository;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.ObjectOptimisticLockingFailureException;

import java.util.Date;

import static org.fest.assertions.api.Assertions.assertThat;

public class UserCredentialsRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private UserCredentialsRepository repository;

    @Autowired
    @Qualifier("sellerDatabaseOperations")
    private JdbcTemplate jdbcTemplate;

    @Test
    public void saveShouldSetCreatedAndLastModifiedDate() {
        // when
        UserCredentials saved = repository.save(UserCredentials.create("mebee", "gumtree123"));

        // then
        UserCredentials fromDB = repository.findOne(saved.getId());
        assertThat(fromDB.getCreated()).isNotNull();
        assertThat(fromDB.getLastModified()).isNotNull();
        assertThat(fromDB.getVersion()).isEqualTo(0L);
    }

    @Test
    public void updateShouldIncreaseVersion() {
        UserCredentials credentials = repository.save(UserCredentials.create("watchout", "gumtree123"));
        Date preUpdateCreatedDate = credentials.getCreated();
        Long preUpdateLastModifiedDate = credentials.getLastModified().getTime();

        try {
            // Add a sleep in, so there is a gap for testing the last modified.
            Thread.sleep(100);
        } catch (InterruptedException e) {
            // NOOP
        }

        // when
        credentials.setPassword("newPassword");
        UserCredentials saved = repository.save(credentials);

        // then
        UserCredentials fromDB = repository.findOne(saved.getId());
        assertThat(fromDB.getVersion()).isEqualTo(1);
        assertThat(fromDB.getCreated()).isEqualTo(preUpdateCreatedDate);

        Long dbLastModified = fromDB.getLastModified().getTime();
        assertThat(dbLastModified).isGreaterThan(preUpdateLastModifiedDate);
    }

    @Test(expected = ObjectOptimisticLockingFailureException.class)
    public void updateAdvertFailsWhenVersionDiffersInDatabase() {
        // given
        UserCredentials credentials = repository.save(UserCredentials.create("maybe", "gumtree123"));

        // and
        jdbcTemplate.execute("update usr_credentials set version = 10 where id = " + credentials.getId());

        // when
        credentials.setPassword("newPassword");
        repository.save(credentials);
    }

    @Test
    public void shouldCreateUserCredentials() {
        // given
        UserCredentials userCredentials = UserCredentials.create("never", "gumtree123");

        // when
        UserCredentials saved = repository.save(userCredentials);

        // then
        assertThat(saved.getId()).isNotNull();
    }

    @Test
    public void shouldFindByUsername() {
        // given
        UserCredentials userCredentials = repository.save(UserCredentials.create("wishful", "gumtree123"));

        // when
        UserCredentials credentials = repository.findByUsername(userCredentials.getUsername());

        // then
        assertThat(credentials).isNotNull();
    }

    @Test
    public void shouldNotFindIfDoesNotExists() {
        // when
        UserCredentials credentials = repository.findByUsername("non-existing");

        // then
        assertThat(credentials).isNull();
    }

}
