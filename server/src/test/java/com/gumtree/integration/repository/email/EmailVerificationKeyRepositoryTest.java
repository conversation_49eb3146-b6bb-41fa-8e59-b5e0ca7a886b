package com.gumtree.integration.repository.email;

import com.gumtree.api.EmailStatus;
import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.email.entity.ContactEmail;
import com.gumtree.seller.domain.email.entity.EmailVerificationKey;
import com.gumtree.seller.repository.email.ContactEmailRepository;
import com.gumtree.seller.repository.email.EmailVerificationKeyRepository;
import com.gumtree.test.DataSetLocation;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;

import static org.fest.assertions.api.Assertions.assertThat;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/email_verification_key.xml", dataSourceName="sellerDatasource")
public class EmailVerificationKeyRepositoryTest extends BaseRepositoryTest {
    private static final Long USR_ID = 1L;

    @Autowired
    private EmailVerificationKeyRepository repository;

    @Autowired
    private ContactEmailRepository contactEmailRepository;

    @Test
    public void verifyFindByKeyReturnsRecognisedEmail() {
        // given
        String verificationKey = "ab23";
        ContactEmail email1 = contactEmailRepository.save(ContactEmail.builder().preferred(false).email("<EMAIL>").status(EmailStatus.VERIFICATION_SENT).usrId(USR_ID).build());
        repository.save(new EmailVerificationKey().contactEmail(email1).key(verificationKey).expiryDate(new DateTime().minusDays(1).toDate()));

        // when
        EmailVerificationKey emailVerificationKey = repository.findByKey(verificationKey);

        // then
        assertThat(emailVerificationKey.getKey()).isEqualTo(verificationKey);
    }

    @Test
    public void findByEmptyKeyReturnsNothing() {
        // given
        String verificationKey = "ab23";
        ContactEmail email1 = contactEmailRepository.save(ContactEmail.builder().preferred(false).email("<EMAIL>").status(EmailStatus.VERIFICATION_SENT).usrId(USR_ID).build());
        repository.save(new EmailVerificationKey().contactEmail(email1).key(verificationKey).expiryDate(new DateTime().minusDays(1).toDate()));

        // when
        EmailVerificationKey emailVerificationKey = repository.findByKey("");

        // then
        assertThat(emailVerificationKey).isNull();
    }

    @Test(expected = DataIntegrityViolationException.class)
    public void verificationKeyMustBeUnique() {
        String verKey = "ab23";

        // given
        ContactEmail email1 = contactEmailRepository.save(ContactEmail.builder().preferred(false).email("<EMAIL>").status(EmailStatus.VERIFICATION_SENT).usrId(USR_ID).build());
        ContactEmail email2 = contactEmailRepository.save(ContactEmail.builder().preferred(false).email("<EMAIL>").status(EmailStatus.VERIFICATION_SENT).usrId(USR_ID).build());
        repository.save(new EmailVerificationKey().contactEmail(email1).key(verKey).expiryDate(new DateTime().minusDays(1).toDate()));

        // when
        repository.save(new EmailVerificationKey().contactEmail(email2).key(verKey).expiryDate(new DateTime().minusDays(1).toDate()));
    }

    @Test
    public void multipleVerificationKeysForSameEmail() {
        // given
        ContactEmail email1 = contactEmailRepository.save(ContactEmail.builder().preferred(false).email("<EMAIL>").status(EmailStatus.VERIFICATION_SENT).usrId(USR_ID).build());

        // when
        repository.save(new EmailVerificationKey().contactEmail(email1).key("ab23").expiryDate(new DateTime().minusDays(1).toDate()));
        repository.save(new EmailVerificationKey().contactEmail(email1).key("cd42").expiryDate(new DateTime().minusDays(1).toDate()));

        // then
        assertThat(repository.findByKey("ab23")).isNotNull();
        assertThat(repository.findByKey("cd42")).isNotNull();
    }

    @Test
    public void differentKeyForDifferentEmail() {
        // given
        ContactEmail email1 = contactEmailRepository.save(ContactEmail.builder().preferred(false).email("<EMAIL>").status(EmailStatus.VERIFICATION_SENT).usrId(USR_ID).build());
        ContactEmail email2 = contactEmailRepository.save(ContactEmail.builder().preferred(false).email("<EMAIL>").status(EmailStatus.VERIFICATION_SENT).usrId(USR_ID).build());


        // when
        repository.save(new EmailVerificationKey().contactEmail(email1).key("ab23").expiryDate(new DateTime().minusDays(1).toDate()));
        repository.save(new EmailVerificationKey().contactEmail(email2).key("cd42").expiryDate(new DateTime().minusDays(1).toDate()));

        // then
        assertThat(repository.findByKey("ab23")).isNotNull();
        assertThat(repository.findByKey("cd42")).isNotNull();
    }

    @Test
    public void shouldGetVerificationKeyByEmailAndUserId() {
        String verKey = "ab23";

        // given
        ContactEmail email = contactEmailRepository.save(ContactEmail.builder().preferred(false).email("<EMAIL>").status(EmailStatus.VERIFICATION_SENT).usrId(USR_ID).build());
        EmailVerificationKey existingVerKey = repository.save(new EmailVerificationKey().contactEmail(email).key(verKey).expiryDate(new DateTime().minusDays(1).toDate()));

        // when
        EmailVerificationKey verificationKey = repository.findByEmailAndUserId("<EMAIL>", USR_ID);

        // then
        assertThat(verificationKey.getId()).isEqualTo(existingVerKey.getId());
    }
}
