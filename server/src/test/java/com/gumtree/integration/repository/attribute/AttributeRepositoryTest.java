package com.gumtree.integration.repository.attribute;

import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.repository.attribute.AttributeRepository;
import com.gumtree.test.DataSetLocation;
import org.fest.assertions.api.Assertions;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/attributes.xml", dataSourceName="sellerDatasource")
public class AttributeRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private AttributeRepository attributeRepository;

    @Test
    public void shouldBeATotalOf21Attributes() {
        assertThat(attributeRepository.count(), equalTo(22L));
    }

    @Test
    public void existsForNonExistantShouldReturnFalse() {
        assertThat(attributeRepository.exists(23L), equalTo(false));
    }

    @Test
    public void existsForExistingShouldReturnTrue() {
        assertThat(attributeRepository.exists(1L), equalTo(true));
    }

    @Test
    public void findByAttributeNameReturnsCorrectAttribute() {
        Assertions.assertThat(attributeRepository.findByName(CategoryConstants.Attribute.VEHICLE_MAKE.name())).isNotNull();
        Assertions.assertThat(attributeRepository.findByName(CategoryConstants.Attribute.VEHICLE_MAKE.getName())).isNull();
    }
}
