package com.gumtree.integration.repository.advert;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.repository.advert.AdvertFeatureRepository;
import com.gumtree.test.DataSetLocation;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;


@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/advert-features.xml", dataSourceName="sellerDatasource")
public class AdvertFeatureRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private AdvertFeatureRepository advertFeatureRepository;

    @Test
    public void findByAdvertIdReturnsCorrectCount() {
        assertThat(advertFeatureRepository.findByAdvertId(111L).size(), equalTo(4));
    }

    @Test
    public void findExpiredByAdvertIdReturnsCorrectCount() {
        assertThat(advertFeatureRepository.findByAdvertIdAndExpiryDateLessThan(111L, new Date()).size(),
                equalTo(2));
    }

    @Test
    public void findExpiredByAdvertIdWouldHaveReturnedZeroAFewYearsAgo() {
        Date date = new DateTime(2009, 12, 12, 12, 12, 12, 12).toDate();
        assertThat(advertFeatureRepository.findByAdvertIdAndExpiryDateLessThan(111L, date).size(),
                equalTo(0));
    }
}
