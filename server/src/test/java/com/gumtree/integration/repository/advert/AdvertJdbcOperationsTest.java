package com.gumtree.integration.repository.advert;

import com.gumtree.api.AdvertSummary;
import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.repository.advert.AdvertJdbcOperations;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;

@DataSetLocation(
        value = "classpath:/com/gumtree/seller/service/advert/advert-service-test-dataset.xml",
        dataSourceName = "sellerDatasource")
public class AdvertJdbcOperationsTest extends BaseRepositoryTest {

    @Autowired
    private AdvertJdbcOperations advertJdbcOperations;

    @Test
    public void findAdvertByIdWithImageAndPrice() {
        long advertId = 1L;
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(advertId);
        assertThat(advertSummaryOptional.isPresent(), is(true));
        AdvertSummary advertSummary = advertSummaryOptional.get();
        assertThat(advertSummary.getAccountId(), is(1L));
        assertThat(advertSummary.getCategoryId(), is(1L));
        assertThat(advertSummary.getImageUrl(), is("https://i.ebayimg.com/00/s/1/$_86.PNG"));
        assertThat(advertSummary.getContactEmail(), is("<EMAIL>"));
        assertThat(advertSummary.getStatus(), is(AdvertStatus.LIVE.name()));
        assertThat(advertSummary.getId(), is(advertId));
    }

    @Test
    public void findAdvertByIdNoImageNoPrice() {
        long advertId = 2L;
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(advertId);
        assertThat(advertSummaryOptional.isPresent(), is(true));
        AdvertSummary advertSummary = advertSummaryOptional.get();
        assertThat(advertSummary.getAccountId(), is(1L));
        assertThat(advertSummary.getCategoryId(), is(1L));
        assertThat(advertSummary.getImageUrl(), is(nullValue()));
        assertThat(advertSummary.getContactEmail(), is("<EMAIL>"));
        assertThat(advertSummary.getStatus(), is(AdvertStatus.AWAITING_SCREENING.name()));
        assertThat(advertSummary.getId(), is(advertId));
    }

    @Test
    public void findAdvertByIdWithMainAdvertImageButMissingImageRecordAndNoOtherImage() {
        long advertId = 300L;
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(advertId);
        assertThat(advertSummaryOptional.isPresent(), is(true));
        AdvertSummary advertSummary = advertSummaryOptional.get();
        assertThat(advertSummary.getAccountId(), is(1L));
        assertThat(advertSummary.getCategoryId(), is(1L));
        assertThat(advertSummary.getImageUrl(), is(nullValue()));
        assertThat(advertSummary.getContactEmail(), is("<EMAIL>"));
        assertThat(advertSummary.getStatus(), is(AdvertStatus.LIVE.name()));
        assertThat(advertSummary.getId(), is(advertId));
    }

    @Test
    public void findAdvertByIdWithMainAdvertImageButMissingImageRecordAndExistingOtherImage() {
        long advertId = 301L;
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(advertId);
        assertThat(advertSummaryOptional.isPresent(), is(true));
        AdvertSummary advertSummary = advertSummaryOptional.get();
        assertThat(advertSummary.getAccountId(), is(1L));
        assertThat(advertSummary.getCategoryId(), is(1L));
        assertThat(advertSummary.getImageUrl(), is("https://imagedelivery.net/abc/2/1"));
        assertThat(advertSummary.getContactEmail(), is("<EMAIL>"));
        assertThat(advertSummary.getStatus(), is(AdvertStatus.LIVE.name()));
        assertThat(advertSummary.getId(), is(advertId));
    }

    @Test
    public void findAdvertByIdWithMainAdvertImageButMissingImageRecordAndExistingMultipleOtherImages() {
        long advertId = 302L;
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(advertId);
        assertThat(advertSummaryOptional.isPresent(), is(true));
        AdvertSummary advertSummary = advertSummaryOptional.get();
        assertThat(advertSummary.getAccountId(), is(1L));
        assertThat(advertSummary.getCategoryId(), is(1L));
        assertThat(advertSummary.getImageUrl(), is("https://imagedelivery.net/abc/2/1"));
        assertThat(advertSummary.getContactEmail(), is("<EMAIL>"));
        assertThat(advertSummary.getStatus(), is(AdvertStatus.LIVE.name()));
        assertThat(advertSummary.getId(), is(advertId));
    }

    @Test
    public void findAdvertByIdWithMainAdvertImageEPS() {
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(401L);
        assertThat(advertSummaryOptional.get().getImageUrl(), is("https://i.ebayimg.com/00/s/3/$_86.PNG"));
    }

    @Test
    public void findAdvertByIdWithMainAdvertImageCF() {
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(402L);
        assertThat(advertSummaryOptional.get().getImageUrl(), is("https://imagedelivery.net/abc/4/1"));
    }

    @Test
    public void emptyWhenAdvertDoesntExist() {
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(101L);
        assertThat(advertSummaryOptional.isPresent(), is(false));
    }

    @Test
    public void emptyWhenIdIsNull() {
        Optional<AdvertSummary> advertSummaryOptional = advertJdbcOperations.id(null);
        assertThat(advertSummaryOptional.isPresent(), is(false));
    }
    @Test
    public void listOfAdvertsSuccessfullyReturned() {
        List<AdvertSummary> adverts = advertJdbcOperations.idsIn(Arrays.asList(1L,2L,3L,4L,5L,1000L));
        assertThat(adverts.size(), is(5));
    }

    @Test
    public void emptyListWhenNoIds() {
        List<AdvertSummary> adverts = advertJdbcOperations.idsIn(Collections.emptyList());
        assertThat(adverts.isEmpty(), is(true));
    }

    @Test
    public void emptyListWhenNullIds() {
        List<AdvertSummary> adverts = advertJdbcOperations.idsIn(null);
        assertThat(adverts.isEmpty(), is(true));
    }

    @Test
    public void batchUpdateVersionAndLastModified_shouldUpdateRows() {
        List<Object[]> params = Arrays.asList(
                new Object[]{new java.util.Date(), 1L},
                new Object[]{new java.util.Date(), 2L}
        );
        int[] result = advertJdbcOperations.batchUpdateVersionAndLastModified(params);
        assertThat(result.length, is(2));
    }

    @Test
    public void batchUpdateVersionAndLastModified_emptyParams() {
        int[] result = advertJdbcOperations.batchUpdateVersionAndLastModified(Collections.emptyList());
        assertThat(result.length, is(0));
    }

    @Test
    public void batchUpdateVersionOnly_shouldUpdateRows() {
        List<Object[]> params = Arrays.asList(
                new Object[]{1L},
                new Object[]{2L}
        );
        int[] result = advertJdbcOperations.batchUpdateVersionOnly(params);
        assertThat(result.length, is(2));
    }

    @Test
    public void batchUpdateVersionOnly_emptyParams() {
        int[] result = advertJdbcOperations.batchUpdateVersionOnly(Collections.emptyList());
        assertThat(result.length, is(0));
    }

    @Test
    public void findIdVersionDateIn_shouldReturnCorrectData() {
        List<Long> ids = Arrays.asList(1L, 2L);
        List<AdvertJdbcOperations.AdvertIdVersionDate> result = advertJdbcOperations.findIdVersionDateIn(ids);
        assertThat(result.size(), is(2));
        assertThat(result.get(0).getId(), is(1L));
        assertThat(result.get(1).getId(), is(2L));
    }

    @Test
    public void findIdVersionDateIn_emptyOrNull() {
        assertThat(advertJdbcOperations.findIdVersionDateIn(Collections.emptyList()).isEmpty(), is(true));
        assertThat(advertJdbcOperations.findIdVersionDateIn(null).isEmpty(), is(true));
    }
}
