package com.gumtree.integration.repository.advert;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.repository.advert.DuplicatedAdvertRepository;
import org.dbunit.Assertion;
import org.dbunit.DataSourceDatabaseTester;
import org.dbunit.dataset.ITable;
import org.dbunit.dataset.xml.FlatXmlDataSetBuilder;
import org.joda.time.LocalDateTime;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.Resource;

import javax.sql.DataSource;

public class DuplicatedAdvertRepositoryTest extends BaseRepositoryTest implements ApplicationContextAware {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private DuplicatedAdvertRepository duplicatedAdvertRepository;

    private DataSourceDatabaseTester tester;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Before
    public void setUp() throws Exception {
        tester = new DataSourceDatabaseTester(dataSource);
    }

    @Test
    public void shouldSaveDuplicatedAdvert() throws Exception {
        // given
        Resource resource = applicationContext.getResource("classpath:/com/gumtree/seller/repository/advert/duplicated-advert-repository-test-dataset.xml");
        ITable expectedTable = new FlatXmlDataSetBuilder().build(resource.getInputStream()).getTable("duplicated_advert");

        // when
        duplicatedAdvertRepository.save(new LocalDateTime(2015, 6, 26, 13, 10).toDate(), 10001L, 10000456L, 10000455L);

        // then
        ITable actualTable = tester.getConnection().createDataSet().getTable("duplicated_advert");
        Assertion.assertEquals(expectedTable, actualTable);
    }
}