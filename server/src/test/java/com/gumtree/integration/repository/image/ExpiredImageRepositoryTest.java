package com.gumtree.integration.repository.image;

import com.google.common.collect.Lists;
import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.repository.image.ImageRepository;
import com.gumtree.test.DataSetLocation;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import static org.fest.assertions.api.Assertions.assertThat;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/expired-images.xml", dataSourceName = "sellerDatasource")
public class ExpiredImageRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private ImageRepository repository;

    /*
     * ---------  getExpiredImageCandidates by IDs --------
     */

    @Test
    public void ignoreImagesNotDeletedNorExpiredAdsByIds() {
        // when
        List<Image> images = repository.getExpiredImageCandidates(1L, 2L, 10);

        // then
        assertThat(images).isEmpty();
    }

    @Test
    public void ignoreImagesIfDontMatchCriteriaFilterByIds() {
        // when
        List<Image> images = repository.getExpiredImageCandidates(12L, 15L, 10);

        // then
        assertThat(images).isEmpty();
    }

    @Test
    public void returnsOKForExpiredAdByIds() {
        // when
        List<Image> images = repository.getExpiredImageCandidates(6L, 7L, 10);

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(image(6)));
    }


    @Test
    public void returnsOKForDeletedUserAdByIds() {
        // when
        List<Image> images = repository.getExpiredImageCandidates(2L, 3L, 10);

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(image(2)));
    }

    @Test
    public void returnsOKForDeletedCSAdByIds() {
        // when
        List<Image> images = repository.getExpiredImageCandidates(4L, 5L, 10);

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(image(4)));
    }

    @Test
    public void returnsOKWithLimitByIds() {
        // when
        List<Image> images = repository.getExpiredImageCandidates(1L, 12L, 1);

        // then
        assertThat(images).hasSize(1);
    }

    /*
     * ---------  getExpiredImageCandidates by Dates --------
     */

    @Test
    public void ignoreImagesNotDeletedNorExpiredAdsByDates() {
        // given
        Date start = dateFromString("2022-12-01 10:22:35");
        Date end = dateFromString("2022-12-02 10:22:35");

        // when
        List<Image> images = repository.getExpiredImageCandidates(start, end, minus14Days(start), minus14Days(end), 10);

        // then
        assertThat(images).isEmpty();
    }

    @Test
    public void returnImagesIfDontMatchCriteriaFilterByDates() {
        // given
        Date start = dateFromString("2022-12-13 10:22:35");
        Date end = dateFromString("2022-12-15 10:22:35");

        // when
        List<Image> images = repository.getExpiredImageCandidates(start, end, minus14Days(start), minus14Days(end), 10);

        // then
        assertThat(images).isEmpty();
    }

    @Test
    public void returnsOKForExpiredAdByDates() {
        // given
        Date start = dateFromString("2014-02-14 10:22:35");
        Date end = dateFromString("2014-02-16 10:22:35");

        // when
        List<Image> images = repository.getExpiredImageCandidates(start, end, minus14Days(start), minus14Days(end), 10);

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(image(9), image(8)));
    }

    @Test
    public void returnsOKForDeletedUserAdByDates() {
        // given
        Date start = dateFromString("2014-02-06 10:22:35");
        Date end = dateFromString("2014-02-08 10:22:35");

        // when
        List<Image> images = repository.getExpiredImageCandidates(start, end, minus14Days(start), minus14Days(end), 10);

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(image(3), image(2)));
    }

    @Test
    public void returnsOKForDeletedCSAdByDates() {
        // given
        Date start = dateFromString("2014-02-11 10:22:35");
        Date end = dateFromString("2014-02-13 10:22:35");

        // when
        List<Image> images = repository.getExpiredImageCandidates(start, end, minus14Days(start), minus14Days(end), 10);

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(image(5), image(4)));
    }

    @Test
    public void returnsOKForExpiredDraftAdByDates() {
        // given
        Date start = dateFromString("2014-01-15 10:22:35");
        Date end = dateFromString("2014-01-17 10:22:35");

        // when
        List<Image> images = repository.getExpiredImageCandidates(start, end, minus14Days(start), minus14Days(end), 10);

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(image(7), image(6)));
    }

    @Test
    public void returnsOKForImagesCreatedWithinDates() {
        // given
        Date start = dateFromString("2022-12-11 09:22:35");
        Date end = dateFromString("2022-12-12 15:22:35");

        // when
        List<Image> images = repository.getExpiredImageCandidates(start, end, minus14Days(start), minus14Days(end), 10);

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(image(12), image(11)));
    }

    @Test
    public void returnsOKWithLimitByDates() {
        // given
        Date start = dateFromString("2022-12-01 10:22:35");
        Date end = dateFromString("2022-12-12 12:22:35");

        // when
        List<Image> images = repository.getExpiredImageCandidates(start, end, minus14Days(start), minus14Days(end), 1);

        // then
        assertThat(images).hasSize(1);
    }

    private static Image image(int id) {
        String formatted = String.format("%2d", id).replace(' ', '0');
        return constructImage((long) id, "https://imagedelivery.net/abc/" + id + "/1",
                "2022-12-" + formatted + " 11:22:35",
                "2022-12-" + formatted + " 10:22:35");
    }

    private static Image constructImage(Long id, String url, String uploadDate, String createdDate) {
        Image image = new Image(url, dateFromString(uploadDate), "UPLOADED");
        image.setId(id);
        image.setCreatedDate(dateFromString(createdDate));
        return image;
    }

    private static Date dateFromString(String date) {
        try {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            df.setTimeZone(TimeZone.getTimeZone(ZoneId.systemDefault()));
            return new Timestamp(df.parse(date).getTime());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private static Date minus14Days(Date date) {
        return new DateTime(date.getTime()).minusDays(14).toDate();
    }
}
