package com.gumtree.integration.repository.user;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.user.entity.UserAdPreference;
import com.gumtree.seller.domain.user.entity.UserAdPreference.ConsentValue;
import com.gumtree.seller.repository.user.UserAdPreferenceRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/user-ad-preference.xml", dataSourceName="sellerDatasource")
public class UserUserAdPreferenceRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private UserAdPreferenceRepository userAdPreferenceRepository;

    @Test
    @Transactional(readOnly = true)
    public void getUserConsentWithValueNo() {
        UserAdPreference userAdPreference = userAdPreferenceRepository.getOne(1L);
        assertThat(userAdPreference, notNullValue());
        assertThat(userAdPreference.getConsent(), equalTo(UserAdPreference.ConsentValue.NO));
    }

    @Test
    @Transactional(readOnly = true)
    public void getUserConsentWithValueYes() {
        UserAdPreference userAdPreference = userAdPreferenceRepository.getOne(2L);
        assertThat(userAdPreference, notNullValue());
        assertThat(userAdPreference.getConsent(), equalTo(UserAdPreference.ConsentValue.YES));
    }

    @Test
    @Transactional(readOnly = true)
    public void getUserConsentWithValueNotSpecified() {
        UserAdPreference userAdPreference = userAdPreferenceRepository.getOne(3L);
        assertThat(userAdPreference, notNullValue());
        assertThat(userAdPreference.getConsent(), equalTo(UserAdPreference.ConsentValue.IMPLICIT));
    }

    @Test
    @Transactional(readOnly = true)
    public void getMissingUserConsent() {
        UserAdPreference userAdPreference = userAdPreferenceRepository.findOne(123L);
        assertThat(userAdPreference, nullValue());
    }

    @Test
    public void setValueOfExistingConsent() {
        UserAdPreference userAdPreference = new UserAdPreference(4L);
        userAdPreference.setConsent(ConsentValue.YES);
        userAdPreferenceRepository.save(userAdPreference);

        UserAdPreference userAdPreferenceControl = userAdPreferenceRepository.findOne(4L);
        assertThat(userAdPreferenceControl.getConsent(), equalTo(UserAdPreference.ConsentValue.YES));
    }

    @Test
    public void setValueOfNonExistingConsent() {
        UserAdPreference userAdPreference = new UserAdPreference(987L);
        userAdPreference.setConsent(ConsentValue.YES);
        userAdPreferenceRepository.save(userAdPreference);

        UserAdPreference userAdPreferenceControl = userAdPreferenceRepository.findOne(987L);
        assertThat(userAdPreferenceControl.getConsent(), equalTo(UserAdPreference.ConsentValue.YES));
    }
}
