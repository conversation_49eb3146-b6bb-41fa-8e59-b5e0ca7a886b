package com.gumtree.integration.repository.advert;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.repository.advert.AdvertExternalReferenceRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/advert-external-references.xml", dataSourceName="sellerDatasource")
public class AdvertExternalReferenceRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private AdvertExternalReferenceRepository externalReferenceRepository;

    @Test
    public void shouldBeATotalOfTwoExternalReferences() {
        assertThat(externalReferenceRepository.count(), equalTo(2L));
    }

    @Test
    public void findByExternalRefAndAccountIdAndUserIdShouldReturnExternalReference() {
        assertThat(externalReferenceRepository.findByExternalRefAndAccountIdAndUserId("foo",1L,1L).getExternalRef(), equalTo("foo"));
    }

    @Test
    public void findByUnknownExternalRefAndAccountIdAndUserIdShouldReturnNull() {
        assertThat(externalReferenceRepository.findByExternalRefAndAccountIdAndUserId("bloofoo", 1L, 1L), equalTo(null));
    }
}
