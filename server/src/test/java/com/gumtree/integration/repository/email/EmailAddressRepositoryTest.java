package com.gumtree.integration.repository.email;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.repository.email.EmailAddressRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.fest.assertions.api.Assertions.assertThat;

/**
 *
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/email-address.xml", dataSourceName = "sellerDatasource")
public class EmailAddressRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private EmailAddressRepository repository;


    @Test
    public void verifyFindByEmailReturnsRecognisedEmail() {
        // when
        EmailAddress email = repository.findByEmail("<EMAIL>");

        // then
        assertThat(email.getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    @Transactional
    public void shouldDeleteByEmailAddress() {
        // given
        EmailAddress email1 = repository.findByEmail("<EMAIL>");
        EmailAddress email2 = repository.findByEmail("<EMAIL>");
        assertThat(email1.getEmail()).isEqualTo("<EMAIL>");
        assertThat(email2.getEmail()).isEqualTo("<EMAIL>");

        // when
        repository.delete(email1);

        // then
        email1 = repository.findByEmail("<EMAIL>");
        email2 = repository.findByEmail("<EMAIL>");
        assertThat(email1).isNull();
        assertThat(email2.getEmail()).isEqualTo("<EMAIL>");
    }

}
