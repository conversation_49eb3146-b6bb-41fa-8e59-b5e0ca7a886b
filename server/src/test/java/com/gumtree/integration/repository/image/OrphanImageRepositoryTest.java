package com.gumtree.integration.repository.image;

import com.google.common.collect.Lists;
import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.repository.image.ImageRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import static org.fest.assertions.api.Assertions.assertThat;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/orphan-images.xml", dataSourceName = "sellerDatasource")
public class OrphanImageRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private ImageRepository repository;

    /*
     * ---------  getOrphanImages by IDs --------
     */

    @Test
    public void ignoreImagesIfConnectedToAdvertByIds() {
        // when
        List<Image> images = repository.getOrphanImages(3L, 5L, 10);
        List<Image> imagesWithExclude = repository.getOrphanImages(3L, 5L, 10, Collections.singletonList("https://imagedelivery.net/abc/8/1"));

        // then
        assertThat(images).isEmpty();
        assertThat(imagesWithExclude).isEmpty();
    }

    @Test
    public void ignoreImagesIfDontMatchCriteriaFilterByIds() {
        // when
        List<Image> images = repository.getOrphanImages(10L, 12L, 10);
        List<Image> imagesWithExclude = repository.getOrphanImages(10L, 12L, 10, Collections.singletonList("https://imagedelivery.net/abc/8/1"));

        // then
        assertThat(images).isEmpty();
        assertThat(imagesWithExclude).isEmpty();
    }

    @Test
    public void returnsOKWithCriteriaByIds() {
        // when
        long startId = 1L;
        long endId = 9L;

        List<Image> images = repository.getOrphanImages(startId, endId, 10);
        List<Image> imagesWithExclude = repository.getOrphanImages(startId, endId, 10, Collections.singletonList("https://imagedelivery.net/abc/8/1"));
        List<Image> imagesWithExcludeNotMatching = repository.getOrphanImages(startId, endId, 10, Collections.singletonList("https://imagedelivery.net/abc/11"));

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(
                constructImage(8L, "https://imagedelivery.net/abc/8/1", "2022-12-08 11:22:35", "UPLOADED", "2022-12-08 10:22:35"),
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35"),
                constructImage(5L, "https://imagedelivery.net/abc/5/1", "2022-12-05 11:22:35", "UPLOADED", "2022-12-05 10:22:35"),
                constructImage(2L, "https://imagedelivery.net/abc/2/1", "2022-12-02 11:22:36", "UPLOADED", "2022-12-02 10:22:35")
        ));
        assertThat(imagesWithExclude).isEqualTo(Lists.newArrayList(
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35"),
                constructImage(5L, "https://imagedelivery.net/abc/5/1", "2022-12-05 11:22:35", "UPLOADED", "2022-12-05 10:22:35"),
                constructImage(2L, "https://imagedelivery.net/abc/2/1", "2022-12-02 11:22:36", "UPLOADED", "2022-12-02 10:22:35")
        ));
        assertThat(imagesWithExcludeNotMatching).isEqualTo(Lists.newArrayList(
                constructImage(8L, "https://imagedelivery.net/abc/8/1", "2022-12-08 11:22:35", "UPLOADED", "2022-12-08 10:22:35"),
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35"),
                constructImage(5L, "https://imagedelivery.net/abc/5/1", "2022-12-05 11:22:35", "UPLOADED", "2022-12-05 10:22:35"),
                constructImage(2L, "https://imagedelivery.net/abc/2/1", "2022-12-02 11:22:36", "UPLOADED", "2022-12-02 10:22:35")
        ));
    }

    @Test
    public void returnsOKWithLimitByIds() {
        // when
        List<Image> images = repository.getOrphanImages(1L, 7L, 1);
        List<Image> imagesWithExclude = repository.getOrphanImages(1L, 7L, 1, Collections.singletonList("https://imagedelivery.net/abc/8/1"));

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35")
        ));
        assertThat(imagesWithExclude).isEqualTo(Lists.newArrayList(
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35")
        ));
    }

    /*
     * ---------  getOrphanImages by Dates --------
     */

    @Test
    public void ignoreImagesIfConnectedToAdvertByDates() {
        // when
        List<Image> images = repository.getOrphanImages(dateFromString("2022-12-03 10:22:35"), dateFromString("2022-12-05 10:22:35"), 10);
        List<Image> imagesWithExclude = repository.getOrphanImages(dateFromString("2022-12-03 10:22:35"), dateFromString("2022-12-05 10:22:35"), 10, Collections.singletonList("https://imagedelivery.net/abc/8/1"));

        // then
        assertThat(images).isEmpty();
        assertThat(imagesWithExclude).isEmpty();
    }

    @Test
    public void ignoreImagesIfDontMatchCriteriaFilterByDates() {
        // when
        List<Image> images = repository.getOrphanImages(dateFromString("2022-12-10 10:22:35"), dateFromString("2022-12-12 10:22:35"), 10);
        List<Image> imagesWithExclude = repository.getOrphanImages(dateFromString("2022-12-10 10:22:35"), dateFromString("2022-12-12 10:22:35"), 10, Collections.singletonList("https://imagedelivery.net/abc/8/1"));

        // then
        assertThat(images).isEmpty();
        assertThat(imagesWithExclude).isEmpty();
    }

    @Test
    public void returnsOKWithCriteriaByDates() {
        // when
        String startDate = "2022-12-01 10:22:35";
        String endDate = "2022-12-08 10:22:36";

        List<Image> images = repository.getOrphanImages(dateFromString(startDate), dateFromString(endDate), 10);
        List<Image> imagesWithExclude = repository.getOrphanImages(dateFromString(startDate), dateFromString(endDate), 10, Collections.singletonList("https://imagedelivery.net/abc/8/1"));
        List<Image> imagesWithExcludeNotMatching = repository.getOrphanImages(dateFromString(startDate), dateFromString(endDate), 10, Collections.singletonList("https://imagedelivery.net/abc/11/1"));

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(
                constructImage(8L, "https://imagedelivery.net/abc/8/1", "2022-12-08 11:22:35", "UPLOADED", "2022-12-08 10:22:35"),
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35"),
                constructImage(5L, "https://imagedelivery.net/abc/5/1", "2022-12-05 11:22:35", "UPLOADED", "2022-12-05 10:22:35"),
                constructImage(2L, "https://imagedelivery.net/abc/2/1", "2022-12-02 11:22:36", "UPLOADED", "2022-12-02 10:22:35")
        ));
        assertThat(imagesWithExclude).isEqualTo(Lists.newArrayList(
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35"),
                constructImage(5L, "https://imagedelivery.net/abc/5/1", "2022-12-05 11:22:35", "UPLOADED", "2022-12-05 10:22:35"),
                constructImage(2L, "https://imagedelivery.net/abc/2/1", "2022-12-02 11:22:36", "UPLOADED", "2022-12-02 10:22:35")
        ));
        assertThat(imagesWithExcludeNotMatching).isEqualTo(Lists.newArrayList(
                constructImage(8L, "https://imagedelivery.net/abc/8/1", "2022-12-08 11:22:35", "UPLOADED", "2022-12-08 10:22:35"),
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35"),
                constructImage(5L, "https://imagedelivery.net/abc/5/1", "2022-12-05 11:22:35", "UPLOADED", "2022-12-05 10:22:35"),
                constructImage(2L, "https://imagedelivery.net/abc/2/1", "2022-12-02 11:22:36", "UPLOADED", "2022-12-02 10:22:35")
        ));
    }

    @Test
    public void returnsOKWithLimitByDates() {
        // when
        List<Image> images = repository.getOrphanImages(dateFromString("2022-12-01 10:22:35"), dateFromString("2022-12-07 10:22:35"), 1);
        List<Image> imagesWithExclude = repository.getOrphanImages(dateFromString("2022-12-01 10:22:35"), dateFromString("2022-12-07 10:22:35"), 1, Collections.singletonList("https://imagedelivery.net/abc/8/1"));
        List<Image> imagesWithExcludeNotMatching = repository.getOrphanImages(dateFromString("2022-12-01 10:22:35"), dateFromString("2022-12-07 10:22:35"), 1, Collections.singletonList("https://imagedelivery.net/abc/11/1"));

        // then
        assertThat(images).isEqualTo(Lists.newArrayList(
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35")
        ));
        assertThat(imagesWithExclude).isEqualTo(Lists.newArrayList(
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35")
        ));
        assertThat(imagesWithExcludeNotMatching).isEqualTo(Lists.newArrayList(
                constructImage(6L, "https://imagedelivery.net/abc/6/1", "2022-12-06 11:22:35", "PROCESSING", "2022-12-06 10:22:35")
        ));
    }

    private static Image constructImage(Long id, String url, String uploadDate, String status, String createdDate) {
        Image image = new Image(url, dateFromString(uploadDate), status);
        image.setId(id);
        image.setCreatedDate(dateFromString(createdDate));
        return image;
    }

    private static Date dateFromString(String date) {
        try {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            df.setTimeZone(TimeZone.getTimeZone(ZoneId.systemDefault()));
            return new Timestamp(df.parse(date).getTime());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
