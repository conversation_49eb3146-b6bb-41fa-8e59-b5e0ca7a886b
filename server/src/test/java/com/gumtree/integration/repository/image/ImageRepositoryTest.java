package com.gumtree.integration.repository.image;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.repository.image.ImageRepository;
import com.gumtree.test.DataSetLocation;
import org.hamcrest.MatcherAssert;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/images.xml", dataSourceName = "sellerDatasource")
public class ImageRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private ImageRepository repository;

    @Test
    public void findWithOnlyImageColumns() {
        // given
        Long id = 1L;
        String imageUrl = "https://i.ebayimg.com/00/s/1/$_86.PNG";
        Date imageDate = dateFromString("2022-12-01T11:22:35");
        String imageStatus = "UPLOADED";

        // when
        Image image = repository.findOne(id);

        // then
        MatcherAssert.assertThat(image, equalTo(constructImage(id, imageUrl, imageDate, imageStatus)));

        // and
        compareImageFields(image, id, imageUrl, imageDate, imageStatus, null, null, null);
    }

    @Test
    public void findWithImageAndMediaColumnsBothUrlsPopulated() {
        // given
        Long id = 2L;
        String imageUrl = "https://i.ebayimg.com/00/s/2/$_86.PNG";
        Date imageDate = dateFromString("2022-12-02T11:22:35");
        String imageStatus = "UPLOADED";
        String mediaUrl = "https://imagedelivery.net/abc/2/1";
        Date mediaDate = dateFromString("2022-12-02T11:22:36");
        String mediaStatus = "UPLOADED";

        // when
        Image image = repository.findOne(id);

        // then
        MatcherAssert.assertThat(image, equalTo(constructImage(id, mediaUrl, mediaDate, mediaStatus)));

        // and
        compareImageFields(image, id, imageUrl, imageDate, imageStatus, mediaUrl, mediaDate, mediaStatus);
    }

    @Test
    public void findWithImageAndMediaColumnsEPSUrlPopulated() {
        // given
        Long id = 3L;
        String imageUrl = "https://i.ebayimg.com/00/s/3/$_86.PNG";
        Date imageDate = dateFromString("2022-12-03T11:22:35");
        String imageStatus = "UPLOADED";
        String mediaUrl = "";
        Date mediaDate = dateFromString("2022-12-03T11:22:36");
        String mediaStatus = "UPLOADED";

        // when
        Image image = repository.findOne(id);

        // then
        MatcherAssert.assertThat(image, equalTo(constructImage(id, imageUrl, imageDate, imageStatus)));

        // and
        compareImageFields(image, id, imageUrl, imageDate, imageStatus, mediaUrl, mediaDate, mediaStatus);
    }

    @Test
    public void findWithImageAndMediaColumnsCFUrlPopulated() {
        // given
        Long id = 4L;
        String imageUrl = "";
        Date imageDate = dateFromString("2022-12-04T11:22:35");
        String imageStatus = "PROCESSING";
        String mediaUrl = "https://imagedelivery.net/abc/4/1";
        Date mediaDate = dateFromString("2022-12-04T11:22:36");
        String mediaStatus = "PROCESSING";

        // when
        Image image = repository.findOne(id);

        // then
        MatcherAssert.assertThat(image, equalTo(constructImage(id, mediaUrl, mediaDate, mediaStatus)));

        // and
        compareImageFields(image, id, imageUrl, imageDate, imageStatus, mediaUrl, mediaDate, mediaStatus);
    }

    @Transactional
    @Test
    public void persistNewImageWithEpsUrl() {
        // given
        repository.deleteAll();

        String url = "https://i.ebayimg.com/00/s/ODAwWDYwMA==/z/JeMAAOSwL-5hgWHv/$_86.PNG";
        Date date = new Timestamp(DateTime.parse("2022-12-01T11:22:36.000Z").getMillis());
        String status = "PROCESSING";
        Image image = new Image(url, date, status);

        // when
        Image persisted = repository.save(image);

        // then
        Image found = repository.getOne(persisted.getId());
        compareImageFields(found, found.getId(), url, date, status, null, date, status);

        // and
        MatcherAssert.assertThat(found.getId(), notNullValue());
        MatcherAssert.assertThat(found.getUrl(), equalTo(url));
        MatcherAssert.assertThat(found.getUploadDate(), equalTo(date));
        MatcherAssert.assertThat(found.getImageStatus(), equalTo(status));
    }

    @Transactional
    @Test
    public void persistNewImageWithCFUrl() {
        // given
        repository.deleteAll();

        String url = "https://imagedelivery.net/gt/abc/1";
        Date date = new Timestamp(DateTime.parse("2022-12-01T11:22:35.000Z").getMillis());
        String status = "UPLOADED";
        Image image = new Image(url, date, status);

        // when
        Image persisted = repository.save(image);

        // then
        Image found = repository.getOne(persisted.getId());

        compareImageFields(found, found.getId(), "", date, status, url, date, status);

        // and
        MatcherAssert.assertThat(found.getId(), notNullValue());
        MatcherAssert.assertThat(found.getUrl(), equalTo(url));
        MatcherAssert.assertThat(found.getUploadDate(), equalTo(date));
        MatcherAssert.assertThat(found.getImageStatus(), equalTo(status));
    }

    @Transactional
    @Test
    public void updateEpsToCFUrlShouldReturnCFUrl() {
        // given
        repository.deleteAll();

        String url = "https://i.ebayimg.com/00/s/abc/55/$_86.PNG";
        String mediaUrl = "https://imagedelivery.net/gt/abc/1";
        Date date = new Timestamp(DateTime.parse("2022-12-01T11:22:35.000Z").getMillis());
        String status = "UPLOADED";
        Image image = new Image(url, date, status);

        // when
        Image persisted = repository.save(image);
        persisted.setUrl(mediaUrl);

        // when
        repository.save(image);

        // then
        Image found = repository.getOne(persisted.getId());

        compareImageFields(found, found.getId(), "", date, status, mediaUrl, date, status);

        // and
        MatcherAssert.assertThat(found.getId(), notNullValue());
        MatcherAssert.assertThat(found.getUrl(), equalTo(mediaUrl));
        MatcherAssert.assertThat(found.getUploadDate(), equalTo(date));
        MatcherAssert.assertThat(found.getImageStatus(), equalTo(status));
    }

    @Transactional
    @Test
    public void updateCFToEpsUrlShouldPersistEpsUrl() {
        // given
        repository.deleteAll();

        String url = "https://i.ebayimg.com/00/s/abc/55/$_86.PNG";
        String mediaUrl = "https://imagedelivery.net/gt/abc/1";
        Date date = new Timestamp(DateTime.parse("2022-12-01T11:22:35.000Z").getMillis());
        String status = "UPLOADED";
        Image image = new Image(mediaUrl, date, status);

        // when
        Image persisted = repository.save(image);
        persisted.setUrl(url);
        repository.save(image);

        // then
        Image found = repository.findOne(persisted.getId());

        compareImageFields(found, found.getId(), url, date, status, null, date, status);

        // and
        MatcherAssert.assertThat(found.getId(), notNullValue());
        MatcherAssert.assertThat(found.getUrl(), equalTo(url));
        MatcherAssert.assertThat(found.getUploadDate(), equalTo(date));
        MatcherAssert.assertThat(found.getImageStatus(), equalTo(status));
    }

    @Transactional
    @Test
    public void deleteImage() {
        Long imageId = 1L;

        Image before = repository.findOne(imageId);
        MatcherAssert.assertThat(before, notNullValue());

        repository.deleteById(imageId);

        Image after = repository.findOne(imageId);
        MatcherAssert.assertThat(after, nullValue());
    }

    private static Image constructImage(Long id, String url, Date date, String status) {
        Image image = new Image(url, date, status);
        image.setId(id);
        return image;
    }

    private static void compareImageFields(Image image,
                                           Long id,
                                           String imageUrl, Date imageDate, String imageStatus,
                                           String mediaUrl, Date mediaDate, String mediaStatus) {
        MatcherAssert.assertThat(ReflectionTestUtils.getField(image, "id"), equalTo(id));
        MatcherAssert.assertThat(ReflectionTestUtils.getField(image, "url"), equalTo(imageUrl));
        MatcherAssert.assertThat(ReflectionTestUtils.getField(image, "uploadDate"), equalTo(imageDate));
        MatcherAssert.assertThat(ReflectionTestUtils.getField(image, "imageStatus"), equalTo(imageStatus));
        MatcherAssert.assertThat(ReflectionTestUtils.getField(image, "cloudflareUrl"), equalTo(mediaUrl));
        MatcherAssert.assertThat(ReflectionTestUtils.getField(image, "cloudflareUploadDate"), equalTo(mediaDate));
        MatcherAssert.assertThat(ReflectionTestUtils.getField(image, "cloudflareImageStatus"), equalTo(mediaStatus));
    }

    private Timestamp dateFromString(String date) {
        return new Timestamp(LocalDateTime.parse(date).atZone(ZoneId.systemDefault())
                .toInstant().toEpochMilli());
    }
}
