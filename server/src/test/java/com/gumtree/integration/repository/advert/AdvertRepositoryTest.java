package com.gumtree.integration.repository.advert;

import com.google.common.collect.Sets;
import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.repository.advert.AdvertRepository;
import com.gumtree.test.DataSetLocation;
import org.dbunit.util.Base64;
import org.joda.time.LocalDate;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static com.google.common.collect.ImmutableSet.of;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

@DataSetLocation(
        value = "classpath:/com/gumtree/seller/repository/advert/advert-repository-test-dataset.xml",
        dataSourceName = "sellerDatasource")
public class AdvertRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private AdvertRepository advertRepository;

    @Autowired
    @Qualifier("sellerDatabaseOperations")
    private JdbcTemplate jdbcTemplate;

    @Test(expected = OptimisticLockingFailureException.class)
    @Transactional
    public void updateAdvertFailsWhenVersionDiffersInDatabase() {
        Advert advert = advertRepository.findOne(1L);
         // need to change something to force dirty state
        advert.setContactEmail("<EMAIL>");
        // change version in database to simulate another user modifying the advert
        jdbcTemplate.execute("update advert set version = 3 where id = 1");
        advertRepository.save(advert);
        advertRepository.flush();
    }

    @Test
    @Transactional
    public void shouldSumAllAdvertVersions() {
        // when
        long sumOfVersions = advertRepository.getSumOfVersions();

        // then
        assertThat(sumOfVersions).isEqualTo(6L);
    }

    @Test
    public void latestUsedPostCodeShouldReturnNullWhenAccountWithoutAds() {
        String postCode = advertRepository.findLatestUsedPostcode(2L);
        assertTrue("Should return null as the account has no ads", postCode == null);
    }

    @Test
    public void latestUsedPostCodeShouldReturnTheCorrectPostCodeWhenAUserHasSomeAds(){
        String postCode = advertRepository.findLatestUsedPostcode(3L);
        assertTrue("Should return SW195AS", postCode.equals("SW195AS"));
    }

    @Test
    public void shouldCountCorrectlyLiveAdvertsForGivenAccount(){
        // for existing data
        assertThat(advertRepository.hasLiveAdvertsInCategoryForGivenAccount(1, Sets.newHashSet(1L, 2L))).isEqualTo(true);
        assertThat(advertRepository.hasLiveAdvertsInCategoryForGivenAccount(3, Sets.newHashSet(1L, 2L))).isEqualTo(true);

        // for non-existing data
        assertThat(advertRepository.hasLiveAdvertsInCategoryForGivenAccount(2, Sets.newHashSet(1L, 2L))).isEqualTo(false);
        assertThat(advertRepository.hasLiveAdvertsInCategoryForGivenAccount(3, Sets.newHashSet(2L))).isEqualTo(false);
    }

    @Test
    public void shouldCountCorrectlyAdsPostedSince(){
        // for existing data
        final LocalDate correctDate = new LocalDate(2011, 1, 1);
        final LocalDate incorrectDate = new LocalDate(2011, 1, 2);
        assertThat(advertRepository.hasAtMostAdvertsByAccountIdAndCategoriesPostedSince(3, of(1L, 2L), correctDate.toDate(), 2)).isEqualTo(2);
        assertThat(advertRepository.hasAtMostAdvertsByAccountIdAndCategoriesPostedSince(3, of(1L, 2L), correctDate.toDate(), 1)).isEqualTo(1);
        assertThat(advertRepository.hasAtMostAdvertsByAccountIdAndCategoriesPostedSince(3, of(1L, 2L), correctDate.toDate(), 0)).isEqualTo(0);
        assertThat(advertRepository.hasAtMostAdvertsByAccountIdAndCategoriesPostedSince(3, of(1L, 2L), incorrectDate.toDate(), 2)).isEqualTo(0);

        // for non-existing data
        assertThat(advertRepository.hasAtMostAdvertsByAccountIdAndCategoriesPostedSince(4, of(1L, 2L), correctDate.toDate(), 2)).isEqualTo(0);
        assertThat(advertRepository.hasAtMostAdvertsByAccountIdAndCategoriesPostedSince(3, of(2L), correctDate.toDate(), 2)).isEqualTo(0);
    }

    @Test
    public void shouldCountCorrectlyAdsCreatedSince(){
        // for existing data
        final LocalDate correctDate = new LocalDate(2011, 1, 1);
        final LocalDate incorrectDate = new LocalDate(2011, 1, 2);
        assertThat(advertRepository.countAdsCreatedSince(3, correctDate.toDate())).isEqualTo(2);
        assertThat(advertRepository.countAdsCreatedSince(3, incorrectDate.toDate())).isEqualTo(0);

        // for non-existing data
        assertThat(advertRepository.countAdsCreatedSince(4, correctDate.toDate())).isEqualTo(0);
    }

    @Test
    public void shouldReturnIdOfDuplicateAd() {
        Date date = new LocalDate(2011, 1, 1).toDate();
        byte[] fingerprint = Base64.decode("80514ce6cf308b75bef762eccf3ca2d17cbeded4");
        assertThat(advertRepository.findDuplicate(6L, fingerprint, 5L, date)).isEqualTo(4L);
    }

    @Test
    public void shouldReturnNullWhenDuplicatedAdsNotFound() {
        Date correctDate = new LocalDate(2011, 1, 1).toDate();
        Date incorrectDate = new LocalDate(2012, 1, 1).toDate();
        byte[] correctFingerprint = Base64.decode("80514ce6cf308b75bef762eccf3ca2d17cbeded4");
        byte[] incorrectFingerprint = Base64.decode("86f7e437faa5a7fce15d1ddcb9eaeaea377667b8");
        long correctAccountId = 5L;
        long incorrectAccountId = 123L;
        long correctAdvertId = 5L;
        long incorrectAdvertId = 4L;

        assertNull(advertRepository.findDuplicate(correctAdvertId, correctFingerprint, correctAccountId, incorrectDate));
        assertNull(advertRepository.findDuplicate(correctAdvertId, incorrectFingerprint, correctAccountId, correctDate));
        assertNull(advertRepository.findDuplicate(correctAdvertId, correctFingerprint, incorrectAccountId, correctDate));
        assertNull(advertRepository.findDuplicate(incorrectAdvertId, correctFingerprint, correctAccountId, correctDate));
    }
}
