package com.gumtree.integration.repository.packages;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.wallet.packages.domain.entity.PackageType;
import com.gumtree.wallet.packages.repository.PackageTypeRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/packages.xml", dataSourceName="sellerDatasource")
public class PackageTypeRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private PackageTypeRepository packageTypeRepository;

    @Test
    @Transactional
    public void canFindAPackageTypeValidId() {
        PackageType packageType = packageTypeRepository.findBySalesforceProductId("ASALESFORCEID");
        assertThat(packageType.getId(), equalTo(1l));
        assertThat(packageType.getCategories().size(), equalTo(1));
        assertThat(packageType.getCategories().get(0), equalTo(6L));
    }

    @Test
    @Transactional
    public void canFindAPackageTypeInvalidId() {
        assertThat(packageTypeRepository.findBySalesforceProductId("NOTASALESFORCEID"), equalTo(null));
    }

    @Test
    @Transactional
    public void findByDisplayName() {
        assertNotNull(packageTypeRepository.findByDisplayName("London Bookkeeping Insertion"));
    }

    @Test
    @Transactional
    public void findByUnknownDisplayName() {
        assertNull(packageTypeRepository.findByDisplayName("non-existent package_type"));
    }
}
