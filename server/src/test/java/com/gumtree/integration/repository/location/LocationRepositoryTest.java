package com.gumtree.integration.repository.location;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.repository.location.LocationRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.equalToIgnoringCase;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/locations-and-categories.xml", dataSourceName="sellerDatasource")
public class LocationRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private LocationRepository locationRepository;

    @Test
    public void shouldBeATotalOf11Locations() {
        assertThat(locationRepository.count(), equalTo(11L));
    }

    @Test
    public void existsForNonExistantShouldReturnFalse() {
        assertThat(locationRepository.exists(11L), equalTo(false));
    }

    @Test
    public void existsForExistingShouldReturnTrue() {
        assertThat(locationRepository.exists(1L), equalTo(true));
    }

    @Test
    public void findOneForExistingShouldReturnCategory() {
        assertThat(locationRepository.findOne(1L).getName(), equalToIgnoringCase("location-one"));
    }

}
