package com.gumtree.integration.repository.account;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.repository.account.AccountRepository;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.equalToIgnoringCase;
import static org.hamcrest.Matchers.notNullValue;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/accounts.xml", dataSourceName = "sellerDatasource")
public class AccountRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private AccountRepository accountRepository;

    @Test
    public void shouldBeATotalOfTwoAccounts() {
        assertThat(accountRepository.count(), equalTo(2L));
    }

    @Test
    public void existsForNonExistingShouldReturnFalse() {
        assertThat(accountRepository.exists(11L), equalTo(false));
    }

    @Test
    public void existsForExistingShouldReturnTrue() {
        assertThat(accountRepository.exists(1L), equalTo(true));
    }

    @Test
    public void findOneForExistingShouldReturnAccount() {
        assertThat(accountRepository.findOne(1L).getName(), equalToIgnoringCase("Test Account 1"));
        assertThat(accountRepository.findOne(2L).getDescription(), equalToIgnoringCase("Second test account"));
    }

    @Test
    public void findByPublicIdForExistingShouldReturnAccount() {
        Account account = accountRepository.findOne(1L);
        assertThat(account, notNullValue(Account.class));
        String publicId = account.getPublicId();
        assertThat(publicId, notNullValue());
        Account dbAccount = accountRepository.findByPublicId(publicId);
        assertThat(dbAccount, notNullValue(Account.class));
        assertThat(dbAccount.getId(), equalTo(1L));
    }

    @Test
    public void saveShouldSetPublicId() {
        Account account = Account.builder().withName("NEW TEST ACCOUNT").build();
        accountRepository.save(account);
        assertThat(account.getPublicId(), notNullValue());
        Account dbAccount = accountRepository.findByPublicId(account.getPublicId());
        assertThat(dbAccount, notNullValue(Account.class));
        assertThat(dbAccount.getId(), equalTo(account.getId()));
    }

    @Test
    public void saveShouldNotOverwriteExistingPublicId() {
        Account account = Account.builder().withName("NEW TEST ACCOUNT").build();
        account.setPublicId("some_public_id_123");
        accountRepository.save(account);
        assertThat(account.getPublicId(), equalTo("some_public_id_123"));
    }

    @Transactional
    @Test
    public void shouldReturnEpsImage() {
        assertThat(accountRepository.findOne(1L).getDefaultImage(), equalTo(constructImage(152001L, "http://domain.com/01.JPG")));
    }

    @Transactional
    @Test
    public void shouldReturnCFImage() {
        assertThat(accountRepository.findOne(2L).getDefaultImage(), equalTo(constructImage(152002L, "https://imagedelivery.net/gt/001/2")));
    }

    private static Image constructImage(Long id, String url) {
        Image image = new Image();
        image.setId(id);
        image.setUrl(url);
        return image;
    }
}
