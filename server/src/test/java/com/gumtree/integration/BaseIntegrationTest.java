package com.gumtree.integration;

import com.gumtree.api.mvc.profile.IntegrationTests;
import com.gumtree.config.BapiServerContextInitializer;
import com.gumtree.config.property.BapiServerProperty;
import com.gumtree.seller.service.advert.expiry.AdCountersService;
import com.gumtree.seller.test.BaseShiroTest;
import com.gumtree.test.CleanInsertTestExecutionListener;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;

/**
 * Base class for integration tests
 */
@ActiveProfiles({"stubbed-external-api", "category-api-stub"})
@Category({IntegrationTests.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        initializers = BapiServerContextInitializer.class,
        locations = "/com/gumtree/seller/seller-test-application-context.xml"
)
@TestExecutionListeners(
        {
                DependencyInjectionTestExecutionListener.class,
                CleanInsertTestExecutionListener.class,
                TransactionalTestExecutionListener.class
        }
)
public abstract class BaseIntegrationTest extends BaseShiroTest {

    static {
        setBaseIntegrationTestProperties();
    }

    public static void setBaseIntegrationTestProperties() {
        System.setProperty("testMode", "true");
        System.setProperty(BapiServerProperty.USER_EVENTLOG_ENABLED.getPropertyName(), "true");
        System.setProperty("gumtree.notify.async.enabled", "false");
        System.setProperty("gumtree.seller.emailSender.synchronous", "true");
        System.setProperty("hpi.service.enabled", "false");
//        System.setProperty("gumtree.brand.refresh.enabled", "true");
        System.setProperty("gumtree.static_asset.base_uri", "staticBaseURI");
        System.setProperty("gumtree.reply.revealemailcategory", "1");
        System.setProperty("gumtree.mailgun.smtp_port", "2500");
        System.setProperty("gumtree.sellertype.categories", "10201, 4610");
        System.setProperty("gumtree.sellertype.advertlimits", "2, 1");
    }
}
