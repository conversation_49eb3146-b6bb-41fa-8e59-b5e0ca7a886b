package com.gumtree.integration.service.expiry;

import com.gumtree.api.service.expiry.ArchiveDraftAdvertsServiceImpl;
import com.gumtree.common.util.time.Clock;
import com.netflix.config.ConfigurationManager;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class ArchiveDraftAdvertsServiceImplTest {

    @Test
    public void testGeneratesCorrectQuery() {
        Properties properties = new Properties();
        properties.setProperty("gumtree.seller.jobs.archive.draftdays", "1");
        ConfigurationManager.loadProperties(properties);
        DateTime now = new DateTime();
        Clock clock = mock(Clock.class);
        when(clock.getDateTime()).thenReturn(now);
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        int draftExpiryDays = 14;
        JdbcOperations jdbcOperations = mock(JdbcOperations.class);
        when(jdbcOperations.queryForList("select id from advert where status = ? and created_date <= ?",
                Long.class, "DRAFT", now.minusDays(draftExpiryDays).toDate())).thenReturn(ids);
        ArchiveDraftAdvertsServiceImpl service = new ArchiveDraftAdvertsServiceImpl();
        ReflectionTestUtils.setField(service, "clock", clock);
        ReflectionTestUtils.setField(service, "jdbcOperations", jdbcOperations);
        assertThat(service.getIdsToExpire(draftExpiryDays), equalTo(ids));
    }
}
