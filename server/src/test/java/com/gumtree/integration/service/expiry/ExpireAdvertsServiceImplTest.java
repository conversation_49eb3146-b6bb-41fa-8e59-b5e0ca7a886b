package com.gumtree.integration.service.expiry;

import com.gumtree.api.service.expiry.ExpireAdvertsServiceImpl;
import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class ExpireAdvertsServiceImplTest {

    @Test
    public void testGeneratesCorrectQuery() {
        DateTime now = new DateTime();
        Clock clock = new StoppedClock(now);
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        JdbcOperations jdbcOperations = mock(JdbcOperations.class);
        when(jdbcOperations.queryForList(
                "select id from advert where status in (?,?,?,?,?) and expiry_date <= ? limit ?",
                Long.class, "LIVE", "AWAITING_ACTIVATION", "NEEDS_EDITING", "AWAITING_SCREENING", "AWAITING_CS_REVIEW", now.toDate(), 20000L)).thenReturn(ids);
        ExpireAdvertsServiceImpl service = new ExpireAdvertsServiceImpl();
        ReflectionTestUtils.setField(service, "clock", clock);
        ReflectionTestUtils.setField(service, "jdbcOperations", jdbcOperations);
        assertThat(service.getIdsToExpire(20000L), equalTo(ids));
    }
}

