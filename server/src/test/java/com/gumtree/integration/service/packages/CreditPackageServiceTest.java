package com.gumtree.integration.service.packages;

import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.packages.CreditPackageUsage;
import com.gumtree.wallet.packages.domain.PackageRequest;
import com.gumtree.wallet.packages.domain.entity.CreditPackage;
import com.gumtree.wallet.packages.domain.exception.PackageNotFoundException;
import com.gumtree.wallet.packages.repository.CreditPackageRepository;
import com.gumtree.wallet.packages.service.CreditPackageService;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.wallet.packages.domain.PackageSearchFilter;
import com.gumtree.test.DataSetLocation;
import org.apache.shiro.authz.AuthorizationException;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;

import java.util.Date;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/packages.xml", dataSourceName="sellerDatasource")
public class CreditPackageServiceTest extends BaseIntegrationTest {

    @Autowired
    private CreditPackageService creditPackageService;

    @Autowired
    private CreditPackageRepository creditPackageRepository;

    private Clock clock;

    @Test
    public void testCreatePackage() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:1", "user1");

        PackageRequest request = createDefaultPackageRequest();
        CreditPackage creditPackage = creditPackageService.createPackage(request);
        assertThat(creditPackage.getAccount(), notNullValue());
        assertThat(creditPackage.getAdjustedCredits(), equalTo(0L));
        assertThat(creditPackage.getUsedCredits(), equalTo(0L));
        assertThat(creditPackage.getEndDate().toDate().getTime(), equalTo(convertToUTCWithOffset(request.getEndDate()).plusDays(1).toDateMidnight().toDate().getTime()));
        assertThat(creditPackage.getSalesforceId(), equalTo("anything_we_like"));
    }


    private DateTime convertToUTCWithOffset(DateTime dateTime) {
        DateTimeZone zone = dateTime.getZone();
        if (zone.equals(DateTimeZone.UTC)) {
            return dateTime;
        }

        return dateTime.toDateTime(DateTimeZone.UTC).plus(zone.getOffset(dateTime));
    }


    @Test(expected = AuthorizationException.class)
    public void createPackageThrowsExceptionOnUnauthorizedUser() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:2", "user1");
        PackageRequest request = createDefaultPackageRequest();
        CreditPackage creditPackage = creditPackageService.createPackage(request);
    }

    @Test
    public void testUpdatePackage() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:1", "user1");

        PackageRequest request = createDefaultPackageRequest();
        CreditPackage creditPackage = creditPackageService.createPackage(request);

        //test an update
        PackageRequest urequest = createDefaultPackageRequest();
        urequest.setAdjustedCredits(10L);

        CreditPackage updatePackage = creditPackageService.createPackage(urequest);
        assertThat(updatePackage.getAdjustedCredits(), equalTo(10L));

        Date updatePackageCreationDate = updatePackage.getCreationDate();
        Date creditPackageCreationDate = creditPackage.getCreationDate();
        assertThat(updatePackageCreationDate.compareTo(creditPackageCreationDate), equalTo(0));
    }

    @Test
    public void testDeletePackage() { //happy path
        setSubjectWithPermissionAndPrincipal("account:managepackages:1", "user1");

        PackageRequest request = new PackageRequest();
        request.setAdjustedCredits(null);
        request.setSalesForceId("to_be_deleted");
        request.setSalesforceProductId("ASALESFORCEID");
        request.setAccountId(1L);
        request.setStartDate(new DateTime());
        DateTime endDate = request.getStartDate().plus(10000);
        request.setEndDate(endDate);
        request.setInitialCredits(100L);

        CreditPackage creditPackage = creditPackageService.createPackage(request);
        assertThat(creditPackage != null, equalTo(true)); //package created ok
        assertThat(creditPackageRepository.findBySalesforceId(request.getSalesForceId()).getSalesforceId(),
                equalTo("to_be_deleted"));

        request.setDeleted(true);

        //now try and delete the package we have just created
        creditPackageService.deletePackage(request.getSalesForceId());

        CreditPackage deletedPackage = creditPackageRepository.findBySalesforceId(request.getSalesForceId());
        assertThat(deletedPackage, equalTo(null));
    }

    @Test(expected = PackageNotFoundException.class)
    public void testDeletePackageUnknownPackage() { //unhappy path
        setSubjectWithPermissionAndPrincipal("account:managepackages:1", "user1");

        PackageRequest request = createDefaultPackageRequest();
        assertThat(creditPackageRepository.findBySalesforceId(request.getSalesForceId()), equalTo(null));
        creditPackageService.deletePackage(request.getSalesForceId());
    }

    @Test
    public void extractsCorrectUsagesForCreditPackage100OnPageOne() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(14L, 100L, 1, 2, false);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getNumberOfElements(), equalTo(2));
        assertThat(usages.getTotalElements(), equalTo(3L));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(true));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(2));
        assertThat(usages.isFirst(), equalTo(true));

        assertPackageUsage(
                usages.getContent().get(0),
                createDateTime(1, 5),
                11L,
                "Specialist nursing job in manchester",
                "<EMAIL>",
                "<EMAIL>",
                null,
                "London Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(1),
                createDateTime(1, 3),
                9L,
                "Automatic package test ad where package has expired",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "London Jobs Insertion");
    }

    @Test
    public void extractsCorrectUsagesForCreditPackage100OnPageTwo() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(14L, 100L, 2, 2, false);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getNumberOfElements(), equalTo(1));
        assertThat(usages.getTotalElements(), equalTo(3L));
        assertThat(usages.getNumber(), equalTo(2));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(true));
        assertThat(usages.getTotalPages(), equalTo(2));
        assertThat(usages.isFirst(), equalTo(false));

        assertPackageUsage(
                usages.getContent().get(0),
                createDateTime(1, 1),
                8L,
                "Automatic package test ad for pro account",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "London Jobs Insertion");
    }

    @Test
    public void extractsCorrectUsagesForCreditPackage101OnPageOne() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(14L, 101L, 1, 2, false);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getNumberOfElements(), equalTo(2));
        assertThat(usages.getTotalElements(), equalTo(3L));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(true));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(2));
        assertThat(usages.isFirst(), equalTo(true));

        assertPackageUsage(
                usages.getContent().get(0),
                createDateTime(1, 6),
                11L,
                "Specialist nursing job in manchester",
                "<EMAIL>",
                "<EMAIL>",
                null,
                "RoUK Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(1),
                createDateTime(1, 4),
                9L,
                "Automatic package test ad where package has expired",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "RoUK Jobs Insertion");
    }

    @Test
    public void extractsCorrectUsagesForCreditPackage101OnPageTwo() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(14L, 101L, 2, 2, false);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getNumberOfElements(), equalTo(1));
        assertThat(usages.getTotalElements(), equalTo(3L));
        assertThat(usages.getNumber(), equalTo(2));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(true));
        assertThat(usages.getTotalPages(), equalTo(2));
        assertThat(usages.isFirst(), equalTo(false));

        assertPackageUsage(
                usages.getContent().get(0),
                createDateTime(1, 2),
                8L,
                "Automatic package test ad for pro account",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "RoUK Jobs Insertion");
    }

    @Test
    public void returnsEmptyPageForCreditPackage20() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:6", "user1");
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(6L, 18L, 1, 2, false);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getContent().size(), equalTo(0));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(0));
        assertThat(usages.getTotalElements(), equalTo(0L));
        assertThat(usages.hasContent(), equalTo(false));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(0));
        assertThat(usages.isFirst(), equalTo(true));
    }

    @Test
    public void doesNotReturnCapabilityUsageForCreditPackageIfNotIncludeCapabilities() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:15", "user1");
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(15L, 205L, 1, 2, false);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getContent().size(), equalTo(0));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(0));
        assertThat(usages.getTotalElements(), equalTo(0L));
        assertThat(usages.hasContent(), equalTo(false));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(0));
        assertThat(usages.isFirst(), equalTo(true));
    }

    @Test
    public void doesNotReturnCapabilityUsageForCreditPackageIfIncludeCapabilities() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:15", "user1");
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(15L, 205L, 1, 2, true);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getContent().size(), equalTo(1));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(1));
        assertThat(usages.getTotalElements(), equalTo(1L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNextPage(), equalTo(false));
        assertThat(usages.hasPreviousPage(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(1));
        assertThat(usages.isFirstPage(), equalTo(true));
    }

    @Test
    public void doesNotReturnCapabilityUsageForAccountIfNotIncludeCapabilities() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:15", "user1");
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(15L, null, null, null, null, false);
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(packageSearchFilter, 1, 2);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getContent().size(), equalTo(1));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(1));
        assertThat(usages.getTotalElements(), equalTo(1L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNextPage(), equalTo(false));
        assertThat(usages.hasPreviousPage(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(1));
        assertThat(usages.isFirstPage(), equalTo(true));
    }

    @Test
    public void doesNotReturnCapabilityUsageForAccountIfIncludeCapabilities() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:15", "user1");
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(15L, null, null, null, null, true);
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(packageSearchFilter, 1, 2);
        assertThat(usages.getSize(), equalTo(2));
        assertThat(usages.getContent().size(), equalTo(2));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(2));
        assertThat(usages.getTotalElements(), equalTo(2L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNextPage(), equalTo(false));
        assertThat(usages.hasPreviousPage(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(1));
        assertThat(usages.isFirstPage(), equalTo(true));
    }


    @Test
    public void returnsAllPackageUsagesForAnAccountOnPageOne() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(14L, null, null, null, null, false);
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(packageSearchFilter, 1, 4);
        assertThat(usages.getSize(), equalTo(4));
        assertThat(usages.getContent().size(), equalTo(4));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(4));
        assertThat(usages.getTotalElements(), equalTo(6L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(true));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(2));
        assertThat(usages.isFirst(), equalTo(true));
    }

    @Test
    public void returnsAllPackageUsagesForAnAccountOnPageTwo() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(14L, null, null, null, null, false);
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(packageSearchFilter, 2, 4);
        assertThat(usages.getSize(), equalTo(4));
        assertThat(usages.getContent().size(), equalTo(2));
        assertThat(usages.getNumber(), equalTo(2));
        assertThat(usages.getNumberOfElements(), equalTo(2));
        assertThat(usages.getTotalElements(), equalTo(6L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(true));
        assertThat(usages.getTotalPages(), equalTo(2));
        assertThat(usages.isFirst(), equalTo(false));
    }

    @Test
    public void returnsAllPackageUsagesForActivePackagesForAnAccount() {

        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(14L, null, null, null, true, false);
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(packageSearchFilter, 1, 10);
        assertThat(usages.getSize(), equalTo(10));
        assertThat(usages.getContent().size(), equalTo(3));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(3));
        assertThat(usages.getTotalElements(), equalTo(3L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(1));
        assertThat(usages.isFirst(), equalTo(true));

        assertPackageUsage(
                usages.getContent().get(0),
                createDateTime(1, 5),
                11L,
                "Specialist nursing job in manchester",
                "<EMAIL>",
                "<EMAIL>",
                null,
                "London Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(1),
                createDateTime(1, 3),
                9L,
                "Automatic package test ad where package has expired",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "London Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(2),
                createDateTime(1, 1),
                8L,
                "Automatic package test ad for pro account",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "London Jobs Insertion");
    }

    @Test
    public void returnsAllPackageUsagesForInactivePackagesForAnAccount() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(14L, null, null, null, false, false);
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(packageSearchFilter, 1, 10);
        assertThat(usages.getSize(), equalTo(10));
        assertThat(usages.getContent().size(), equalTo(3));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(3));
        assertThat(usages.getTotalElements(), equalTo(3L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(1));
        assertThat(usages.isFirst(), equalTo(true));

        assertPackageUsage(
                usages.getContent().get(0),
                createDateTime(1, 6),
                11L,
                "Specialist nursing job in manchester",
                "<EMAIL>",
                "<EMAIL>",
                null,
                "RoUK Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(1),
                createDateTime(1, 4),
                9L,
                "Automatic package test ad where package has expired",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "RoUK Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(2),
                createDateTime(1, 2),
                8L,
                "Automatic package test ad for pro account",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "RoUK Jobs Insertion");
    }

    @Test
    public void returnsAllPackageUsagesForPackagesOfASpecifiedTypeForAnAccount() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(14L, 7L, null, null, null, false);
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(packageSearchFilter, 1, 10);
        assertThat(usages.getSize(), equalTo(10));
        assertThat(usages.getContent().size(), equalTo(3));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(3));
        assertThat(usages.getTotalElements(), equalTo(3L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(1));
        assertThat(usages.isFirst(), equalTo(true));

        assertPackageUsage(
                usages.getContent().get(0),
                createDateTime(1, 5),
                11L,
                "Specialist nursing job in manchester",
                "<EMAIL>",
                "<EMAIL>",
                null,
                "London Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(1),
                createDateTime(1, 3),
                9L,
                "Automatic package test ad where package has expired",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "London Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(2),
                createDateTime(1, 1),
                8L,
                "Automatic package test ad for pro account",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "London Jobs Insertion");
    }

    @Test
    public void returnsAllPackageUsagesForCreditsAppliedWithinASpecifiedDateRangeForAnAccount() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:14", "user1");
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(14L, null,
                new DateTime(2012, 1, 3, 0, 0, 0, 0), new DateTime(2012, 1, 5, 0, 0, 0, 0), null, false);
        Page<CreditPackageUsage> usages = creditPackageService.getPackageUsages(packageSearchFilter, 1, 10);
        assertThat(usages.getSize(), equalTo(10));
        assertThat(usages.getContent().size(), equalTo(3));
        assertThat(usages.getNumber(), equalTo(1));
        assertThat(usages.getNumberOfElements(), equalTo(3));
        assertThat(usages.getTotalElements(), equalTo(3L));
        assertThat(usages.hasContent(), equalTo(true));
        assertThat(usages.hasNext(), equalTo(false));
        assertThat(usages.hasPrevious(), equalTo(false));
        assertThat(usages.getTotalPages(), equalTo(1));
        assertThat(usages.isFirst(), equalTo(true));

        assertPackageUsage(
                usages.getContent().get(0),
                createDateTime(1, 5),
                11L,
                "Specialist nursing job in manchester",
                "<EMAIL>",
                "<EMAIL>",
                null,
                "London Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(1),
                createDateTime(1, 4),
                9L,
                "Automatic package test ad where package has expired",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "RoUK Jobs Insertion");

        assertPackageUsage(
                usages.getContent().get(2),
                createDateTime(1, 3),
                9L,
                "Automatic package test ad where package has expired",
                "<EMAIL>",
                "<EMAIL>",
                "01632 960001",
                "London Jobs Insertion");
    }

    private DateTime createDateTime(int month, int day) {
        return new DateTime(2012, month, day, 0, 0, 0, 0);
    }

    private void assertPackageUsage(
            CreditPackageUsage usage,
            DateTime appliedDate,
            Long advertId,
            String advertTitle,
            String appliedBy,
            String contactEmail,
            String contactTelephone,
            String packageType) {

        assertThat(usage.getAppliedDate(), equalTo(appliedDate));
        assertThat(usage.getAdvertId(), equalTo(advertId));
        assertThat(usage.getAdvertTitle(), equalTo(advertTitle));
        assertThat(usage.getAppliedBy(), equalTo(appliedBy));
        assertThat(usage.getContactEmail(), equalTo(contactEmail));
        assertThat(usage.getContactTelephone(), equalTo(contactTelephone));
        assertThat(usage.getPackageType(), equalTo(packageType));
    }

    private PackageRequest createDefaultPackageRequest() {
        PackageRequest request = new PackageRequest();
        request.setAdjustedCredits(null);
        request.setSalesForceId("anything_we_like");
        request.setSalesforceProductId("ASALESFORCEID");
        request.setAccountId(1L);
        request.setStartDate(new DateTime());
        DateTime endDate = request.getStartDate().plus(10000);
        request.setEndDate(endDate);
        request.setInitialCredits(100L);
        return request;
    }
}
