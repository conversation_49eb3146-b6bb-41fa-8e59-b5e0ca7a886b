package com.gumtree.integration.service.account;

import com.google.common.collect.ImmutableMap;
import com.gumtree.api.controller.exception.AccountLinkedToMultipleUsersException;
import com.gumtree.api.controller.exception.NonApiUserRoleException;
import com.gumtree.api.domain.savedsearches.SearchBean;
import com.gumtree.common.util.time.SystemClock;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.personalization.domain.savedsearches.SavedSearchRequest;
import com.gumtree.seller.domain.account.AccountCommand;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.account.status.AccountStatus;
import com.gumtree.seller.domain.advert.AdvertRequest;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.advert.entity.AdvertFeature;
import com.gumtree.seller.domain.deactivation.entity.DeactivationReason;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.domain.marketing.MarketingPreferenceServiceBean;
import com.gumtree.seller.domain.order.OrderItemRequest;
import com.gumtree.seller.domain.order.OrderRequest;
import com.gumtree.seller.domain.order.entity.Order;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.domain.user.CreateUserCommand;
import com.gumtree.seller.domain.user.UserAdServicePreferenceBean;
import com.gumtree.seller.domain.user.entity.ResetPasswordKey;
import com.gumtree.seller.domain.user.entity.ResetPasswordReason;
import com.gumtree.seller.domain.user.entity.Role;
import com.gumtree.seller.domain.user.entity.SocialData;
import com.gumtree.seller.domain.user.entity.SocialPlatform;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.entity.UserActivationKey;
import com.gumtree.seller.domain.user.entity.UserAdPreference;
import com.gumtree.seller.domain.verification.entity.Verification;
import com.gumtree.seller.domain.verification.entity.VerificationChannel;
import com.gumtree.seller.repository.account.AccountRepository;
import com.gumtree.seller.repository.advert.AdvertFeatureRepository;
import com.gumtree.seller.repository.advert.AdvertFingerprintRepository;
import com.gumtree.seller.repository.advert.AdvertRepository;
import com.gumtree.seller.repository.email.EmailAddressRepository;
import com.gumtree.seller.repository.image.ImageRepository;
import com.gumtree.seller.repository.marketing.UserMarketingPreferenceRepository;
import com.gumtree.seller.repository.product.ProductRepository;
import com.gumtree.seller.repository.user.ResetPasswordKeyRepository;
import com.gumtree.seller.repository.user.UserActivationKeyRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.repository.verification.VerificationRepository;
import com.gumtree.seller.service.account.AccountService;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.service.email.EmailAddressService;
import com.gumtree.seller.service.gdpr.user.GdprService;
import com.gumtree.seller.service.image.ImageService;
import com.gumtree.seller.service.keygen.KeyGeneratorService;
import com.gumtree.seller.service.marketing.UserMarketingPreferenceService;
import com.gumtree.seller.service.order.create.CreateOrderService;
import com.gumtree.seller.service.order.read.ReadOrderService;
import com.gumtree.seller.service.saveads.SaveAdsService;
import com.gumtree.seller.service.savedsearches.SavedSearchService;
import com.gumtree.seller.service.security.SecurityService;
import com.gumtree.seller.service.user.UserAdPreferenceService;
import com.gumtree.seller.service.user.UserService;
import com.gumtree.test.DataSetLocation;
import com.gumtree.wallet.packages.domain.PackageRequest;
import com.gumtree.wallet.packages.domain.entity.CreditPackage;
import com.gumtree.wallet.packages.repository.CreditPackageRepository;
import com.gumtree.wallet.packages.service.CreditPackageService;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import static com.google.common.collect.Lists.newArrayList;
import static java.util.stream.Collectors.toList;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.notNullValue;
import static org.junit.Assert.fail;

@DataSetLocation(value = "classpath:/com/gumtree/seller/service/account/delete-account-service-test-dataset.xml", dataSourceName = "sellerDatasource")
public class CreateAndDeleteAccountIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private AdvertRepository advertRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private ImageRepository imageRepository;

    @Autowired
    private AdvertFeatureRepository advertFeatureRepository;

    @Autowired
    private CreditPackageRepository creditPackageRepository;

    @Autowired
    private UserMarketingPreferenceRepository userMarketingPreferenceRepository;

    @Autowired
    private EmailAddressRepository emailAddressRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private GdprService testedObj;

    @Autowired
    private ImageService imageService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private CreditPackageService creditPackageService;

    @Autowired
    private CreateOrderService createOrderService;

    @Autowired
    private ReadOrderService readOrderService;

    @Autowired
    private UserMarketingPreferenceService userMarketingPreferenceService;

    @Autowired
    private EmailAddressService emailAddressService;

    @Autowired
    private SaveAdsService saveAdsService;

    @Autowired
    private SavedSearchService savedSearchService;

    @Autowired
    private UserAdPreferenceService userAdPreferenceService;

    @Autowired
    private VerificationRepository verificationRepository;

    @Autowired
    private ResetPasswordKeyRepository resetPasswordKeyRepository;

    @Autowired
    private UserActivationKeyRepository userActivationKeyRepository;

    @Autowired
    private KeyGeneratorService keyGeneratorService;

    @Autowired
    private SecurityService securityService;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private AdvertFingerprintRepository advertFingerprintRepository;

    @Before
    public void beforeEach() {
        setupDefaultPrincipal();
    }

    /**
     * The following tables have 0 records in production so they were omitted
     * <p>
     * usr_specified_location
     * edialog_user
     * saved_search
     * user_marketing_preference_category
     * usr_permission
     */
    @Test
    @Transactional
    public void shouldSuccessfullyDeleteAccountAndUserAndEmailAddress() {
        // given
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        Long accountId = createAccount(String.format("<EMAIL>", uuid));
        String uuid2 = UUID.randomUUID().toString().replaceAll("-", "");
        Long accountId2 = createAccount(String.format("<EMAIL>", uuid2));
        flushAll();
        resetHibernateCache();

        Account account = accountService.getAccount(accountId);
        User user = account.getUsers().get(0);
        long userId = user.getId();
        EmailAddress emailAddress = user.getEmailAddress();
        assertAccountHasCreditPackages(account);
        assertAccountHasEmailPreference(account);
        assertAccountHasDefaultImage(account);
        assertUserHasAdvertImages(account);
        assertUserHasDeactivation(account);
        assertAccountHasOrders(account);
        assertUserHasAdPreference(userId);
        assertUserHasApiKey(userId);
        assertUserHasGenderRecord(userId);
        assertUserHasRoles(userId);
        assertUserHasSocialData(userId);
        assertUserHasContactEmail(userId);
        assertUserHasVerification(userId);
        assertUserResetPasswordKey(userId);
        assertUserHasActivationKey(userId);

        List<Advert> adverts = advertService.getByAccountId(accountId);
        List<Long> advertIds = adverts.stream().map(Advert::getId).collect(toList());
        List<Long> imageIds = adverts.stream().flatMap(a -> a.getImages().stream()).map(Image::getId).collect(toList());
        assertAdvertsHasOrderItems(adverts);
        assertAdvertHasSpecifiedLocation(adverts);
        assertAdvertHasPostCode(adverts);
        assertAdvertHasOutCode(adverts);
        assertAdvertHasAuditFields(adverts);
        assertAdvertHasImages(adverts);
        assertAdvertHasAttributes(adverts);
        assertAdvertHasExternalRef(adverts);
        assertAdvertHasFeatures(adverts);
        assertAdvertHasWebsiteUrl(adverts);
        assertAdvertHasFingerprints(adverts);
        assertAdvertSaveAdsCount(userId, advertIds, 2);
        assertAdvertSavedSearchedCount(userId, 1);

        resetHibernateCache();

        // when
        testedObj.deleteAccount(userId, false);
        testedObj.deleteUser(userId, true);

        flushAll();
        resetHibernateCache();

        // then

        assertNoAccount(accountId);
        assertNoUser(userId);
        assertNoEmail(emailAddress);
        assertAdvertsAreDeleted(advertIds);
        assertAdvertFingerprintsAreDeleted(advertIds);
        assertAdvertSaveAdsCount(userId, advertIds, 0);
        assertAdvertSavedSearchedCount(userId, 0);
        assertImagesNotDeleted(imageIds);

        assertAccountNotDeleted(accountId2);
    }

    /**
     * Delete dormant user account
     */
    @Test
    @Transactional
    public void shouldSuccessfullyDeleteDormantAccount() {
        // given
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        Long accountId = createAccount(String.format("<EMAIL>", uuid));
        flushAll();
        resetHibernateCache();

        Account account = accountService.getAccount(accountId);
        User user = account.getUsers().get(0);
        long userId = user.getId();
        EmailAddress emailAddress = user.getEmailAddress();
        assertAccountHasCreditPackages(account);
        assertAccountHasEmailPreference(account);
        assertAccountHasDefaultImage(account);
        assertUserHasAdvertImages(account);
        assertUserHasDeactivation(account);
        assertAccountHasOrders(account);
        assertUserHasAdPreference(userId);
        assertUserHasApiKey(userId);
        assertUserHasGenderRecord(userId);
        assertUserHasRoles(userId);
        assertUserHasSocialData(userId);
        assertUserHasContactEmail(userId);
        assertUserHasVerification(userId);
        assertUserResetPasswordKey(userId);
        assertUserHasActivationKey(userId);

        List<Advert> adverts = advertService.getByAccountId(accountId);
        List<Long> advertIds = adverts.stream().map(Advert::getId).collect(toList());
        List<Long> imageIds = adverts.stream().flatMap(a -> a.getImages().stream()).map(Image::getId).collect(toList());
        assertAdvertsHasOrderItems(adverts);
        assertAdvertHasSpecifiedLocation(adverts);
        assertAdvertHasPostCode(adverts);
        assertAdvertHasOutCode(adverts);
        assertAdvertHasAuditFields(adverts);
        assertAdvertHasImages(adverts);
        assertAdvertHasAttributes(adverts);
        assertAdvertHasExternalRef(adverts);
        assertAdvertHasFeatures(adverts);
        assertAdvertHasWebsiteUrl(adverts);
        assertAdvertHasFingerprints(adverts);
        assertAdvertSaveAdsCount(userId, advertIds, 2);
        assertAdvertSavedSearchedCount(userId, 1);

        resetHibernateCache();

        // when
        testedObj.deleteAccount(userId, false);
        testedObj.deleteUser(userId, false);

        flushAll();
        resetHibernateCache();

        // then

        assertNoAccount(accountId);
        assertNoUser(userId);
        assertNoEmail(emailAddress);
        assertAdvertsAreDeleted(advertIds);
        assertAdvertFingerprintsAreDeleted(advertIds);
        assertAdvertSaveAdsCount(userId, advertIds, 0);
        assertAdvertSavedSearchedCount(userId, 0);
        assertImagesNotDeleted(imageIds);
    }

    /**
     * Delete dormant user account
     */
    @Test
    @Transactional
    public void shouldDeleteDormantUserWithoutAccount() {
        // given
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        User user = createUser(String.format("<EMAIL>", uuid), EnumSet.of(Role.RoleName.API_USER));
        flushAll();
        resetHibernateCache();

        testedObj.deleteUser(user.getId(), false);

        flushAll();
        resetHibernateCache();

        // then
        assertNoUser(user.getId());
        assertNoEmail(user.getEmailAddress());
    }

    @Test(expected = AccountLinkedToMultipleUsersException.class)
    @Transactional
    public void shouldFailDeleteWhenAccountLinkedToMultipleUsers() {
        Account account = accountService.getAccount(3L);
        User user = account.getUsers().get(0);
        long userId = user.getId();

        testedObj.deleteAccount(userId, true);

        fail("expected AccountLinkedToMultipleUsersException exception, but not thrown");
    }

    /**
     * Dormant account deletion should throw exception if the user is NON-API-USER
     */
    @Test(expected = NonApiUserRoleException.class)
    @Transactional
    public void shouldThrowExceptionForNonAPI_USER_DormantUserDeletion() {
        // given
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        Long accountId = createAccount(String.format("<EMAIL>", uuid), EnumSet.of(Role.RoleName.SUPER_USER));
        flushAll();
        resetHibernateCache();

        Account account = accountService.getAccount(accountId);
        User user = account.getUsers().get(0);

        long userId = user.getId();

        resetHibernateCache();

        // when
        testedObj.deleteAccount(userId, false);
        fail("Expected NonApiUserRoleException");

    }

    /**
     * Dormant account deletion should throw exception if the user is ebay motors user
     */
    @Test(expected = NonApiUserRoleException.class)
    @Transactional
    public void dormantAccountDeletionShouldThrowExceptionIfUserIsEbayMotorsUser() {
        // given
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        Long accountId = createAccount(String.format("<EMAIL>", uuid), EnumSet.of(Role.RoleName.API_USER, Role.RoleName.TRUSTED_FEED_PROVIDER));
        flushAll();
        resetHibernateCache();

        Account account = accountService.getAccount(accountId);
        User user = account.getUsers().get(0);

        long userId = user.getId();

        resetHibernateCache();

        // when
        testedObj.deleteAccount(userId, false);
        fail("Expected NonApiUserRoleException");

    }
    /**
     * Dormant account deletion should happen for JOB-USER
     */
    @Test
    @Transactional
    public void shouldAllowDormantUserDeletionForJOB_USER() {
        // given
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        Long accountId = createAccount(String.format("<EMAIL>", uuid), EnumSet.of(Role.RoleName.JOBS_USER));
        flushAll();
        resetHibernateCache();

        Account account = accountService.getAccount(accountId);
        User user = account.getUsers().get(0);

        long userId = user.getId();

        resetHibernateCache();

        // when
        testedObj.deleteAccount(userId, false);
        testedObj.deleteUser(userId, false);

        //then
        assertNoAccount(accountId);
        assertNoUser(userId);
    }

    /**
     * Dormant account deletion should happen for API_USER and JOB-USER
     */
    @Test
    @Transactional
    public void shouldAllowDormantUserDeletionForAPI_USERAndJOB_USER() {
        // given
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        Long accountId = createAccount(String.format("<EMAIL>", uuid), EnumSet.of(Role.RoleName.JOBS_USER, Role.RoleName.API_USER));
        flushAll();
        resetHibernateCache();

        Account account = accountService.getAccount(accountId);
        User user = account.getUsers().get(0);
        long userId = user.getId();

        // when
        testedObj.deleteAccount(userId, false);
        testedObj.deleteUser(userId, false);

        //then
        assertNoAccount(accountId);
        assertNoUser(userId);
    }

    private void assertUserHasVerification(long userId) {
        assertCountGreaterThanZero("verification", "usr_id", userId);
    }

    private void assertUserResetPasswordKey(long userId) {
        assertCountGreaterThanZero("reset_password_key", "usr_id", userId);
    }

    private void assertUserHasActivationKey(long userId) {
        assertCountGreaterThanZero("usr_activation_key", "usr_id", userId);
    }

    private void assertImagesNotDeleted(List<Long> imageIds) {
        assertThat(imageIds.isEmpty(), is(false));
        imageIds.stream().forEach(id -> {
            assertCountGreaterThanZero("image", "id", id);
        });
    }

    private void assertUserHasRoles(long userId) {
        assertCountGreaterThanZero("usr_role", "usr_id", userId);
    }

    private long getLong(String s, long param1) {
        return new JdbcTemplate(dataSource).queryForObject(s, new Object[]{param1}, Long.class);
    }

    private void assertUserHasSocialData(long userId) {
        assertCountGreaterThanZero("social_data", "usr_id", userId);
    }

    private void assertUserHasContactEmail(long userId) {
        assertCountGreaterThanZero("contact_email", "usr_id", userId);
    }

    private void assertUserHasGenderRecord(long userId) {
        assertCountGreaterThanZero("gender", "usr_id", userId);
    }

    private void assertUserHasApiKey(long userId) {
        assertCountGreaterThanZero("api_key", "usr_id", userId);
    }

    private void assertUserHasAdPreference(long userId) {
        assertThat(userAdPreferenceService.getAdPreference(userId), is(notNullValue()));
    }

    private void assertAccountHasDefaultImage(Account account) {
        assertThat(account.getDefaultImage(), is(notNullValue()));
    }

    private void assertUserHasAdvertImages(Account account) {
        List<Advert> adverts = advertService.getByAccountId(account.getId());
        assertThat(adverts.stream().map(a -> a.getImages().size()).count(), is(equalTo(2L)));
    }

    private void assertUserHasAdvertFeatures(Account account) {
        List<Advert> adverts = advertService.getByAccountId(account.getId());
        assertThat(adverts.stream().map(a -> a.getFeatures()).count(), is(greaterThan(0L)));
    }

    private void assertAccountHasOrders(Account account) {
        assertThat(getLong("select count(*) from purchase_order po where po.account_id=?", account.getId()), is(greaterThan(0L)));
        assertThat(getLong("select count(*) from purchase_order_item poi join purchase_order po on poi.purchase_order_id=po.id where po.account_id=?", account.getId()), is(greaterThan(0L)));
        assertThat(getLong("select count(*) from purchase_order_item_payment poip join purchase_order_item poi on poip.purchase_order_item_id=poi.id join purchase_order po on poi.purchase_order_id=po.id where po.account_id=?", account.getId()), is(greaterThan(0L)));
        assertThat(getLong("select count(*) from purchase_order_item_refund poir join purchase_order_item poi on poir.purchase_order_item_id=poi.id join purchase_order po on poi.purchase_order_id=po.id where po.account_id=?", account.getId()), is(greaterThan(0L)));
        assertThat(getLong("select count(*) from package_payment pp join purchase_order_item poi on pp.purchase_order_item_id=poi.id join purchase_order po on poi.purchase_order_id=po.id where po.account_id=?", account.getId()), is(greaterThan(0L)));
    }

    private void assertNoAccount(Long accountId) {
        assertThat(accountService.getAccount(accountId), is(nullValue()));
    }

    private void assertAccountNotDeleted(Long accountId) {
        Account account = accountService.getAccount(accountId);
        assertThat(account, is(not(nullValue())));
        assertThat(account.getUsers(), hasSize(1));
        User user = account.getUsers().get(0);
        Long userId = user.getId();

        assertAccountHasCreditPackages(account);
        assertAccountHasEmailPreference(account);
        assertAccountHasDefaultImage(account);
        assertUserHasDeactivation(account);
        assertUserHasAdPreference(userId);
        assertUserHasApiKey(userId);
        assertUserHasGenderRecord(userId);
        assertUserHasRoles(userId);
        assertUserHasSocialData(userId);
        assertUserHasContactEmail(userId);
        assertUserHasAdvertImages(account);
        assertUserHasEmailVerificationKey(userId);
        assertUserHasAdvertFeatures(account);
        assertAccountHasOrders(account);
    }

    private void resetHibernateCache() {
        entityManager.clear();
    }

    private void assertNoUser(long userid) {
        assertCountIsZero("usr", "id", userid);
    }

    private void assertCountGreaterThanZero(String table, String field, long value) {
        assertThat(getLong(String.format("select count(*) from %s where %s=?", table, field), value), is(greaterThan(0L)));
    }

    private void assertCountIsZero(String table, String field, long value) {
        assertThat(getLong(String.format("select count(*) from %s where %s=?", table, field), value), is(equalTo(0L)));
    }

    private void assertNoEmail(EmailAddress emailAddress) {
        int count = new JdbcTemplate(dataSource).queryForObject("select count(*) from email_address where email=?", new Object[]{emailAddress.getEmail()}, Integer.class);
        assertThat(count, is(equalTo(0)));
    }

    private void assertAdvertsAreDeleted(List<Long> advertIds) {
        advertIds.forEach(id -> {
            boolean exists = advertRepository.exists(id);
            assertThat(exists, is(equalTo(false)));
        });
    }

    private void assertAdvertFingerprintsAreDeleted(List<Long> advertIds) {
        advertIds.forEach(id -> {
            boolean exists = advertFingerprintRepository.exists(id);
            assertThat(exists, is(equalTo(false)));
        });
    }

    private void assertAccountHasCreditPackages(Account account) {
        assertThat(account.getCreditPackages().size(), is(greaterThan(1)));
    }

    private void assertAccountHasEmailPreference(Account account) {
        User user = account.getUsers().get(0);
        String email = user.getEmailAddress().getEmail();
        int count = new JdbcTemplate(dataSource).queryForObject("select count(*) from user_marketing_preference ump join email_address ea on ump.email_address_id=ea.id where ea.email=?", new Object[]{email}, Integer.class);
        assertThat(count, is(equalTo(1)));
    }

    private void assertUserHasDeactivation(Account account) {
        User user = account.getUsers().get(0);
        assertCountGreaterThanZero("usr_deactivation", "usr_id", user.getId());
    }

    private void assertUserHasEmailVerificationKey(Long userId) {
        long contactEmailId = getLong("select id from contact_email where usr_id=?", userId);
        assertCountGreaterThanZero("email_verification_key", "contact_email_id", contactEmailId);
    }

    private void assertAdvertHasPostCode(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getPostcode() != null), equalTo(true));
    }

    private void assertAdvertHasOutCode(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getOutcode() != null), equalTo(true));
    }

    private void assertAdvertHasSpecifiedLocation(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getSpecifiedLocation() != null), equalTo(true));
    }

    private void assertAdvertHasAuditFields(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getCreatedBy() != null), equalTo(true));
        assertThat(adverts.stream().allMatch(a -> a.getLastModifiedBy() != null), equalTo(true));
    }

    private void assertAdvertHasImages(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getImages().size() > 0), equalTo(true));
    }

    private void assertAdvertHasAttributes(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getAttributes().size() > 0), equalTo(true));
    }

    private void assertAdvertHasExternalRef(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getExternalRef() != null), equalTo(true));
    }

    private void assertAdvertHasFeatures(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getFeatures().size() > 0), equalTo(true));
    }

    private void assertAdvertHasWebsiteUrl(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> a.getWebsiteUrl().getWebsiteUrl() != null), equalTo(true));
    }

    private void assertAdvertHasFingerprints(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> advertFingerprintRepository.exists(a.getId())), equalTo(true));
    }

    private void assertAdvertSaveAdsCount(long userId, List<Long> advertIds, long expectedCount) {
        assertThat(advertIds.stream().filter(advertId -> saveAdsService.hasSavedAdvert(userId, advertId)).count(), equalTo(expectedCount));
    }

    private void assertAdvertSavedSearchedCount(long userId, int expectedCount) {
        assertThat(savedSearchService.getSavedSearches(userId).size(), equalTo(expectedCount));
    }

    private void assertAdvertsHasOrderItems(List<Advert> adverts) {
        assertThat(adverts.stream().allMatch(a -> readOrderService.getOrdersForAdvert(a.getId()).stream().allMatch(o -> o.getOrderItems().size() > 1)),
                equalTo(true));
    }

    private Long createAccount(String email) {
        return createAccount(email, EnumSet.of(Role.RoleName.API_USER));
    }

    private Long createAccount(String email, EnumSet userRoles) {
        User user = createUser(email, userRoles);

        AccountCommand accountCommand = createAccountCommandObject();

        setSubjectWithPermissionAndPrincipal("account:manageaccounts:*", "user1");
        Account account = accountService.createAccount(accountCommand);
        account.setPostingSince(toThePast(account.getPostingSince()));
        accountRepository.flush();
        userService.associateAccountsWithUser(user.getUsername(), newArrayList(account.getId()), false);

        Image defaultMage = new Image();
        defaultMage.setUrl("http://default" + 0);
        imageService.saveImage(defaultMage);
        accountService.setDefaultImage(account.getId(), defaultMage.getId());

        UserAdServicePreferenceBean userAdServicePreference = new UserAdServicePreferenceBean();
        userAdServicePreference.setConsent(UserAdPreference.ConsentValue.NO);
        userAdPreferenceService.setAdPreference(user.getId(), userAdServicePreference);

        List<Image> images = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            Image image = new Image();
            image.setUrl("http://" + i);
            images.add(image);
            imageService.saveImage(image);
            assertThat(image.getId(), is(not(nullValue())));
        }

        setSubjectWithPermissionAndPrincipal("account:manageads:" + account.getId(), "user1");
        Advert advert1 = advertService.createAdvert(createAdvertRequest(account.getId(), images.subList(0, 3), "extRef1"));
        Advert advert2 = advertService.createAdvert(createAdvertRequest(account.getId(), images.subList(3, 6), "extRef2"));
        ReflectionTestUtils.setField(advert1, "createdDate", toThePast(advert1.getCreatedDate()).toDate());
        ReflectionTestUtils.setField(advert2, "createdDate", toThePast(advert2.getCreatedDate()).toDate());
        advert1.setFeatures(newArrayList(feature(ProductName.URGENT, advert1), feature(ProductName.HOMEPAGE_SPOTLIGHT, advert1)));
        advert2.setFeatures(newArrayList(feature(ProductName.URGENT, advert2), feature(ProductName.HOMEPAGE_SPOTLIGHT, advert2)));
        advertRepository.flush();

        insertOrderRelatedInfoFor(account, advert1, "ASALESFORCEID", "salesForceId" + email);
        insertOrderRelatedInfoFor(account, advert2, "SFID-PT-002", "salesForceId2" + email);

        userService.deactivateUser(user.getId(), DeactivationReason.OTHER);

        saveAdsService.addAdvert(advert1.getId(), user.getId());
        saveAdsService.addAdvert(advert2.getId(), user.getId());

        SavedSearchRequest savedSearchRequest = new SavedSearchRequest();
        savedSearchRequest.setUserId(user.getId());
        SearchBean search = new SearchBean();
        search.setLocation("SW18 5AS");
        savedSearchRequest.setSearch(search);
        savedSearchRequest.setEmailAlert(false);
        savedSearchService.storeSavedSearch(savedSearchRequest);

        return account.getId();
    }

    private User createUser(String email, EnumSet userRoles) {
        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setEmail(email);

        emailAddress = emailAddressService.saveOrUpdate(emailAddress);
        assertThat(emailAddress.getEmail(), is(equalTo(email)));

        MarketingPreferenceServiceBean marketingPreference = new MarketingPreferenceServiceBean(email);
        userMarketingPreferenceService.saveOrUpdateUserMarketingPreference(marketingPreference);
        userMarketingPreferenceRepository.flush();

        User user = registerUserPersistsDetails(email, userRoles);
        SocialData socialData = new SocialData();
        socialData.setUser(user);
        socialData.setPlatform(SocialPlatform.GUMTREE_LOGIN);
        user.addSocialData(socialData);
        userRepository.saveAndFlush(user);
        securityService.resetApiKey(user.getId());
        storeVerification(user);
        storeResetPasswordKey(user);
        storeUsrActivationKey(user);

        return user;
    }


    private void storeVerification(User user) {
        Verification verification = new Verification();
        verification.setUser(user);
        verification.setVerificationDate(DateTime.now());
        verification.setPin("1234");
        verification.setChannel(VerificationChannel.SMS);
        verificationRepository.saveAndFlush(verification);
    }

    private void storeResetPasswordKey(User user) {
        ResetPasswordKey resetPasswordKey = new ResetPasswordKey();
        resetPasswordKey.initialise(user, keyGeneratorService, new SystemClock(), ResetPasswordReason.USER);
        resetPasswordKeyRepository.saveAndFlush(resetPasswordKey);
    }

    private void storeUsrActivationKey(User user) {
        UserActivationKey userActivationKey = new UserActivationKey.Builder()
                .withUser(user)
                .withKey("key" + user.getId())
                .withExpiryDate(new Date())
                .build();
        userActivationKeyRepository.saveAndFlush(userActivationKey);
    }

    private void insertOrderRelatedInfoFor(Account account, Advert advert, String salesforceProductId, String salesForceId) {
        setSubjectWithPermissionAndPrincipal("account:managepackages:" + account.getId(), "user1");
        CreditPackage cp = creditPackageService.createPackage(
                createDefaultPackageRequest(account, salesforceProductId, salesForceId));
        setSubjectWithPermissionAndPrincipal("account:manageads:" + account.getId(), "user1");
        Order order = createOrderService.createOrder(orderRequest(account, advert, ProductName.WEBSITE_URL));
        int paymentId = randomInt();
        new JdbcTemplate(dataSource).update("insert into payment(id, amount, payment_method) values(?, 1, 'PAYPAL_CARD')", new Object[]{paymentId});
        Long purchaseOrderId = order.getOrderItems().get(0).getId();
        new JdbcTemplate(dataSource).update("insert into purchase_order_item_payment(purchase_order_item_id, payment_id) values(?, ?)", new Object[]{purchaseOrderId, paymentId});
        int refundId = randomInt();
        new JdbcTemplate(dataSource).update(
                "insert into refund(id, amount, vat_amount, payment_reference, refund_reference, refund_reason) " +
                        "values(?, 1, 1 , 'payment reference', 'refund reference', 'refund reason')", new Object[]{refundId});
        new JdbcTemplate(dataSource).update(
                "insert into purchase_order_item_refund(purchase_order_item_id, refund_id) " +
                        "values(?, ?)", new Object[]{purchaseOrderId, refundId});
        new JdbcTemplate(dataSource).update("insert into package_payment(purchase_order_item_id, credit_package_id) " +
                "values(?, ?)", new Object[]{purchaseOrderId, cp.getId()});
    }

    private int randomInt() {
        return new Random().nextInt();
    }

    private Date toThePast(Date date) {
        return toThePast(new DateTime(date)).toDate();
    }

    private DateTime toThePast(DateTime date) {
        return date.minusMonths(48);
    }

    private void flushAll() {
        accountRepository.flush();
        advertRepository.flush();
        advertFingerprintRepository.flush();
        imageRepository.flush();
        advertFeatureRepository.flush();
        creditPackageRepository.flush();
        userRepository.flush();
        emailAddressRepository.flush();
    }

    private AccountCommand createAccountCommandObject() {
        AccountCommand accountCommand = new AccountCommand();
        accountCommand.setAddress1("address1");
        accountCommand.setAddress2("address2");
        accountCommand.setCity("city");
        accountCommand.setCounty("county");
        accountCommand.setCountry("country");
        accountCommand.setDescription("description");
        accountCommand.setPostcode("postcode");
        accountCommand.setName("name");
        accountCommand.setAccountStatus(AccountStatus.ACTIVE);
        return accountCommand;
    }

    public User registerUserPersistsDetails(String emailAddress, EnumSet roles) {
        CreateUserCommand request = new CreateUserCommand();
        request.setFirstName("Roger");
        request.setLastName("Climpson");
        request.setEmailAddress(emailAddress);
        request.setContactPhone(" 01632 960001");
        request.setPlainTextPassword("funnyman31");
        request.setGender("my-gender");
        request.setRoles(roles);
        User user = userService.createUser(request);
        user.setCreationDate(toThePast(user.getCreationDate()));
        userRepository.flush();
        userService.addContactEmail(user.getId(), emailAddress);

        return user;
    }

    private AdvertRequest createAdvertRequest(Long accountId, List<Image> images, String externalRef) {
        AdvertRequest createAdvertRequest = new AdvertRequest();
        createAdvertRequest.setCategoryId(1L);
        createAdvertRequest.setPostcode("SW18 5AS");
        createAdvertRequest.setContactUrl("http://www.gumtree.com");
        createAdvertRequest.setContactEmail("<EMAIL>");
        createAdvertRequest.setContactTelephone("01632 960001");
        createAdvertRequest.setDescription("A test description");
        createAdvertRequest.setTitle("A test title");
        createAdvertRequest.setAccountId(accountId);
        createAdvertRequest.setIp("127.0.0.1");
        createAdvertRequest.setYoutubeLink("http://www.youtube.com/watch?v=B8WHKRzkCOY&feature=g-all-esi");
        createAdvertRequest.setWebsiteUrl("http://www.gumtree.com");
        createAdvertRequest.setSpecifiedLocationId(1L);
        createAdvertRequest.setImageIds(images.stream().map(Image::getId).collect(toList()));
        createAdvertRequest.setMainImageId(images.get(0).getId());
        createAdvertRequest.setAttributes(ImmutableMap.of("color", "green", "size", "L"));
        createAdvertRequest.setExternalRef(externalRef);
        return createAdvertRequest;
    }

    private AdvertFeature feature(ProductName productName, Advert advert) {
        Product product = productRepository.findByName(productName);
        AdvertFeature feature = new AdvertFeature();
        feature.setProduct(product);
        feature.setExpiryDate(DateTime.now());
        feature.setAdvert(advert);
        return feature;
    }

    private PackageRequest createDefaultPackageRequest(Account account, String salesforceProductId, String salesForceId) {
        PackageRequest request = new PackageRequest();
        request.setAdjustedCredits(null);
        request.setSalesForceId(salesForceId);
        request.setSalesforceProductId(salesforceProductId);
        request.setAccountId(account.getId());
        request.setStartDate(new DateTime());
        DateTime endDate = request.getStartDate().plus(10000);
        request.setEndDate(endDate);
        request.setInitialCredits(100L);
        return request;
    }

    private OrderRequest orderRequest(Account account, Advert advert, ProductName productName) {
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.setAccountId(account.getId());

        OrderItemRequest orderItemRequest = new OrderItemRequest();
        orderItemRequest.setAdvertId(advert.getId());
        orderItemRequest.setProductName(productName);
        List<OrderItemRequest> orderItemRequests = new ArrayList<>();
        orderItemRequests.add(orderItemRequest);
        orderRequest.setOrderItemRequests(orderItemRequests);
        return orderRequest;
    }

}
