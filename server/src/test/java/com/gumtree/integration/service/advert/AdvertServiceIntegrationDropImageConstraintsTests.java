package com.gumtree.integration.service.advert;

import com.codahale.metrics.MetricRegistry;
import com.gumtree.api.Ad;
import com.gumtree.api.AdvertSummary;
import com.gumtree.api.controller.AccountController;
import com.gumtree.api.controller.AdvertController;
import com.gumtree.api.controller.EmgAdvertController;
import com.gumtree.api.converter.advert.AdvertToAdConverter;
import com.gumtree.api.converter.advert.AdvertToApiAdvertConverter;
import com.gumtree.api.converter.search.AdvertToFlatAdConverter;
import com.gumtree.bapi.model.Image;
import com.gumtree.common.util.time.SystemClock;
import com.gumtree.context.bapi.domain.listingevents.AdvertDomainEventFactory;
import com.gumtree.sapi.spec.domain.FlatAd;
import com.gumtree.seller.domain.advert.AdvertRequest;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.image.exception.ImageNotFoundException;
import com.gumtree.seller.event.advert.AdvertOldState;
import com.gumtree.seller.event.advert.BeforeAdvertPostedEvent;
import com.gumtree.seller.event.advert.BeforeAdvertPublishedEvent;
import com.gumtree.seller.notifications.akka.DbQueryMessage;
import com.gumtree.seller.notifications.akka.DbResultsFactory;
import com.gumtree.seller.notifications.provider.FetchImagesQuery;
import com.gumtree.seller.service.advert.AdvertRequestBuilder;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.test.BaseShiroTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.gumtree.integration.BaseIntegrationTest.setBaseIntegrationTestProperties;
import static java.lang.String.format;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;
import static org.fest.util.Lists.newArrayList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;

/**
 * No test for ValidImageIds - we assume that imageIds still need to be validated against image table as it is even in case of inconsistency in the DB
 * Only unit Tests for ChaseEmailServiceImpl
 *
 * @see com.gumtree.api.validation.image.ValidImageIds
 * @see com.gumtree.api.domain.advert.PostAdvertBean
 * @see com.gumtree.api.validation.image.ImageIdValidatorImpl
 */
public abstract class AdvertServiceIntegrationDropImageConstraintsTests extends BaseShiroTest {
    private static Long private_ACCOUNT_ID = 1L;

    private static String private_ACCOUNT_PERMISSION = "account:manageads:1";

    private static long EXISTING_ADVERT_ID = 1L;
    private static long NON_EXISTING_IMAGE_ID = -9999L;
    protected static long FIRST_IMAGE_ID = 40001L;
    protected static long LAST_IMAGE_ID = 40003L;
    private static long FIRST_IMAGE_UPDATE_REQ = 40002L;

    static {
        setBaseIntegrationTestProperties();
    }

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertToApiAdvertConverter advertToApiAdvertConverter;

    @Autowired
    private AdvertToAdConverter advertToAdConverter;

    @Autowired
    private AdvertToFlatAdConverter advertToFlatAdConverter;

    @Autowired
    private AdvertController advertController;

    @Autowired
    private EmgAdvertController emgAdvertController;

    @Resource(name = "sellerDatasource")
    protected DataSource sellerDatasource;

    private AccountController accountController() {
        MetricRegistry metricRegistry = Mockito.mock(MetricRegistry.class);
        return new AccountController(null, null, advertService, null, advertToApiAdvertConverter,
                null, null, null, metricRegistry);
    }

    protected abstract void switchOffConstraint();

    protected abstract void switchOnConstraint();

    @Before
    public void checkNonExisting() {
        Integer nonExistingIdCount = new JdbcTemplate(sellerDatasource).queryForObject("select count(id) from image where id=" + NON_EXISTING_IMAGE_ID, Integer.class);
        assertThat(nonExistingIdCount, equalTo(0));
    }

    @Before
    public void cleanAdvertImage() {
        delAdvertImage(NON_EXISTING_IMAGE_ID);
    }

    // create

    @Test
    public void advertImagesPersistedWhenNewAdvertCreated() {
        testAdvertImagesPersistedWhenNewAdvertCreated();
    }

    @Test
    public void advertImagesPersistedWhenNewAdvertCreatedWithNonFirstMainImage() {
        testAdvertImagesPersistedWhenNewAdvertCreatedWithAnotherMainImage();
    }

    @Test
    public void advertImagesPersistedWhenNewAdvertCreatedWithMainImageOutsideTheImageList() {
        testAdvertImagesPersistedWhenNewAdvertCreated(NON_EXISTING_IMAGE_ID, FIRST_IMAGE_ID);
    }

    @Test(expected = ImageNotFoundException.class)
    public void advertImagesWithAListWithNonExistingImage() {
        testAdvertImagesWithAListWithNonExistingImage();
    }

    // update

    @Test
    public void advertImageOrderUpdatedWhenAdvertUpdated() {
        testAdvertImageOrderUpdatedWhenAdvertUpdated();
    }

    @Test
    public void advertImageOrderUpdatedWhenAdvertUpdateWithMainImageOutsideTheImageList() {
        testAdvertImageOrderUpdatedWhenAdvertUpdateAWithMainImageOutsideTheImageList();
    }

    // getAdvert by ID with inconsistency in DB

    @Test
    public void advertImagesCanBeRetrievedUsingControllerWhenReferentialIntegritySwitchedOffAndImageDoesNotExist() {
        Advert advert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteNonMainImageAndAssertGetAdvertUsingController(advert);
        } finally {
            switchOnConstraint();
        }
    }

    @Test
    public void advertImagesCanBeRetrievedUsingControllerWhenReferentialIntegritySwitchedOffAndMainImageDoesNotExist() {
        Advert advert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteMainImageAndAssertGetAdvertUsingControllerWithNoPrimaryImage(advert);
        } finally {
            switchOnConstraint();
        }
    }

    @Test
    public void advertForConversCanBeRetrievedUsingControllerWhenReferentialIntegritySwitchedOffAndMainImageDoesNotExist() {
        Advert advert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteMainImageAndAssertGetAdvertForConversUsingControllerWithNoPrimaryImage(advert);
        } finally {
            switchOnConstraint();
        }
    }

    // get account adverts with inconsistency in DB

    @Test
    public void accountAdvertImagesCanBeRetrievedUsingControllerWhenReferentialIntegritySwitchedOffAndImageDoesNotExist() {
        Advert lastAdvert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteNonMainImageAndAssertGetAccountAdvertUsingAccountController(lastAdvert.getId());
        } finally {
            switchOnConstraint();
        }
    }

    @Test
    public void accountAdvertImagesCanBeRetrievedUsingControllerWhenReferentialIntegritySwitchedOffAndMainImageDoesNotExist() {
        Advert lastAdvert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteMainImageAndAssertGetAccountAdvertUsingControllerWithNoPrimaryImage(lastAdvert.getId());
        } finally {
            switchOnConstraint();
        }
    }

    // actor is getting adverts by ID while inconsistency in DB

    @Test
    public void advertImagesCanBeRetrievedUsingActorWhenReferentialIntegritySwitchedOffAndImageDoesNotExist() {
        Advert advert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteNonMainImageAndAssertGetAdvertUsingActor(advert);
        } finally {
            switchOnConstraint();
        }
    }

    @Test
    public void advertImagesCanBeRetrievedUsingActorWhenReferentialIntegritySwitchedOffAndMainImageDoesNotExist() {
        Advert advert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteMainImageAndAssertGetAdvertUsingActorWithNoPrimaryImage(advert);
        } finally {
            switchOnConstraint();
        }
    }

    // gdpr
    @Test
    public void gdprServiceCanHandleGDPRRequestsWhenReferentialIntegritySwitchedOffAndImageDoesNotExist() {
        createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteNonMainImageAndAssertGdprService();
        } finally {
            switchOnConstraint();
        }
    }

    @Test
    public void gdprServiceCanHandleGDPRRequestsWhenReferentialIntegritySwitchedOffAndMainImageDoesNotExist() {
        createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteMainImageAndAssertGdprService();
        } finally {
            switchOnConstraint();
        }
    }

    // test converters used in listener

    @Test
    public void convertersAndEventListenerCanHandleGDPRRequestsWhenReferentialIntegritySwitchedOffAndImageDoesNotExist() {
        Advert advert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteNonMainImageAndAssertConvertersAndEventListenerCanHandle(advert);
        } finally {
            switchOnConstraint();
        }
    }

    @Test
    public void convertersAndEventListenerCanHandleGDPRRequestsWhenReferentialIntegritySwitchedOffAndMainImageDoesNotExist() {
        Advert advert = createAdvertWithAdditionalImage();

        switchOffConstraint();
        try {
            deleteMainImageAndAssertConvertersAndEventListenerCanHandle(advert);
        } finally {
            switchOnConstraint();
        }
    }

    private void testAdvertImagesPersistedWhenNewAdvertCreated(Long mainImageId, Long expectedMainImageId) {
        Advert advert = createAdvert(newArrayList(FIRST_IMAGE_ID, 40002L), mainImageId == FIRST_IMAGE_ID ? null : mainImageId);

        assertAdvertWithImages(advert, newArrayList(40001L, 40002L), expectedMainImageId);

        Optional<AdvertSummary> advertSummary = advertService.getAdvertSummary(advert.getId());
        assertThat(advertSummary.isPresent(), is(equalTo(true)));
        assertThat(advertSummary.get().getImageUrl(), is(equalTo(advert.getPrimaryImage().getUrl())));
    }

    private void testAdvertImagesWithAListWithNonExistingImage() {
        setSubjectPermission(private_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest(private_ACCOUNT_ID);

        ArrayList<Long> imageIds = newArrayList(FIRST_IMAGE_ID, NON_EXISTING_IMAGE_ID);
        Long mainImageId = FIRST_IMAGE_ID;

        createAdvertRequest.setImageIds(imageIds);
        createAdvertRequest.setMainImageId(mainImageId);

        Advert advert = advertService.createAdvert(createAdvertRequest);

        assertAdvertWithImages(advert, imageIds, mainImageId);
    }

    private void testAdvertImagesPersistedWhenNewAdvertCreated(Long mainImageId) {
        testAdvertImagesPersistedWhenNewAdvertCreated(mainImageId, mainImageId);
    }

    private void testAdvertImagesPersistedWhenNewAdvertCreated() {
        testAdvertImagesPersistedWhenNewAdvertCreated(FIRST_IMAGE_ID);
    }

    private void testAdvertImagesPersistedWhenNewAdvertCreatedWithAnotherMainImage() {
        testAdvertImagesPersistedWhenNewAdvertCreated(40002L);
    }

    private void testAdvertImageOrderUpdatedWhenAdvertUpdated(long mainImageId, long expectedMainImageId) {
        setSubjectPermission(private_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();

        ArrayList<Long> imageIds = newArrayList(40002L, 40001L);
        requestBuilder.withImageIds(imageIds).withMainImage(mainImageId);

        Advert advert = advertService.updateAdvert(EXISTING_ADVERT_ID, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());

        assertAdvertWithImages(advert, imageIds, expectedMainImageId);

        Optional<AdvertSummary> advertSummary = advertService.getAdvertSummary(advert.getId());
        assertThat(advertSummary.isPresent(), is(equalTo(true)));
        assertThat(advertSummary.get().getImageUrl(), is(equalTo(advert.getPrimaryImage().getUrl())));
    }

    private void testAdvertImageOrderUpdatedWhenAdvertUpdated() {
        testAdvertImageOrderUpdatedWhenAdvertUpdated(FIRST_IMAGE_UPDATE_REQ, FIRST_IMAGE_UPDATE_REQ);
    }

    private void testAdvertImageOrderUpdatedWhenAdvertUpdateAWithMainImageOutsideTheImageList() {
        testAdvertImageOrderUpdatedWhenAdvertUpdated(NON_EXISTING_IMAGE_ID, FIRST_IMAGE_UPDATE_REQ);
    }

    protected Advert createAdvert(ArrayList<Long> imageIds, Long mainImageId) {
        setSubjectPermission(private_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest(private_ACCOUNT_ID);

        createAdvertRequest.setImageIds(imageIds);
        if (mainImageId != null) {
            createAdvertRequest.setMainImageId(mainImageId);
        }

        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());

        return advert;
    }

    protected Advert createAdvertWithAdditionalImage() {
        addImage(LAST_IMAGE_ID);
        Advert advert = createAdvert(newArrayList(40001L, 40002L, 40003L), 40001L);
        assertAdvertWithImages(advert, newArrayList(40001L, 40002L, 40003L), 40001L);
        return advert;
    }

    private void addImage(long id) {
        new JdbcTemplate(sellerDatasource).execute(format("insert into image(id,url) values(%d,'%s')", id, imageUrl(id)));
    }

    private String imageUrl(long id) {
        return format("http://foo.bar/%d.jpg", id);
    }

    protected void deleteNonMainImageAndAssertGetAdvertUsingController(Advert advert) {
        delImage(LAST_IMAGE_ID);
        try {

            advert = getAdvertUsingServiceAndController(advert.getId()).usingService;

            assertAdvertWithImages(advert, newArrayList(40001L, 40002L), 40001L);
        } finally {
            delAdvertImage(LAST_IMAGE_ID);
        }
    }

    protected void delAdvertImage(long imageId) {
        new JdbcTemplate(sellerDatasource).execute("delete from advert_image where image_id=" + imageId);
    }

    protected void delImage(long imageId) {
        new JdbcTemplate(sellerDatasource).execute("delete from media where id=" + imageId);
        new JdbcTemplate(sellerDatasource).execute("delete from image where id=" + imageId);
    }

    class RetrievedAdvert {
        Advert usingService;
        Ad usingAdvertController;
        AdvertSummary advertForConvers;
        Ad usingAccountController;
    }

    private RetrievedAdvert getAdvertUsingServiceAndController(Long advertId) {
        Advert usingService = advertService.getAdvert(advertId);
        Ad usingController = advertController.getAdvert(advertId);
        com.gumtree.bapi.model.Ad usingEmgController = emgAdvertController.getAdvert(advertId);
        AdvertSummary advertForConvers = advertController.getAdvertForConvers(advertId);

        // compare usingController and ad
        assertThat(usingController.getImages().stream().map(im -> im.values().iterator().next().getId()).collect(toList()),
                is(equalTo(usingService.getImages().stream().map(i -> i.getId()).sorted().collect(toList()))));
        assertThat(usingController.getMainImageId(), is(equalTo(idFromPrimaryOrFirstImage(usingService))));

        // compare usingEmgController and ad
        assertThat(usingEmgController.getImages().stream().map(Image::getId).collect(toList()),
                equalTo(usingService.getImages().stream().map(i -> i.getId()).sorted().collect(toList())));
        assertThat(usingEmgController.getMainImageId(), is(equalTo(idFromPrimaryOrFirstImage(usingService))));

        // compare advertForConvers and ad
        assertThat(advertForConvers.getImageUrl(), is(equalTo(urlFromPrimaryOrFirstImage(usingService))));

        RetrievedAdvert retrievedAdvert = new RetrievedAdvert();
        retrievedAdvert.usingService = usingService;
        retrievedAdvert.usingAdvertController = usingController;
        retrievedAdvert.advertForConvers = advertForConvers;
        return retrievedAdvert;
    }

    private String urlFromPrimaryOrFirstImage(Advert advert) {
        return advert.getPrimaryImage() != null ? advert.getPrimaryImage().getUrl() : advert.getImages().isEmpty() ? null : advert.getImages().get(0).getUrl();
    }

    private Long idFromPrimaryOrFirstImage(Advert advert) {
        return advert.getPrimaryImage() != null ? advert.getPrimaryImage().getId() : advert.getImages().isEmpty() ? null : advert.getImages().get(0).getId();
    }

    private void deleteNonMainImageAndAssertGetAccountAdvertUsingAccountController(Long advertId) {
        Advert advert = getAccountAdvertImagesUsingServiceAndAccountController(advertId).usingService;
        assertAdvertWithImages(advert, newArrayList(40001L, 40002L, 40003L), 40001L);

        delImage(LAST_IMAGE_ID);
        try {

            RetrievedAdvert retrievedAdvert = getAccountAdvertImagesUsingServiceAndAccountController(advertId);

            assertAdvertWithImages(retrievedAdvert.usingAccountController, newArrayList(40001L, 40002L), 40001L);
        } finally {
            delAdvertImage(LAST_IMAGE_ID);
        }
    }

    // not very relevant - just added for
    // AdsBuilderActor cannot be created so we just verify AdsBuilderActor#loadAdsImages
    private void deleteNonMainImageAndAssertGetAdvertUsingActor(Advert advert) {
        deleteImageAndAssertGetAdvert(advert, LAST_IMAGE_ID);
    }

    private void deleteNonMainImageAndAssertGdprService() {
        deleteImageAndAssertGdprService(LAST_IMAGE_ID, newArrayList(40001L, 40002L));
    }

    private void deleteMainImageAndAssertGdprService() {
        deleteImageAndAssertGdprService(FIRST_IMAGE_ID, newArrayList(40002L, 40003L));
    }

    private void deleteNonMainImageAndAssertConvertersAndEventListenerCanHandle(Advert advert) {
        deleteImageAndAssertConvertersAndEventListenerCanHandle(advert, FIRST_IMAGE_ID);
    }

    private void deleteMainImageAndAssertConvertersAndEventListenerCanHandle(Advert advert) {
        deleteImageAndAssertConvertersAndEventListenerCanHandle(advert, LAST_IMAGE_ID);
    }

    private DbQueryMessage loadAdsImages(Collection<Long> advertIds, DbResultsFactory dbResultsFactory) {
        String query = new FetchImagesQuery().advertIds(advertIds).buildQuery();
        FetchImagesQuery.ImageRowHandler rowHandler = new FetchImagesQuery.ImageRowHandler();
        return new DbQueryMessage(query, rowHandler, dbResultsFactory);
    }

    private void assertAdvertWithImages(Advert advert, ArrayList<Long> imageIds, Long mainImageId) {
        assertThat(advert.getImages().size(), is(equalTo(imageIds.size())));
        if (imageIds.isEmpty()) {
            assertThat(advert.getPrimaryImage(), is(nullValue()));
        } else {
            if (mainImageId == null) {
                assertThat(advert.getPrimaryImage(), is(nullValue()));
            } else {
                assertThat(advert.getPrimaryImage().getId(), is(equalTo(mainImageId)));
            }
            for (int i = 0; i < imageIds.size(); i++) {
                assertThat(advert.getImages().get(i).getId(), is(equalTo(imageIds.get(i))));
            }
        }
    }

    private void assertAdvertWithImages(Ad ad, ArrayList<Long> imageIds, Long mainImageId) {
        assertThat(ad.getImages().size(), is(equalTo(imageIds.size())));
        if (imageIds.isEmpty()) {
            assertThat(ad.getMainImageId(), is(nullValue()));
        } else {
            if (mainImageId == null) {
                assertThat(ad.getMainImageId(), is(nullValue()));
            } else {
                assertThat(ad.getMainImageId(), is(equalTo(mainImageId)));
            }
            for (int i = 0; i < imageIds.size(); i++) {
                assertThat(ad.getImages().get(i).values().iterator().next().getId(), is(equalTo(imageIds.get(i))));
            }
        }
    }

    private void assertAdvertWithImages(AdvertSummary advertSummary, String mainImageUrl) {
        if (mainImageUrl == null) {
            assertThat(advertSummary.getImageUrl(), is(nullValue()));
        } else {
            assertThat(advertSummary.getImageUrl(), is(mainImageUrl));
        }
    }

    private RetrievedAdvert getAccountAdvertImagesUsingServiceAndAccountController(Long advertId) {
        Ad usingController = getAccountAdvertImagesUsingAccountController(advertId);
        Advert usingService = getAccountAdvertImagesUsingService(advertId);
        assertThat(usingController.getImages().stream().map(im -> im.values().iterator().next().getId()).collect(toList()),
                is(equalTo(usingService.getImages().stream().map(i -> i.getId()).sorted().collect(toList()))));
        assertThat(usingController.getMainImageId(), is(equalTo(idFromPrimaryOrFirstImage(usingService))));

        RetrievedAdvert retrievedAdvert = new RetrievedAdvert();
        retrievedAdvert.usingService = usingService;
        retrievedAdvert.usingAccountController = usingController;
        return retrievedAdvert;
    }

    private Advert getAccountAdvertImagesUsingService(Long advertId) {
        List<Advert> byAccountId = advertService.getByAccountId(private_ACCOUNT_ID);

        Optional<Advert> advert = filter(byAccountId, advertId);

        assertThat(advert.isPresent(), is(equalTo(true)));
        return advert.get();
    }

    private Optional<Advert> filter(List<Advert> byAccountId, Long advertId) {
        Optional<Advert> advert = byAccountId.stream()
                .filter(ad -> ad.getId().equals(advertId))
                .findAny();
        return advert;
    }

    private Ad getAccountAdvertImagesUsingAccountController(Long advertId) {
        List<Ad> ads = accountController().getAdverts(private_ACCOUNT_ID.toString());

        Optional<Ad> ad = ads.stream()
                .filter(a -> a.getId().equals(advertId))
                .findAny();

        assertThat(ad.isPresent(), is(equalTo(true)));

        return ad.get();
    }

    private void deleteMainImageAndAssertGetAdvertUsingControllerWithNoPrimaryImage(Advert advert) {
        assertThat(advert.getPrimaryImage().getId(), is(equalTo(FIRST_IMAGE_ID)));

        delImage(FIRST_IMAGE_ID);
        try {
            RetrievedAdvert retrievedAdvert = getAdvertUsingServiceAndController(advert.getId());

            assertAdvertWithImages(retrievedAdvert.usingService, newArrayList(40002L, 40003L), 40002L);
            assertAdvertWithImages(retrievedAdvert.usingAdvertController, newArrayList(40002L, 40003L), 40002L);
        } finally {
            addImage(FIRST_IMAGE_ID);
        }
    }

    private void deleteMainImageAndAssertGetAdvertForConversUsingControllerWithNoPrimaryImage(Advert advert) {
        assertThat(advert.getPrimaryImage().getId(), is(equalTo(FIRST_IMAGE_ID)));

        delImage(FIRST_IMAGE_ID);
        try {
            RetrievedAdvert retrievedAdvert = getAdvertUsingServiceAndController(advert.getId());

            assertAdvertWithImages(retrievedAdvert.usingService, newArrayList(40002L, 40003L), 40002L);
            assertAdvertWithImages(retrievedAdvert.advertForConvers, imageUrl(40002));
        } finally {
            delAdvertImage(FIRST_IMAGE_ID);
            addImage(FIRST_IMAGE_ID);
        }
    }


    private void deleteMainImageAndAssertGetAccountAdvertUsingControllerWithNoPrimaryImage(Long advertId) {
        Advert advert = getAccountAdvertImagesUsingServiceAndAccountController(advertId).usingService;
        assertAdvertWithImages(advert, newArrayList(40001L, 40002L, 40003L), FIRST_IMAGE_ID);

        delImage(FIRST_IMAGE_ID);
        try {
            RetrievedAdvert retrievedAdvert = getAccountAdvertImagesUsingServiceAndAccountController(advertId);

            assertAdvertWithImages(retrievedAdvert.usingService, newArrayList(40002L, 40003L), 40002L);
            assertAdvertWithImages(retrievedAdvert.usingAccountController, newArrayList(40002L, 40003L), 40002L);
        } finally {
            addImage(FIRST_IMAGE_ID);
        }
    }

    private void deleteMainImageAndAssertGetAdvertUsingActorWithNoPrimaryImage(Advert advert) {
        deleteImageAndAssertGetAdvert(advert, FIRST_IMAGE_ID);
    }

    private void deleteImageAndAssertGetAdvert(Advert advert, long imageId) {
        delImage(imageId);
        try {
            DbResultsFactory dbResultsFactory = new DbResultsFactory();

            DbQueryMessage dbQueryMessage = loadAdsImages(singletonList(advert.getId()), dbResultsFactory);

            assertThat(dbQueryMessage.getQuery(), is(equalTo(format("select   adimg.advert_id as advert_id,  case when (mda.url <> '') is true then mda.url else img.url end as image_url,  " +
                    "adimg.position as position,  adimg.main as is_main " +
                    "from advert_image adimg inner join image img on img.id = adimg.image_id left join media mda on mda.id = img.id " +
                    "where adimg.advert_id in(%d)", advert.getId()))));
        } finally {
            delAdvertImage(imageId);
        }
    }

    private void deleteImageAndAssertGdprService(long id, List<Long> expectedNonDeleted) {
        delImage(id);
        delAdvertImage(id);
        delAdvertImage(id);
    }

    private void deleteImageAndAssertConvertersAndEventListenerCanHandle(Advert advertBefore, long imageId) {
        // after delete
        AdvertDomainEventFactory advertDomainEventFactory = new AdvertDomainEventFactory(new SystemClock());

        delImage(imageId);
        try {
            // after delete
            Advert advertAfter = advertService.getAdvert(advertBefore.getId());

            // test indirectly AdvertConversionHelper
            advertDomainEventFactory.buildEvents(new BeforeAdvertPostedEvent(advertBefore));
            advertDomainEventFactory.buildEvents(new BeforeAdvertPublishedEvent(AdvertOldState.build(advertBefore), advertBefore));
            advertDomainEventFactory.buildEvents(new BeforeAdvertPostedEvent(advertAfter));
            advertDomainEventFactory.buildEvents(new BeforeAdvertPublishedEvent(AdvertOldState.build(advertBefore), advertAfter));
            advertDomainEventFactory.buildEvents(new BeforeAdvertPublishedEvent(AdvertOldState.build(advertAfter), advertAfter));

            com.gumtree.bapi.model.Ad convertBefore = advertToAdConverter.convert(advertBefore);
            com.gumtree.bapi.model.Ad convertAfter = advertToAdConverter.convert(advertAfter);

            assertThat(convertBefore.getImages().stream().map(i -> i.getId()).collect(toList()),
                    is(equalTo(advertBefore.getImages().stream().map(i -> i.getId()).sorted().collect(toList()))));

            assertThat(convertAfter.getImages().stream().map(i -> i.getId()).collect(toList()),
                    is(equalTo(advertAfter.getImages().stream().map(i -> i.getId()).sorted().collect(toList()))));

            FlatAd flatAdBefore = advertToFlatAdConverter.convert(advertBefore);
            FlatAd flatAdAfter = advertToFlatAdConverter.convert(advertAfter);

            // advertToFlatAdConverter choose a main image if it is missing
            assertThat(flatAdBefore.getPrimaryImageUrl(), is(equalTo(advertBefore.getPrimaryImage() == null ? null : advertBefore.getPrimaryImage().getUrl())));
            assertThat(flatAdAfter.getPrimaryImageUrl(), is(equalTo(advertAfter.getImages().get(0).getUrl())));

            assertThat(flatAdBefore.getAdditionalImageUrls(), is(equalTo(advertBefore.getImages().stream().filter(i -> advertBefore.getPrimaryImage() != null && i.getUrl() != advertBefore.getPrimaryImage().getUrl())
                    .map(i -> i.getUrl()).collect(toList()))));
            assertThat(flatAdAfter.getAdditionalImageUrls(), is(equalTo(advertAfter.getImages().stream().filter(i -> i.getUrl() != flatAdAfter.getPrimaryImageUrl())
                    .map(i -> i.getUrl()).collect(toList()))));
        } finally {
            delAdvertImage(imageId);
        }
    }

    private AdvertRequest createAdvertRequest(Long accountId) {
        AdvertRequest createAdvertRequest = new AdvertRequest();
        createAdvertRequest.setCategoryId(1L);
        createAdvertRequest.setPostcode("SW18 5AS");
        createAdvertRequest.setContactUrl("http://www.gumtree.com");
        createAdvertRequest.setContactEmail("<EMAIL>");
        createAdvertRequest.setContactTelephone("01632 960001");
        createAdvertRequest.setDescription("A test description");
        createAdvertRequest.setTitle("A test title");
        createAdvertRequest.setAccountId(accountId);
        createAdvertRequest.setIp("127.0.0.1");
        createAdvertRequest.setYoutubeLink("http://www.youtube.com/watch?v=B8WHKRzkCOY&feature=g-all-esi");
        createAdvertRequest.setWebsiteUrl("http://www.gumtree.com");
        return createAdvertRequest;
    }

    private AdvertRequestBuilder updateRequestBuilder() {
        AdvertRequestBuilder requestBuilder = new AdvertRequestBuilder();
        requestBuilder.withTitle("New title")
                .withDescription("New description")
                .withContactEmail("<EMAIL>")
                .withContactTelephone("01632 960001")
                .withContactUrl("http://www.newurl.com")
                .inCategory(2L)
                .withPostcode("SW18 5AS")
                .toAccount(private_ACCOUNT_ID);

        return requestBuilder;
    }
}
