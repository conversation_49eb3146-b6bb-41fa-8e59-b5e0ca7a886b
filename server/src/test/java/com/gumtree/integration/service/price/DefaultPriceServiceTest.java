package com.gumtree.integration.service.price;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.mvc.profile.IntegrationTests;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.repository.product.ProductRepository;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.service.price.PriceService;
import com.gumtree.seller.service.price.pricing.models.PricingContext;
import com.gumtree.seller.service.price.pricing.models.ProductPrice;
import com.gumtree.test.CleanInsertTestExecutionListener;
import com.gumtree.test.DataSetLocation;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;

import java.util.List;
import java.util.Map;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.greaterThan;

/**
 *
 */
@Ignore
@DataSetLocation(value = "classpath:/com/gumtree/seller/service/price/price-service-test-dataset.xml", dataSourceName="sellerDatasource")
@Category({IntegrationTests.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        locations = "/com/gumtree/integration/service/price/default-price-service-test-context.xml"
)
@TestExecutionListeners(
        {
                DependencyInjectionTestExecutionListener.class,
                CleanInsertTestExecutionListener.class,
                TransactionalTestExecutionListener.class
        }
)
public class DefaultPriceServiceTest extends BaseIntegrationTest {

    @Autowired
    private AdvertService advertService;

    @Autowired
    private PriceService priceService;

    @Autowired
    private ProductRepository productRepository;

    @Before
    public void setUp() {
        setSubjectWithPermissionAndPrincipal(ImmutableList.of("account:manageads:1"), "<EMAIL>");
    }

    @Test
    public void returnTheCorrectPricesGivenAnAdvert() {
        Advert advert = advertService.getAdvert(1L);
        List<ProductPrice> prices = priceService.getPrices(pricingContextFrom(advert));
        assertThat(prices.isEmpty(), equalTo(false));
    }

    @Test
    public void returnTheCorrectPriceForAnAdvertAndProduct() {
        Advert advert = advertService.getAdvert(1L);
        ProductPrice price = priceService.getPrice(pricingContextFrom(advert), product(ProductName.FEATURE_3_DAY));
        assertThat(price.getIncVat(), equalTo(995L));
    }

    @Test
    public void returnFreePriceIfUserGetsFeaturesForFree() throws Exception {
        setSubjectWithPermissionAndPrincipal(ImmutableList.of("account:manageads:1", "account:getfeaturesforfree:1"), "<EMAIL>");
        Advert advert = advertService.getAdvert(1L);
        ProductPrice price = priceService.getPrice(pricingContextFrom(advert), product(ProductName.FEATURE_3_DAY));
        assertThat(price.getIncVat(), equalTo(0L));
    }

    @Test
    public void returnFreePricesIfUserGetsFeaturesForFree() throws Exception {
        setSubjectWithPermissionAndPrincipal(ImmutableList.of("account:manageads:1", "account:getfeaturesforfree:1"), "<EMAIL>");
        Advert advert = advertService.getAdvert(1L);
        List<ProductPrice> prices = priceService.getPrices(pricingContextFrom(advert));
        for (ProductPrice price : prices) {
            assertThat(price.getIncVat(), equalTo(0L));
        }
        assertThat(prices.size(), equalTo(8));
    }

    @Test
    public void returnFreePricesForAllAdvertsIfUserGetsFeaturesForFree() throws Exception {
        setSubjectWithPermissionAndPrincipal(
                ImmutableList.of("account:manageads:1",
                        "account:getfeaturesforfree:1",
                        "account:manageads:2",
                        "account:getfeaturesforfree:2"
                ),
                "<EMAIL>");
        List<Advert> adverts = Lists.newArrayList(advertService.getAdvert(1L), advertService.getAdvert(2L));
        List<PricingContext> pricingContexts = PricingContext.factory().fromAdverts(adverts);

        Map<Long, List<ProductPrice>> prices = Maps.newHashMapWithExpectedSize(pricingContexts.size());
        pricingContexts.forEach(context -> {
            List<ProductPrice> productPrices = priceService.getPrices(context);
            prices.put(context.getAdvertId().get(), productPrices);
        });

        for (Advert advert : adverts) {
            for (ProductPrice price : prices.get(advert.getId())) {
                assertThat(price.getIncVat(), equalTo(0L));
            }
            assertThat(prices.get(advert.getId()).size(), equalTo(8));
        }
    }

    @Test
    public void returnNonFreePriceIfUserDoesntGetFeaturesForFree() throws Exception {
        setSubjectWithPermissionAndPrincipal(ImmutableList.of("account:manageads:1"), "<EMAIL>");
        Advert advert = advertService.getAdvert(1L);
        ProductPrice price = priceService.getPrice(pricingContextFrom(advert), product(ProductName.FEATURE_3_DAY));
        assertThat(price.getIncVat(), not(equalTo(0L)));
    }

    @Test
    public void returnNonFreePricesIfUserDoesntGetFeaturesForFree() throws Exception {
        setSubjectWithPermissionAndPrincipal(ImmutableList.of("account:manageads:1"), "<EMAIL>");
        Advert advert = advertService.getAdvert(1L);
        List<ProductPrice> prices = priceService.getPrices(pricingContextFrom(advert));
        for (ProductPrice price : prices) {
            assertThat(price.getIncVat(), not(equalTo(0L)));
        }
        assertThat(prices.size(), greaterThan(0));
    }

    @Test
    public void returnNonFreePricesForAllAdvertsIfUserDoesntGetFeaturesForFree() throws Exception {
        setSubjectWithPermissionAndPrincipal( ImmutableList.of("account:manageads:1", "account:manageads:2"), "<EMAIL>");
        List<Advert> adverts = Lists.newArrayList(advertService.getAdvert(1L), advertService.getAdvert(2L));

        List<PricingContext> pricingContexts = PricingContext.factory().fromAdverts(adverts);

        Map<Long, List<ProductPrice>> prices = Maps.newHashMapWithExpectedSize(pricingContexts.size());
        pricingContexts.forEach(context -> {
            List<ProductPrice> productPrices = priceService.getPrices(context);
            prices.put(context.getAdvertId().get(), productPrices);
        });

        for (Advert advert : adverts) {
            for (ProductPrice price : prices.get(advert.getId())) {
                assertThat(price.getIncVat(), not(equalTo(0L)));
            }
            assertThat(prices.get(advert.getId()).size(), greaterThan(0));
        }
    }

    @Test
    public void findAllPricesByTraversingHierarchy() {
        PricingContext pricingContext = PricingContext.builder()
                .withCatId(1L)
                .withLocId(2134L)
                .build();
        List<ProductPrice> prices = priceService.getPrices(pricingContext);
        assertThat(prices.size(), equalTo(5));
    }

    private Product product(ProductName productName) {
        return productRepository.findByName(productName);
    }

    private PricingContext pricingContextFrom(Advert advert) {
        return PricingContext.factory().fromAdvert(advert);
    }
}
