package com.gumtree.integration.service.packages;

import com.codahale.metrics.MetricRegistry;
import com.gumtree.api.category.CategoryMapperFactory;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.DefaultCategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.config.AdvertMessagingConfig;
import com.gumtree.config.DroolsConfig;
import com.gumtree.config.PricingConfig;
import com.gumtree.context.bapi.infrastructure.DomainEventLogConfiguration;
import org.codehaus.jackson.map.ObjectMapper;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.Collection;
@Import({DroolsConfig.class, PricingConfig.class, AdvertMessagingConfig.class, DomainEventLogConfiguration.class})
@Configuration
@ImportResource("/com/gumtree/seller/seller-test-application-context.xml")
public class CreditPackageLocatorTestConfig {

    @Bean
    public CategoryModel categoryModel() throws IOException {
        ObjectMapper json = new CategoryMapperFactory().create();
        return new DefaultCategoryModel("1",
                json.readValue(categoriesResource().getInputStream(), Category.class),
                scala.Option.<Collection<AttributeMetadata>>apply(null));
    }

    @Bean
    public MetricRegistry metricRegistry() {
        return Mockito.mock(MetricRegistry.class);
    }

    private ClassPathResource categoriesResource() {
        return new ClassPathResource("credit-package-locator-categories.json");
    }
}
