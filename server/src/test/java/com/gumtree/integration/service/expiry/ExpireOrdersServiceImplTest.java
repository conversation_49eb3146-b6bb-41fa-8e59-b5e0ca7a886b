package com.gumtree.integration.service.expiry;

import com.gumtree.api.service.expiry.ExpireOrdersServiceImpl;
import com.gumtree.common.util.time.Clock;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class ExpireOrdersServiceImplTest {

    @Test
    public void testGeneratesCorrectQuery() {
        DateTime now = new DateTime();
        Clock clock = mock(Clock.class);
        when(clock.getDateTime()).thenReturn(now);
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        JdbcOperations jdbcOperations = mock(JdbcOperations.class);


        Date createdDate = clock.getDateTime().minusDays(2).toDate();
        Date fromDate = clock.getDateTime().minusDays(2+3).toDate();
        when(jdbcOperations.queryForList("select t1.id from (" +
                "select distinct po.id " +
                "from purchase_order po left join purchase_order_item poi on po.id = poi.purchase_order_id " +
                "where po.created_date > ? and po.created_date < ? and poi.status = 'UNPAID'" +
                ") as t1 " +
                "where 'UNPAID' = all(select i.status from purchase_order o left join purchase_order_item i on o.id = i.purchase_order_id " +
                "where o.created_date > ? and o.created_date < ? and t1.id = o.id)",
                Long.class, fromDate, createdDate, fromDate, createdDate)).thenReturn(ids);
        ExpireOrdersServiceImpl service = new ExpireOrdersServiceImpl();
        ReflectionTestUtils.setField(service, "clock", clock);
        ReflectionTestUtils.setField(service, "jdbcOperations", jdbcOperations);
        assertThat(service.getIdsToExpire(2), equalTo(ids));
    }
}
