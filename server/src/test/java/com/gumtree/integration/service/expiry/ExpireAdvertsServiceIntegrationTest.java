package com.gumtree.integration.service.expiry;


import com.gumtree.api.service.expiry.ExpireAdvertsServiceImpl;
import com.gumtree.test.CleanInsertTestExecutionListener;
import com.gumtree.test.DataSetLocation;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;

import java.util.List;
import java.util.Properties;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        locations = "expiry-jobs-service-integration-test-context.xml",
        initializers = ExpireAdvertsServiceIntegrationTest.TestContextInitializer.class
)
@TestExecutionListeners(
        {
                DependencyInjectionTestExecutionListener.class,
                CleanInsertTestExecutionListener.class,
                TransactionalTestExecutionListener.class
        }
)
@DataSetLocation(value = "classpath:/com/gumtree/api/service/expiry/expire-adverts-service-test-dataset.xml", dataSourceName = "sellerDatasource")
public class ExpireAdvertsServiceIntegrationTest {

    @Autowired
    private ExpireAdvertsServiceImpl advertsService;

    private List<Long> idsToExpire;

    @Before
    public void init() {
        idsToExpire = advertsService.getIdsToExpire(20000L);
    }

    @Test
    public void whenAdvertsLiveAndNotReachedExpiry() {
        assertThat(idsToExpire.contains(3L), equalTo(false));
        assertThat(idsToExpire.contains(4L), equalTo(false));
    }

    @Test
    public void whenAdvertsLiveAndReachedExpiry() {
        assertThat(idsToExpire.contains(1L), equalTo(true));
        assertThat(idsToExpire.contains(2L), equalTo(true));
    }

    @Test
    public void whenAdvertIsNotLiveAndHasReachedExpiry() {
        assertThat(idsToExpire.contains(5L), equalTo(true));
    }

    @Test
    public void whenAdvertIsNotLiveAndHasNotReachedExpiry() {
        assertThat(idsToExpire.contains(6L), equalTo(false));
    }

    @Test
    public void whenAdvertIsLiveAndExpiryEqualsCurrentDateTime() {
        assertThat(idsToExpire.contains(7L), equalTo(true));
    }

    @Test
    @Ignore
    public void whenAdvertIsDraftAndReachedExpiry() {
        assertThat(idsToExpire.contains(8L), equalTo(true));
    }

    public static class TestContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public final void initialize(ConfigurableApplicationContext appContext) {
            Properties properties = new Properties();
            properties.setProperty("gumtree.seller.jobs.archive.draftdays", "0");
            ConfigurationManager.loadProperties(properties);
        }

    }

}
