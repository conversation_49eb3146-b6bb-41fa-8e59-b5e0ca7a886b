package com.gumtree.integration.service.image;

import com.gumtree.seller.domain.image.CreateImageRequest;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.service.image.ImageService;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.not;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/images.xml", dataSourceName="sellerDatasource")
public class ImageServiceTest extends BaseIntegrationTest {

    @Autowired
    private ImageService imageService;

    @Test
    public void validImageRequestIsPersisted() throws Exception {
        CreateImageRequest request = new CreateImageRequest();
        Resource resource = new ClassPathResource("com/gumtree/seller/images/mw.jpg");
        request.setImage(resource.getInputStream());
        request.setSize(resource.contentLength());
        Image image = imageService.postNewImage(request);
        image = imageService.getImage(image.getId());
        assertThat(image, not(equalTo(null)));
    }

}
