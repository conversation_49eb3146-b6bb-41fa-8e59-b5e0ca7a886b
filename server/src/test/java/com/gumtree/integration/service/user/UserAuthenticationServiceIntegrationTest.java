package com.gumtree.integration.service.user;

import com.gumtree.api.UserAuthenticationInfo;
import com.gumtree.common.util.security.exception.UnknownUserAccountException;
import com.gumtree.common.util.security.exception.UserAccountNotActiveException;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.service.security.UserAuthenticationService;
import com.gumtree.seller.service.user.impl.UsrCredentialsMissingException;
import com.gumtree.test.DataSetLocation;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

@DataSetLocation(value = "classpath:/com/gumtree/seller/service/user/user-authentication-service-test-dataset.xml", dataSourceName="sellerDatasource")
public class UserAuthenticationServiceIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private UserAuthenticationService userAuthenticationService;

    @Test
    @Transactional
    public void authenticationSuccess() throws UsrCredentialsMissingException {
        String username = "<EMAIL>";
        UserAuthenticationInfo userAuthenticationInfo = userAuthenticationService.authenticate(username, "gumtree123");
        assertThat(userAuthenticationInfo.getUsername(), is(username));
        assertThat(userAuthenticationInfo.getPasswordVersion(), is(1L));
    }

    @Test(expected = UnknownUserAccountException.class)
    @Transactional
    public void authenticationFailsForUnknownUser() {
        userAuthenticationService.authenticate("<EMAIL>", "aPassword");
    }

    @Test(expected = IncorrectCredentialsException.class)
    @Transactional
    public void authenticationFailsForInvalidPassword() {
        userAuthenticationService.authenticate("<EMAIL>", "notMyPassword");
    }

    @Test(expected = UsrCredentialsMissingException.class)
    @Transactional
    public void authenticationFailsWhenUsrCredentialsMissing() throws UsrCredentialsMissingException {
        userAuthenticationService.authenticate("<EMAIL>", "notMyPassword");
    }

    @Test(expected = UserAccountNotActiveException.class)
    @Transactional
    public void authenticationFailsForUserAwaitingActivation() {
        userAuthenticationService.authenticate("<EMAIL>", "myPassword");
    }

    @Test(expected = UserAccountNotActiveException.class)
    @Transactional
    public void authenticationFailsForDeactivatedUser() {
        userAuthenticationService.authenticate("<EMAIL>", "myPassword");
    }

}
