package com.gumtree.integration.service.security;

import com.google.common.collect.Lists;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.domain.user.entity.ApiKey;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.service.security.SecurityService;
import com.gumtree.test.DataSetLocation;
import org.fest.assertions.api.Assertions;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 */
    @DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/users-and-roles-and-permissions.xml", dataSourceName="sellerDatasource")
public class SecurityServiceTest extends BaseIntegrationTest {

    @Autowired
    private SecurityService securityService;

    @Autowired
    private UserRepository userRepository;

    @Test
    public void testGetUserWithRolesAndPermissionsReturnsFullyInitialisedObjectModel() {
        try {
            User user = securityService.getUserWithRolesAndPermissions("user1");
            assertThat(user.getPermissions().size(), equalTo(6));
            assertThat(user.getRoles().size(), equalTo(2));
        } catch (Exception ex) {
            ex.printStackTrace();
            Assert.fail("Are you sure you have initialized the full Hibernate object model for roles and permissions?");
        }
    }

    @Test
    @Transactional
    public void shouldResetUserApiKey() {
        // given
        User user = userRepository.findByUsername("<EMAIL>");

        // when
        ApiKey apiKey = securityService.resetApiKey(user.getId());

        // then
        Assertions.assertThat(apiKey.getAccessKey()).isNotEqualTo("user3").hasSize(32);
        Assertions.assertThat(apiKey.getPrivateKey()).isNotEqualTo("user3").hasSize(32);

        // and
        User updatedUser = userRepository.findByUsername("<EMAIL>");
        Assertions.assertThat(updatedUser.getApiKeys()).isEqualTo(Lists.newArrayList(apiKey));
    }
}
