package com.gumtree.integration.service.user;

import com.google.common.base.Optional;
import com.gumtree.common.util.security.exception.UserAccountNotActiveException;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.user.entity.ResetPasswordKey;
import com.gumtree.seller.domain.user.entity.ResetPasswordReason;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.entity.UserCredentials;
import com.gumtree.seller.domain.user.exception.ResetPasswordKeyExpiredException;
import com.gumtree.seller.domain.user.exception.ResetPasswordKeyUnrecognisedException;
import com.gumtree.seller.domain.user.exception.UserNotFoundException;
import com.gumtree.seller.repository.user.ResetPasswordKeyRepository;
import com.gumtree.seller.repository.user.UserCredentialsRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.service.user.PasswordService;
import com.gumtree.seller.service.user.UserService;
import com.gumtree.test.DataSetLocation;
import org.fest.assertions.api.Assertions;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.atomic.AtomicInteger;

import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;

@DataSetLocation(value = "classpath:/com/gumtree/seller/service/user/user-service-test-dataset.xml", dataSourceName="sellerDatasource")
public class PasswordServiceImplTest extends BaseIntegrationTest {
    private static final String PASSWORD = "gumtree123";

    @Autowired
    private PasswordService passwordService;

    @Autowired
    private UserService userService;

    @Autowired
    private ResetPasswordKeyRepository resetPasswordKeyRepository;

    @Autowired
    private UserCredentialsRepository userCredentialsRepository;

    @Autowired
    private UserRepository userRepository;

    @Test
    @Transactional
    public void createsResetPasswordKeyCorrectlyForKnownUser() {
        User user = userService.getUser("<EMAIL>");
        ResetPasswordKey resetPasswordKey = passwordService.initiatePasswordReset(user, true, ResetPasswordReason.USER);
        resetPasswordKeyRepository.flush();
        assertThat(resetPasswordKey.getUser().getEmailAddress().getEmail(), equalTo("<EMAIL>"));
        assertThat(resetPasswordKey.getKey(), is(not(nullValue())));
        assertThat(resetPasswordKey.getExpiryDate().toDateMidnight(), equalTo(
                new DateTime().plusDays(7).toDateMidnight()));
    }

    @Test(expected = UserNotFoundException.class)
    @Transactional
    public void throwsUserNotFoundExceptionCreatingResetPasswordKeyForUnknownUser() {
        User user = userService.getUser("unknownUser");
        passwordService.initiatePasswordReset(user, true, ResetPasswordReason.USER);
    }

    @Test(expected = ResetPasswordKeyUnrecognisedException.class)
    @Transactional
    public void throwsResetPasswordKeyUnrecognisedExceptionWhenKeyNotRecognised() {
        passwordService.updatePassword("unknownKey", "somePassword");
    }

    @Test(expected = ResetPasswordKeyExpiredException.class)
    @Transactional
    public void throwsResetPasswordKeyExpiredExceptionWhenKeyHasExpired() {
        passwordService.updatePassword("TEST_PASSWORD_KEY", "somePassword");
    }

    @Test
    public void shouldResetPasswordForUsrWithUsrCredentials() {
        String username = "<EMAIL>";
        passwordIsUpdatedAndKeyIsRemovedForValidKey(username);
    }

    @Test
    public void shouldResetPasswordForUsrWithoutUsrCredentials() {
        String username = "<EMAIL>";
        passwordIsUpdatedAndKeyIsRemovedForValidKey(username);
    }

    public void passwordIsUpdatedAndKeyIsRemovedForValidKey(String username) {
        User initialUser = userService.getUser(username);
        ResetPasswordKey key = passwordService.initiatePasswordReset(initialUser, true, ResetPasswordReason.USER);

        // when
        String newPassword = "newPassword";
        User user = passwordService.updatePassword(key.getKey(), newPassword);

        // then
        assertThat(userRepository.findByUsername(username).verifyPassword(newPassword), equalTo(true));
        assertThat(resetPasswordKeyRepository.findByKey(key.getKey()), is(nullValue()));
        assertThat(user.getEmailAddress().getEmail(), equalTo(username));
        Assertions.assertThat(userCredentialsRepository.findByUsername(username)).isNotNull();
        assertThat(passwordService.verifyCredentials(user, newPassword).isPresent(), equalTo(true));

    }

    @Test(expected = UserAccountNotActiveException.class)
    public void testExceptionThrownWhenAttemptingToResetPasswordWithNonActiveAccount() {
        User user = userService.getUser("<EMAIL>");
        passwordService.initiatePasswordReset(user, true, ResetPasswordReason.USER);
    }

    @Test
    public void testChangePassword() {
        long userId = 4L;

        // given
        Assertions.assertThat(resetPasswordKeyRepository.findByUserId(userId)).isNotEmpty();

        // when
        passwordService.changePassword(userId, "pass", true);

        // then
        User user = userRepository.findOne(userId);
        assertThat(passwordService.verifyCredentials(user, "pass").isPresent(), equalTo(true));

        // and
        Assertions.assertThat(resetPasswordKeyRepository.findByUserId(userId)).isEmpty();
    }

    @Test
    public void testValidateCredentials() {
        String username = generateUsername();

        // given
        userCredentialsRepository.save(UserCredentials.create(username, PASSWORD));
        User user = createUser(username, PASSWORD);

        // when
        boolean valid = passwordService.verifyCredentials(user, PASSWORD).isPresent();

        // then
        assertThat(valid, equalTo(true));
    }

    /**
     * It's not clear what this test is trying to do..
     */
    @Test
    public void testValidateCredentialsButResetPasswordKeyExists() {
        String username = "<EMAIL>";

        // given
        userCredentialsRepository.save(UserCredentials.create(username, PASSWORD));
        User user = userService.getUser(username);
        passwordService.initiatePasswordReset(user, true, ResetPasswordReason.USER);
        resetPasswordKeyRepository.flush();
        Assertions.assertThat(resetPasswordKeyRepository.findByUserId(user.getId())).isNotEmpty();

        // when
        boolean valid = passwordService.verifyCredentials(user, PASSWORD).isPresent();

        // then
        assertThat(valid, equalTo(true));

        //when
        passwordService.initiatePasswordReset(user, true, ResetPasswordReason.USER); //reset pass again
        resetPasswordKeyRepository.flush();

        //then
        Assertions.assertThat(resetPasswordKeyRepository.findByUserId(user.getId())).isNotEmpty();
        Assertions.assertThat(resetPasswordKeyRepository.findByUserId(user.getId())).hasSize(1); //only one key
    }

    @Test
    public void testExistingCredentialsInSyncWithUserTable() {
        String username = generateUsername();

        // given credentials in sync with usr table (meaning version > 0)
        UserCredentials credentials = UserCredentials.create(username, PASSWORD);
        credentials.setVersion(3L);
        userCredentialsRepository.save(credentials);

        // and
        User user = createUser(username, null);

        // when
        Optional<Long> verified = passwordService.verifyCredentials(user, PASSWORD);

        // then
        Assertions.assertThat(verified).isEqualTo(Optional.of(3L));

        // and credentials are not update (as they are already in sync)
        UserCredentials userCredentials = userCredentialsRepository.findByUsername(username);
        Assertions.assertThat(userCredentials.getVersion()).isEqualTo(3L);
    }

    @Test
    public void testValidateWrongPassword() {
        String username = generateUsername();

        // given
        userCredentialsRepository.save(UserCredentials.create(username, PASSWORD));
        User user = createUser(username, PASSWORD);

        // when
        boolean valid = passwordService.verifyCredentials(user, "wrong-password").isPresent();

        // then
        Assertions.assertThat(valid).isEqualTo(false);
    }

    @Test
    public void getCredentialsVersionForUnknownUsername() {
        // when
        Optional<Long> version = passwordService.getCredentialsVersion("unknown");

        // then
        Assertions.assertThat(version).isEqualTo(Optional.<Long>absent());
    }

    @Test
    public void getCredentialsVersionForKnownUser() {
        String username = "<EMAIL>";

        // given
        userCredentialsRepository.save(UserCredentials.create(username, PASSWORD));

        // when
        Optional<Long> version = passwordService.getCredentialsVersion(username);

        // then
        Assertions.assertThat(version).isEqualTo(Optional.of(0L));
    }

    @Test
    public void getCredentialsVersionForKnownUserWithResetPasswordKey() {
        // given
        User user = userService.getUser("<EMAIL>");
        passwordService.initiatePasswordReset(user, true, ResetPasswordReason.USER);
        resetPasswordKeyRepository.flush();

        // given
        userCredentialsRepository.save(UserCredentials.create(user.getEmailAddress().getEmail(), PASSWORD));

        // when
        Optional<Long> version = passwordService.getCredentialsVersion(user.getEmailAddress().getEmail());

        // then
        Assertions.assertThat(version).isEqualTo(Optional.of(0L));
    }

    @Test
    public void shouldNotSaveCredentialsIfPasswordIsBlank() {
        // when
        passwordService.saveCredentials("username", null);

        // then
        Assertions.assertThat(userCredentialsRepository.findByUsername("username")).isNull();
    }

    @Test
    public void shouldSaveCredentials() {
        // when
        passwordService.saveCredentials("muddleduser1", "thatisjoke");

        // then
        Assertions.assertThat(userCredentialsRepository.findByUsername("muddleduser1")).isNotNull();
    }

    @Test
    public void testReplacePasswordWithRandomlyGeneratedPassword() {
        // given
        String username = "<EMAIL>";
        User user = userService.getUser(username);
        Assertions.assertThat(userCredentialsRepository.findByUsername(username)).isNull();

        // when
        passwordService.replacePasswordWithRandomlyGeneratedPassword(user.getId());

        // then
        UserCredentials newCredentials = userCredentialsRepository.findByUsername(username);
        Assertions.assertThat(newCredentials.getUsername()).isEqualTo(username);
        Assertions.assertThat(newCredentials.getPassword()).isNotNull();
        Assertions.assertThat(newCredentials.getPassword()).isNotEqualTo(user.getPassword());
        Assertions.assertThat(newCredentials.getSalt()).isNotNull();
        Assertions.assertThat(newCredentials.getSalt()).isNotEqualTo(user.getSalt());
        Assertions.assertThat(newCredentials.getVersion()).isEqualTo(0L);
    }

    private User createUser(String username, String password) {
        User user = new User.Builder().withId(1L).withEmailAddress(new EmailAddress.Builder().withEmail(username)).build();
        if (password != null) {
            user.setPassword(password);
        }
        return user;
    }

    private static final AtomicInteger ID_GEN = new AtomicInteger();
    private String generateUsername() {
        return "test" + ID_GEN.getAndIncrement() + "@test.com";
    }
}
