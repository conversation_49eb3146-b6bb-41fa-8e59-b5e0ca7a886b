package com.gumtree.integration.service.expiry;

import com.gumtree.api.service.expiry.ArchiveDraftAdvertsServiceImpl;
import com.gumtree.test.CleanInsertTestExecutionListener;
import com.gumtree.test.DataSetLocation;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;

import java.util.List;
import java.util.Properties;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        locations = "expiry-jobs-service-integration-test-context.xml",
        initializers = ArchiveDraftAdvertsServiceIntegrationTest.TestContextInitializer.class
)
@TestExecutionListeners(
        {
                DependencyInjectionTestExecutionListener.class,
                CleanInsertTestExecutionListener.class,
                TransactionalTestExecutionListener.class
        }
)
@DataSetLocation(value = "classpath:/com/gumtree/api/service/expiry/archive-draft-adverts-service-test-dataset.xml", dataSourceName = "sellerDatasource")
public class ArchiveDraftAdvertsServiceIntegrationTest {
    @Autowired
    private ArchiveDraftAdvertsServiceImpl draftAdvertsService;

    private List<Long> idsToExpire;

    @Before
    public void init() {
        idsToExpire = draftAdvertsService.getIdsToExpire(2);
    }

    @Test
    public void whenAdvertIsDraftAndCreatedTooLongAgo() {
        assertThat(idsToExpire.contains(9L), equalTo(true));
    }

    public static class TestContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public final void initialize(ConfigurableApplicationContext appContext) {
            Properties properties = new Properties();
            properties.setProperty("gumtree.seller.jobs.archive.draftdays", "0");
            ConfigurationManager.loadProperties(properties);
        }

    }
}
