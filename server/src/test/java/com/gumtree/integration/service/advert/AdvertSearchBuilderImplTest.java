package com.gumtree.integration.service.advert;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.advert.query.AdvertQuery;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.repository.advert.AdvertRepository;
import com.gumtree.seller.service.advert.AdvertSearchBuilder;
import com.gumtree.seller.service.advert.impl.AdvertSearchBuilderImpl;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.test.DataSetLocation;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;

import java.util.Iterator;
import java.util.List;
import java.util.function.IntConsumer;
import java.util.stream.IntStream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.not;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/advert-search.xml", dataSourceName="sellerDatasource")
public class AdvertSearchBuilderImplTest extends BaseIntegrationTest {

    @Autowired
    AdvertRepository advertRepository;

    @Test
    public void getAllAdverts() {
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder.results();
        assertThat(results.getNumberOfElements(),equalTo(8));
        assertThat(results.getTotalElements(),equalTo(8L));
    }

    @Test
    public void getAllAdvertsForAccount() {
        Long accountId = 1L;
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder
                .forAccount(accountId)
                .results();
        assertThat(results.getNumberOfElements(),equalTo(6));
        assertThat(results.getTotalElements(),equalTo(6L));
    }

    @Test
    public void getLiveAdverts() {
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder
                .withStatus(AdvertStatus.LIVE)
                .results();
        assertThat(results.getNumberOfElements(),equalTo(4));
        assertThat(results.getTotalElements(),equalTo(4L));
    }

    @Test
    public void getLiveAndDraftAdverts() {
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder
                .withStatus(AdvertStatus.LIVE)
                .withStatus(AdvertStatus.DRAFT)
                .results();
        assertThat(results.getNumberOfElements(),equalTo(5));
        assertThat(results.getTotalElements(),equalTo(5L));
    }

    @Test
    public void getFirstPageOfAdvertsForAccount() {
        Long accountId = 1L;
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder
                .forAccount(accountId)
                .withNumberPerPage(2)
                .results();
        assertThat(results.getNumberOfElements(),equalTo(2));
        assertThat(results.getTotalElements(),equalTo(6L));
    }

    @Test
    public void getAdvertsOrderedByLastModifiedDate() {
        Long accountId = 1L;
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder
                .forAccount(accountId).withNumberPerPage(100)
                .orderedByLastModifiedDate()
                .results();
        assertThat(results.getNumberOfElements(),equalTo(6));
        assertThat(results.getTotalElements(),equalTo(6L));

        DateTime current = null;

        for(Advert advert : results.getContent() ){
            if(current==null) {
                current = advert.getLastModifiedDate();
            } else {
                assertThat(current.compareTo(advert.getLastModifiedDate()),not(equalTo(-1)));
                current = advert.getLastModifiedDate();
            }
        }
    }

    @Test
    public void getAdvertsForAdvertQuery() {
        AdvertQuery advertQuery = new AdvertQuery();
        advertQuery.setAccountId(1L);
        advertQuery.setStatuses(Sets.<AdvertStatus>newHashSet(AdvertStatus.LIVE));
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder
                .forQuery(advertQuery)
                .results();
        assertThat(results.getNumberOfElements(),equalTo(3));
        assertThat(results.getTotalElements(),equalTo(3L));
    }

    @Test
    public void getAdvertsSortedByStatus() {
        AdvertQuery advertQuery = new AdvertQuery();
        advertQuery.setAccountId(1L);
        advertQuery.setStatuses(Sets.newLinkedHashSet(
                Lists.newArrayList(AdvertStatus.LIVE, AdvertStatus.NEEDS_EDITING, AdvertStatus.DELETED_USER)));
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder
                .forQuery(advertQuery)
                .orderedByStatus(true)
                .results();
        List<Advert> adverts = results.getContent();
        IntStream.rangeClosed(0, 2).forEach(value -> assertThat(adverts.get(value).getStatus(), equalTo(AdvertStatus.LIVE)));
        IntStream.rangeClosed(3, 4).forEach(value -> assertThat(adverts.get(value).getStatus(), equalTo(AdvertStatus.NEEDS_EDITING)));
        assertThat(adverts.get(5).getStatus(), equalTo(AdvertStatus.DELETED_USER));
    }

    @Test
    public void getAdvertsSortedByStatusAndLastModifiedDate() {
        AdvertQuery advertQuery = new AdvertQuery();
        advertQuery.setAccountId(1L);
        advertQuery.setStatuses(Sets.newLinkedHashSet(
                Lists.newArrayList(AdvertStatus.NEEDS_EDITING, AdvertStatus.LIVE, AdvertStatus.DELETED_USER)));
        AdvertSearchBuilder advertSearchBuilder = new AdvertSearchBuilderImpl(advertRepository);
        Page<Advert> results = advertSearchBuilder
                .forQuery(advertQuery)
                .orderedByStatus(true)
                .orderedByLastModifiedDate()
                .results();
        Iterator<Advert> adverts = results.getContent().iterator();
        assertAdvertIdAndStatus(adverts.next(), 4L, AdvertStatus.NEEDS_EDITING);
        assertAdvertIdAndStatus(adverts.next(), 5L, AdvertStatus.NEEDS_EDITING);
        assertAdvertIdAndStatus(adverts.next(), 2L, AdvertStatus.LIVE);
        assertAdvertIdAndStatus(adverts.next(), 3L, AdvertStatus.LIVE);
        assertAdvertIdAndStatus(adverts.next(), 1L, AdvertStatus.LIVE);
        assertAdvertIdAndStatus(adverts.next(), 8L, AdvertStatus.DELETED_USER);
    }

    private void assertAdvertIdAndStatus(Advert advert, Long id, AdvertStatus advertStatus) {
        assertThat(advert.getId(), equalTo(id));
        assertThat(advert.getStatus(), equalTo(advertStatus));
    }
}
