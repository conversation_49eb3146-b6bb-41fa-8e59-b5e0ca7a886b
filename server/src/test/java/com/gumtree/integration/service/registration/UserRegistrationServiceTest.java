package com.gumtree.integration.service.registration;

import com.gumtree.seller.domain.user.CreateUserCommand;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.entity.UserActivationKey;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.seller.repository.advert.AdvertRepository;
import com.gumtree.seller.repository.user.UserActivationKeyRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.service.registration.UserRegistrationService;
import com.gumtree.seller.service.user.UserService;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/service/user/user-service-test-dataset.xml", dataSourceName="sellerDatasource")
public class UserRegistrationServiceTest extends BaseIntegrationTest {

    @Autowired
    UserRegistrationService registrationService;

    @Autowired
    UserActivationKeyRepository activationKeyRepository;

    @Autowired
    UserRepository userRepository;

    @Autowired
    UserService userService;

    @Autowired
    AdvertRepository advertRepository;

    @Test
    @Transactional
    public void registerUserPersistsDetailsAndCreatesActivationKey() {
        CreateUserCommand request = new CreateUserCommand();
        request.setFirstName("Roger");
        request.setLastName("Climpson");
        request.setEmailAddress("<EMAIL>");
        request.setContactPhone(" 01632 960001");
        request.setPlainTextPassword("funnyman31");
        request.setOptInMarketing(true);

        // when
        User user = registrationService.registerUser(request);
        userRepository.flush();

        // then
        UserActivationKey activationKey = activationKeyRepository.findByUserId(user.getId());
        assertThat(activationKey.getUser().getUsername(), equalTo("<EMAIL>"));
        assertThat(activationKey.getKey(), is(not(nullValue())));
        assertThat(activationKey.getExpiryDate(), is(not(nullValue())));
    }

    @Test
    @Transactional
    public void activateUserSuccesfullyFlagsUserAsActiveAndDeletesKey() {
        User user = registrationService.activateUser("TESTKEY101");
        userRepository.flush();
        activationKeyRepository.flush();
        user = userService.getUser(user.getId());
        assertThat(user.getStatus(), equalTo(UserStatus.ACTIVE));
        assertThat(activationKeyRepository.exists(1L), equalTo(false));
    }
}
