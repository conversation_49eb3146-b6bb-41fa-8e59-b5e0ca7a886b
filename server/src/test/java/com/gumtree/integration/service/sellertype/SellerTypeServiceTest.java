package com.gumtree.integration.service.sellertype;

import com.gumtree.api.category.domain.Category;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.domain.sellertype.dto.SellerTypeInfo;
import com.gumtree.seller.domain.sellertype.dto.SellerTypeEnum;
import com.gumtree.seller.domain.sellertype.entity.SellerType;
import com.gumtree.seller.repository.sellertype.SellerTypeRepository;
import com.gumtree.seller.service.sellertype.SellerTypeService;
import com.gumtree.seller.service.sellertype.exception.SellerTypeAdvertLimitExceeded;
import com.gumtree.test.DataSetLocation;
import org.fest.assertions.api.Assertions;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.lang.Boolean.TRUE;


@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/seller-type.xml", dataSourceName = "sellerDatasource")
public class SellerTypeServiceTest extends BaseIntegrationTest {

    private static final String PRIVATE_TYPE = "PRIVATE";
    private static final String TRADE_TYPE = "TRADE";
    private static final long ACCOUNT_ID_1 = 68463646L;
    private static final long ACCOUNT_ID_2 = 53122314L;
    private static final long ACCOUNT_ID_NON_EXIST = ********;
    private static final long ACCOUNT_ID_PRO = ********;
    private static final long PROPERTY_CAT_ID = 10201L;
    private static final long THREE_BEDROOM_TO_RENT_CAT_ID = 126L;
    private static final long MATTRESSES_CAT_ID = 4610L;
    private static final long BICYCLES_CAT_ID = 174L;
    private static final int ADVERT_LIMIT_PROPERTIES = 2;
    private static final int ADVERT_LIMIT_MATTRESSES= 1;

    @Autowired
    private SellerTypeService sellerTypeService;

    @Autowired
    private SellerTypeRepository sellerTypeRepository;

    @Test
    public void testListingCaps() {
        // When
        Map<Long, Integer> listingCaps = sellerTypeService.getListingCaps();

        // Then
        String cats = listingCaps.keySet().stream().map(Object::toString).collect( Collectors.joining( "," ) );
        Assertions.assertThat(cats).isEqualTo("4610,10201");

        Assertions.assertThat(listingCaps.size()).isEqualTo(2);
        Assertions.assertThat(listingCaps.get(PROPERTY_CAT_ID)).isEqualTo(ADVERT_LIMIT_PROPERTIES);
        Assertions.assertThat(listingCaps.get(MATTRESSES_CAT_ID)).isEqualTo(ADVERT_LIMIT_MATTRESSES);
    }

    @Test
    public void testFindListingCapCategoryNotFound() {
        // When
        // Searching for a category that is not linked to listing caps
        Optional<Category> category = sellerTypeService.findListingCapCategory(BICYCLES_CAT_ID);

        // Then
        Assertions.assertThat(category.isPresent()).isFalse();
    }

    @Test
    public void testFindListingCapCategoryFound() {
        // When
        // Searching for a category that is linked to listing caps (Property)
        Optional<Category> category = sellerTypeService.findListingCapCategory(THREE_BEDROOM_TO_RENT_CAT_ID);

        // Then
        Assertions.assertThat(category.isPresent()).isTrue();
        Assertions.assertThat(category.get().getId()).isEqualTo(PROPERTY_CAT_ID);
    }

    @Test
    public void testGetSellerTypeNonSellerTypeCategory() {
        // When
        // Querying for seller type in category not linked to seller type
        Optional<SellerTypeInfo> sellerTypeDto = sellerTypeService.getSellerType(ACCOUNT_ID_1, BICYCLES_CAT_ID);

        // Then
        Assertions.assertThat(sellerTypeDto.isPresent()).isFalse();
    }

    @Test
    public void testGetSellerTypeNonExistingAccount() {
        // When
        // Querying for an account that does not exist in the seller-type table should default to PRIVATE
        Optional<SellerTypeInfo> sellerTypeDto = sellerTypeService.getSellerType(ACCOUNT_ID_NON_EXIST, THREE_BEDROOM_TO_RENT_CAT_ID);

        // Then
        Assertions.assertThat(sellerTypeDto.isPresent()).isTrue();
        Assertions.assertThat(sellerTypeDto.get().getType().name()).isEqualTo(PRIVATE_TYPE);
        Assertions.assertThat(sellerTypeDto.get().getAdverts()).isEqualTo(0);
        Assertions.assertThat(sellerTypeDto.get().getCategoryId()).isEqualTo(PROPERTY_CAT_ID);
        Assertions.assertThat(sellerTypeDto.get().getAdvertsLimit()).isEqualTo(2);
    }

    @Test
    public void testGetSellerTypeExceedLimit() {
        // When
        // Querying for a PRO-account should default to TRADE
        Optional<SellerTypeInfo> sellerTypeDto = sellerTypeService.getSellerType(ACCOUNT_ID_2, MATTRESSES_CAT_ID);

        // Then
        Assertions.assertThat(sellerTypeDto.isPresent()).isTrue();
        Assertions.assertThat(sellerTypeDto.get().getType().name()).isEqualTo(TRADE_TYPE);
        Assertions.assertThat(sellerTypeDto.get().getAdverts()).isEqualTo(3);
        Assertions.assertThat(sellerTypeDto.get().getCategoryId()).isEqualTo(MATTRESSES_CAT_ID);
        Assertions.assertThat(sellerTypeDto.get().getAdvertsLimit()).isEqualTo(1);
    }

    @Test
    public void testGetSellerTypeProAccount() {
        // When
        // Querying for account that exceeded advert limit should default to TRADE
        Optional<SellerTypeInfo> sellerTypeDto = sellerTypeService.getSellerType(ACCOUNT_ID_PRO, THREE_BEDROOM_TO_RENT_CAT_ID);

        // Then
        Assertions.assertThat(sellerTypeDto.isPresent()).isTrue();
        Assertions.assertThat(sellerTypeDto.get().getType().name()).isEqualTo(TRADE_TYPE);
        Assertions.assertThat(sellerTypeDto.get().getAdverts()).isEqualTo(0);
        Assertions.assertThat(sellerTypeDto.get().getCategoryId()).isEqualTo(PROPERTY_CAT_ID);
        Assertions.assertThat(sellerTypeDto.get().getAdvertsLimit()).isEqualTo(2);
    }


    @Test
    public void testGetSellerType() {
        // when
        Optional<SellerTypeInfo> sellerTypeDto = sellerTypeService.getSellerType(ACCOUNT_ID_1, THREE_BEDROOM_TO_RENT_CAT_ID);

        // then
        Assertions.assertThat(sellerTypeDto.isPresent()).isTrue();
        Assertions.assertThat(sellerTypeDto.get().getType().name()).isEqualTo(TRADE_TYPE);
        Assertions.assertThat(sellerTypeDto.get().getAdverts()).isEqualTo(1);
        Assertions.assertThat(sellerTypeDto.get().getCategoryId()).isEqualTo(PROPERTY_CAT_ID);
        Assertions.assertThat(sellerTypeDto.get().getAdvertsLimit()).isEqualTo(2);

        // and
        Optional<SellerType> sellerTypeOpt = sellerTypeRepository.findByAccountAndCategoryId(ACCOUNT_ID_1, PROPERTY_CAT_ID);
        Assertions.assertThat(sellerTypeOpt.isPresent()).isEqualTo(TRUE);
        SellerType sellerType = sellerTypeOpt.get();
        Assertions.assertThat(sellerType.getType().getDisplayValue()).isEqualTo(TRADE_TYPE);
        Assertions.assertThat(sellerType.getCategoryId()).isEqualTo(PROPERTY_CAT_ID);
        Assertions.assertThat(sellerType.getAccountId()).isEqualTo(ACCOUNT_ID_1);
    }

    @Test
    public void testUpdateSellerType() {
        sellerTypeService.updateSellerType(SellerTypeEnum.PRIVATE, ACCOUNT_ID_1, THREE_BEDROOM_TO_RENT_CAT_ID);
        Optional<SellerType> sellerTypeOpt = sellerTypeRepository.findByAccountAndCategoryId(ACCOUNT_ID_1, PROPERTY_CAT_ID);
        Assertions.assertThat(sellerTypeOpt.isPresent()).isEqualTo(TRUE);
        SellerType sellerType = sellerTypeOpt.get();
        Assertions.assertThat(sellerType.getType().getDisplayValue()).isEqualTo(PRIVATE_TYPE);
    }

    public void testUpdateSellerTypePrivateAsProAccount() {
        sellerTypeService.updateSellerType(SellerTypeEnum.PRIVATE, ACCOUNT_ID_PRO, THREE_BEDROOM_TO_RENT_CAT_ID);
        Optional<SellerTypeInfo> sellerTypeOpt = sellerTypeService.getSellerType(ACCOUNT_ID_PRO, THREE_BEDROOM_TO_RENT_CAT_ID);
        Assertions.assertThat(sellerTypeOpt.isPresent()).isEqualTo(TRUE);
        Assertions.assertThat(sellerTypeOpt.get().getAdverts()).isEqualTo(3);
        Assertions.assertThat(sellerTypeOpt.get().getAdvertsLimit()).isEqualTo(3);
    }
}
