package com.gumtree.integration.service.order;

import com.gumtree.api.mvc.profile.IntegrationTests;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.advert.entity.AdvertFeature;
import com.gumtree.seller.domain.advert.exception.AdvertAlreadyFeaturedException;
import com.gumtree.seller.domain.advert.exception.AdvertNotYetPublishedException;
import com.gumtree.seller.domain.advert.exception.AdvertRemovedException;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.domain.image.exception.ImageNotFoundException;
import com.gumtree.seller.domain.order.MigrateOrderItemRequest;
import com.gumtree.seller.domain.order.MigrateOrderRequest;
import com.gumtree.seller.domain.order.MigratePaymentDetail;
import com.gumtree.seller.domain.order.OrderItemRequest;
import com.gumtree.seller.domain.order.OrderRequest;
import com.gumtree.seller.domain.order.entity.Order;
import com.gumtree.seller.domain.order.entity.OrderItem;
import com.gumtree.seller.domain.order.exception.OrderNotFoundException;
import com.gumtree.seller.domain.order.status.OrderItemStatus;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.seller.domain.payment.entity.PaymentMethod;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.repository.advert.AdvertRepository;
import com.gumtree.seller.repository.order.OrderItemRepository;
import com.gumtree.seller.repository.order.OrderRepository;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.service.order.create.CreateMigratedOrderService;
import com.gumtree.seller.service.order.create.CreateOrderService;
import com.gumtree.seller.service.order.execute.ExecuteOrderService;
import com.gumtree.seller.service.order.expire.ExpiryOrderService;
import com.gumtree.seller.service.order.read.ReadOrderService;
import com.gumtree.test.CleanInsertTestExecutionListener;
import com.gumtree.test.DataSetLocation;
import com.gumtree.wallet.packages.domain.entity.CreditPackage;
import com.gumtree.wallet.packages.repository.CreditPackageRepository;
import com.gumtree.wallet.packages.repository.PackagePaymentRepository;
import org.fest.assertions.api.Assertions;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.core.AnyOf.anyOf;


@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/advert-features.xml", dataSourceName="sellerDatasource")
@Category({IntegrationTests.class})
@RunWith(SpringJUnit4ClassRunner.class)
@TestExecutionListeners(
        {
                DependencyInjectionTestExecutionListener.class,
                CleanInsertTestExecutionListener.class,
                TransactionalTestExecutionListener.class
        }
)
public class OrderServiceIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private CreateOrderService createOrderService;

    @Autowired
    private CreateMigratedOrderService createMigratedOrderService;

    @Autowired
    private ReadOrderService readOrderService;

    @Autowired
    private ExecuteOrderService executeOrderService;

    @Autowired
    private ExpiryOrderService expiryOrderService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderItemRepository orderItemRepository;

    @Autowired
    private CreditPackageRepository creditPackageRepository;

    @Autowired
    private PackagePaymentRepository packagePaymentRepository;

    @Autowired
    AdvertService advertService;

    @Autowired
    AdvertRepository advertRepository;

    private final long liveAdvertId = 888L;

    @Before
    public void beforeEach() {
        setSubjectWithPermissionAndPrincipal("account:manageads:1", "<EMAIL>");
    }

    @Test
    public void verifyFindInsertionOrderByAdvertId(){
    List<OrderItem> orderItems = orderItemRepository.findByAdvertIdAndProductName(222L, ProductName.INSERTION);
        assertThat(orderItems.size(), equalTo(1));
    }

    @Test
    public void verifyCreateOrderPersistsOrder() throws Exception {
        OrderRequest orderRequest = orderRequest(111L, ProductName.URGENT);

        // when
        Order order = createOrderService.createOrder(orderRequest);

        // then
        order = readOrderService.getOrder(order.getId());
        assertThat(order, notNullValue());
        assertThat(order.getAccount().getId(), equalTo(1L));
        assertThat(order.getOrderItems().get(0).getStatus(), equalTo(OrderItemStatus.UNPAID));
    }

    @Test
    public void verifyCreateOrderDoesNotPersistSingleFreeInsertionOrder() throws Exception {
        OrderRequest orderRequest = orderRequest(444L, ProductName.INSERTION);
        Long numberOrders = orderRepository.count();
        Order order = createOrderService.createOrder(orderRequest);
        assertThat(readOrderService.isFreeOrder(order), equalTo(true));
        assertThat(orderRepository.count(), equalTo(numberOrders));
    }

    @Test(expected = AdvertNotYetPublishedException.class)
    public void verifyCreateOrderFailsForBumpupOnDraftAdvert() throws Exception {
        OrderRequest orderRequest = orderRequest(555L, ProductName.BUMP_UP);
        createOrderService.createOrder(orderRequest);
    }

    @Test
    public void executeOrderUpdatesOrderItems() throws Exception {
        Order order = createOrderService.createOrder(orderRequest(111L, ProductName.BUMP_UP));

        // when
        executeOrderService.executeOrder(order.getId());

        // then
        order = readOrderService.getOrder(order.getId());
        assertThat(order.getStatus(), equalTo(OrderStatus.PAID));
        assertThat(order.getOrderItems().get(0).getId(), notNullValue());
        assertThat(order.getOrderItems().get(0).getStatus(), equalTo(OrderItemStatus.ACTIONED));
    }

    @Ignore
    @Test
    public void executeOrderPersistsOrderItemPrices() throws Exception {
        Order order = createOrderService.createOrder(orderRequest(333L, ProductName.FEATURE_7_DAY));

        // when
        executeOrderService.executeOrder(order.getId());

        // then
        order = readOrderService.getOrder(order.getId());
        assertThat(order.getStatus(), equalTo(OrderStatus.UNPAID));
        assertThat(order.getOrderItems().get(0).getId(), notNullValue());
        assertThat(order.getOrderItems().get(0).getStatus(), equalTo(OrderItemStatus.UNPAID));
        assertThat(order.getOrderItems().get(0).getPriceIncVat(), equalTo(950L));
    }

    @Test
    public void createOrderHasNoPackagePayment() throws Exception {
        OrderRequest orderRequest = orderRequest(111L, ProductName.URGENT);

        // when
        Order order = createOrderService.createOrder(orderRequest);

        // then
        order = readOrderService.getOrder(order.getId());
        assertThat(order.getOrderItems().get(0).getPackagePayment(), nullValue());
    }

    @Test
    public void executeOrderSetsFulfillmentDateWhenAllItemsFulFilled() throws Exception {
        OrderRequest orderRequest = orderRequest(111L, ProductName.BUMP_UP);

        Order order = createOrderService.createOrder(orderRequest);
        executeOrderService.executeOrder(order.getId());
        order = orderRepository.findOne(order.getId());
        assertThat(order.getFulfillmentDate(), notNullValue());
    }

    @Test
    public void executeOrderDoesNotSetFulfillmentDateIfProductIsNotYetFulfilled() throws Exception {
        OrderRequest orderRequest = orderRequest(112L, ProductName.URGENT);

        Order order = createOrderService.createOrder(orderRequest);

        // when
        executeOrderService.executeOrder(order.getId());

        // then
        order = readOrderService.getOrder(order.getId());
        assertThat(order.getFulfillmentDate(), nullValue());
    }

    @Test
    public void executeOrderSetsPaymentDateForCreditPackagePayment() throws Exception {
        OrderRequest orderRequest = orderRequest(111L, ProductName.URGENT);

        Order order = createOrderService.createOrder(orderRequest);

        // when
        executeOrderService.executeOrder(order.getId());

        // then
        order = readOrderService.getOrder(order.getId());
        assertThat(order.getOrderItems().get(0).getPackagePayment().getPaymentDate(), notNullValue());
    }

    @Test
    public void executeOrderForAwaitingScreeningAdWithBumpUpShouldStillPutAdLive() throws Exception {
        // when
        executeOrderService.executeOrder(101011L);

        // then
        Order order = readOrderService.getOrder(101011L);
        assertThat(order.getStatus(), equalTo(OrderStatus.PAID));
        assertThat(order.getOrderItems().get(0).getId(), notNullValue());
        assertThat(order.getOrderItems().get(0).getStatus(), equalTo(OrderItemStatus.ACTIONED));
    }

    @Test(expected = AdvertRemovedException.class)
    public void createOrderWithFeatureToExpiredThrowsException() throws Exception {
        OrderRequest orderRequest = orderRequest(222L, ProductName.URGENT);

        createOrderService.createOrder(orderRequest);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void createOrderWithDuplicateFeatureThrowsException() throws Exception {
        OrderRequest orderRequest = orderRequest(222L, ProductName.BUMP_UP);

        OrderItemRequest orderItemRequest = new OrderItemRequest();
        orderItemRequest.setAdvertId(222L);
        orderItemRequest.setProductName(ProductName.FEATURE_3_DAY);
        orderRequest.getOrderItemRequests().add(orderItemRequest);

        createOrderService.createOrder(orderRequest);
    }

    @Test(expected = ImageNotFoundException.class)
    public void createOrderWithSpotlightHomepageAndNoImageOnAdvertThrowsException() throws Exception {
        OrderRequest orderRequest = orderRequest(111L, ProductName.HOMEPAGE_SPOTLIGHT);

        createOrderService.createOrder(orderRequest);
    }

    @Test
    public void expireOrderWithUnpaidItemsDeletesOrder() {
        Order order = readOrderService.getOrder(111L);
        assertThat(order.getStatus(), equalTo(OrderStatus.UNPAID));

        // when
        expiryOrderService.expireOrder(111L);

        // then
        Assertions.assertThat(readOrderService.exists(111L)).isFalse();
    }

    @Test(expected = OrderNotFoundException.class)
    public void throwExceptionForNonExistingOrder() {
        // when
        readOrderService.getOrder(9999999L);
    }

    @Test
    public void expireOrderWithPackageItemsRefundsCredits() {
        // given
        Order order = readOrderService.getOrder(222L);
        assertThat(order.getStatus(), equalTo(OrderStatus.UNPAID));

        // when
        expiryOrderService.expireOrder(222L);

        // then
        Assertions.assertThat(readOrderService.exists(222L)).isFalse();
        assertThat(packagePaymentRepository.findOne(212L), is(nullValue()));

        CreditPackage creditPackage = creditPackageRepository.findOne(2L);
        assertThat(creditPackage.getUsedCredits(), equalTo(3L));
    }

    @Test
    public void executeOrderWhenAdvertIsNotLiveDoesNotApplyFeatures() {
        Order order = readOrderService.getOrder(333L);
        assertThat(order.getStatus(), equalTo(OrderStatus.PAID));

        // when
        executeOrderService.executeOrder(333L);

        // then
        order = readOrderService.getOrder(333L);
        assertThat(order.getOrderItems().size(), equalTo(2));
        assertThat(order.getOrderItems().get(0).getStatus(), equalTo(OrderItemStatus.PAID));
        assertThat(order.getOrderItems().get(1).getStatus(), equalTo(OrderItemStatus.PAID));
    }


    @Test
    public void executeOrderWhenAdvertIsLiveAppliesNecessaryFeatures() {
        Order order = readOrderService.getOrder(444L);
        assertThat(order.getStatus(), equalTo(OrderStatus.PAID));

        // when
        executeOrderService.executeOrder(444L);

        // then
        order = readOrderService.getOrder(444L);
        assertThat(order.getOrderItems().size(), equalTo(2));
        assertThat(order.getOrderItems().get(0).getStatus(), equalTo(OrderItemStatus.ACTIONED));
        assertThat(order.getOrderItems().get(1).getStatus(), equalTo(OrderItemStatus.ACTIONED));
    }

    @Test
    public void verifyCreateOrderOnDraftAdvertMakesInsertionItem() throws Exception {
        OrderRequest orderRequest = orderRequest(777L, ProductName.FEATURE_14_DAY);

        // when
        Order order = createOrderService.createOrder(orderRequest);

        // then
        order = readOrderService.getOrder(order.getId());
        assertThat(order, notNullValue());
        assertThat(order.getOrderItems().size(), equalTo(2));
        List<OrderItem> orderItems = order.getOrderItems();
        assertThat(orderItems.get(0).getProduct().getName(), equalTo(ProductName.FEATURE_14_DAY));
        assertThat(orderItems.get(1).getProduct().getName(), equalTo(ProductName.INSERTION));
    }

    @Test
    public void verifyExecuteOrderPersistsAndRetrievesAdvertFeatureWithOrderItemIdWhenSearchingByAdvertId() throws Exception {
        OrderRequest orderRequest = orderRequest(liveAdvertId, ProductName.FEATURE_14_DAY);
        Order order = createOrderService.createOrder(orderRequest);

        //update Order Item Status, this is what would normally happen with a call to the PaymentService to validate a payment.
        simulateValidatePayment(order, liveAdvertId);
        executeOrderService.executeOrder(order.getId(), false);
        List<Order> ordersForAdvert = readOrderService.getOrdersForAdvert(liveAdvertId);

        Order retrievedOrder = ordersForAdvert.get(0);
        assertThat(retrievedOrder, notNullValue());
        assertThat(retrievedOrder.getOrderItems().size(), equalTo(1));
        List<OrderItem> orderItems = retrievedOrder.getOrderItems();
        assertThat(orderItems.get(0).getProduct().getName(), equalTo(ProductName.FEATURE_14_DAY));
        for (OrderItem orderItem  : orderItems) {
            AdvertFeature advertFeature = orderItem.getAdvertFeature();
            assertThat(advertFeature, equalTo(null));
        }
    }

    @Test
    public void verifyExecuteOrderDoesNotPersistAdvertFeatureWhichisFree() throws Exception {
        OrderRequest orderRequest = orderRequest(liveAdvertId, ProductName.BUMP_UP);
        Order order = createOrderService.createOrder(orderRequest);

        //update Order Item Status, this is what would normally happen with a call to the PaymentService to validate a payment.
        simulateValidatePayment(order, liveAdvertId);
        executeOrderService.executeOrder(order.getId(), false);
        List<Order> ordersForAdvert = readOrderService.getOrdersForAdvert(liveAdvertId);

        Order retrievedOrder = ordersForAdvert.get(0);
        assertThat(retrievedOrder, notNullValue());
        assertThat(retrievedOrder.getOrderItems().size(), equalTo(1));
        List<OrderItem> orderItems = retrievedOrder.getOrderItems();
        assertThat(orderItems.get(0).getProduct().getName(), equalTo(ProductName.BUMP_UP));
        for (OrderItem orderItem  : orderItems) {
            AdvertFeature advertFeature = orderItem.getAdvertFeature();
            assertThat(advertFeature,  equalTo(null));
        }
    }

    @Test
    public void verifyExecuteOrderPersistsAndRetrievesMultipleAdvertFeatureWithOrderItemIdWhenSearchingByAdvertId() throws Exception {
        OrderRequest orderRequest = orderRequest(liveAdvertId, ProductName.INSERTION);

        OrderItemRequest orderItemRequest = new OrderItemRequest();
        orderItemRequest.setAdvertId(liveAdvertId);
        orderItemRequest.setProductName(ProductName.FEATURE_3_DAY);

        OrderItemRequest orderItemRequest2 = new OrderItemRequest();
        orderItemRequest2.setAdvertId(liveAdvertId);
        orderItemRequest2.setProductName(ProductName.URGENT);

        orderRequest.getOrderItemRequests().add(orderItemRequest);
        orderRequest.getOrderItemRequests().add(orderItemRequest2);

        Order order = createOrderService.createOrder(orderRequest);

        //update Order Item Status, this is what would normally happen with a call to the PaymentService to validate a payment.
        simulateValidatePayment(order, liveAdvertId);
        executeOrderService.executeOrder(order.getId(), false);
        List<Order> ordersForAdvert = readOrderService.getOrdersForAdvert(liveAdvertId);

        Order retrievedOrder = ordersForAdvert.get(0);
        assertThat(retrievedOrder, notNullValue());
        assertThat(retrievedOrder.getOrderItems().size(), equalTo(3));
        List<OrderItem> orderItems = retrievedOrder.getOrderItems();
        assertThatOrderItemsExist(orderItems);
    }

    private void assertThatOrderItemsExist(List<OrderItem> orderItems) {
        for (OrderItem orderItem  : orderItems) {
            AdvertFeature advertFeature = orderItem.getAdvertFeature();
            assertThat(orderItem.getProduct().getName(), anyOf(is(ProductName.INSERTION), is(ProductName.FEATURE_3_DAY), is(ProductName.URGENT)));
            assertThat(advertFeature,  equalTo(null));
        }
    }

    @Test
    public void verifyExecuteOrderPeristsAndRetrievesAdvertFeatureWithOrderItemIdWhenSearchingByOrderId() throws Exception {
        OrderRequest orderRequest = orderRequest(liveAdvertId, ProductName.FEATURE_14_DAY);
        Order order = createOrderService.createOrder(orderRequest);

        //update Order Item Status, this is what would normally happen with a call to the PaymentService to validate a payment.
        simulateValidatePayment(order, liveAdvertId);
        executeOrderService.executeOrder(order.getId(), false);
        Order foundorder = readOrderService.getOrder(order.getId());

        assertThat(foundorder, notNullValue());
        assertThat(foundorder.getOrderItems().size(), equalTo(1));
        List<OrderItem> orderItems = foundorder.getOrderItems();
        assertThat(orderItems.get(0).getProduct().getName(), equalTo(ProductName.FEATURE_14_DAY));
        for (OrderItem orderItem  : orderItems) {
            AdvertFeature advertFeature = orderItem.getAdvertFeature();
            assertThat(advertFeature, equalTo(null));
        }
    }

    @Test
    public void verifyMigratedOrderWithOneFeaturedItemPersistsOneItemOnly() {
        MigrateOrderRequest orderRequest = new MigrateOrderRequest();
        orderRequest.setAccountId(1L);
        orderRequest.setCreatedDate(new DateTime());
        MigrateOrderItemRequest orderItemRequest = new MigrateOrderItemRequest();
        orderItemRequest.setAdvertId(liveAdvertId);
        orderItemRequest.setProductName(ProductName.FEATURE_14_DAY);
        DateTime expiryDate = new DateTime(2016, 12, 12, 12, 12, 12, 12);
        orderItemRequest.setFeatureExpireDate(expiryDate);
        orderItemRequest.setStatus(OrderItemStatus.ACTIONED);
        MigratePaymentDetail paymentDetail = new MigratePaymentDetail();
        paymentDetail.setAmount(123L);
        paymentDetail.setPaymentMethod(PaymentMethod.LEGACY_PAYPAL_ACCOUNT);
        paymentDetail.setPaymentDate(new DateTime());
        paymentDetail.setReference("ABCD-REF");
        paymentDetail.setVatAmount(23L);
        orderItemRequest.setPaymentDetail(paymentDetail);
        orderRequest.setItems(Arrays.asList(orderItemRequest));

        // when
        createMigratedOrderService.createMigratedOrder(orderRequest);

        // then
        List<OrderItem> items =  orderItemRepository.findByAdvertId(liveAdvertId);
        assertThat(items.size(), equalTo(1));
    }

    private OrderRequest orderRequest(Long advertId, ProductName productName) {
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.setAccountId(1L);

        OrderItemRequest orderItemRequest = new OrderItemRequest();
        orderItemRequest.setAdvertId(advertId);
        orderItemRequest.setProductName(productName);
        List<OrderItemRequest> orderItemRequests = new ArrayList<>();
        orderItemRequests.add(orderItemRequest);
        orderRequest.setOrderItemRequests(orderItemRequests);
        return orderRequest;
    }

    private void simulateValidatePayment(Order order, Long advertId) {
        Advert advert = advertService.getAdvert(advertId);
        advert.setStatus(AdvertStatus.AWAITING_SCREENING);
        advertRepository.saveAndFlush(advert);
        List<OrderItem> orderItems = order.getOrderItems();
        for (OrderItem orderItem : orderItems) {
            orderItem.setStatus(OrderItemStatus.PAID);
        }
        orderRepository.saveAndFlush(order);
    }
}
