package com.gumtree.integration.service.expiry;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.IdTokenCredentials;
import com.google.auth.oauth2.IdTokenProvider;
import com.gumtree.authgenerator.GoogleJwtTokenProvider;
import org.jetbrains.annotations.Nullable;

public class GoogleBearerTokenProviderStub implements GoogleJwtTokenProvider {

    @Override
    public String getToken(GoogleCredentials googleCredentials, @Nullable String s) {
        return "valid_token";
    }

    @Override
    public IdTokenCredentials getIdTokenCredentials(GoogleCredentials googleCredentials, @Nullable String s) {
        return IdTokenCredentials.newBuilder().setTargetAudience(s).setIdTokenProvider((IdTokenProvider) googleCredentials).build();
    }

    @Override
    public String provideWithImpersonation(GoogleCredentials googleCredentials, @Nullable String s, @Nullable String s1) {
        return "valid_token";
    }
}
