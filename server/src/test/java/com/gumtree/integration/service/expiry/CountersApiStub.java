package com.gumtree.integration.service.expiry;

import com.gumtree.bapi.adcounters.api.CountersApi;
import com.gumtree.bapi.adcounters.model.SearchRequest;
import rx.Single;

import java.util.List;
import java.util.Map;

public class CountersApiStub implements CountersApi {

    public CountersApiStub() {
    }

    @Override
    public Single<Map<String, Integer>> getCountersByName(
        List<String> include, String xRequestID, String xExperiments, String bearerAuth
    ) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Single<Map<String, Integer>> getCountersByName(
        String xRequestID, String xExperiments, Map<String, Object> queryParams, String bearerAuth
    ) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Single<Map<String, String>> getLastEventDate(
        List<String> include, String xRequestID, String xExperiments, String bearerAuth
    ) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Single<Map<String, String>> getLastEventDate(
        String xRequestID, String xExperiments, Map<String, Object> queryParams, String bearerAuth
    ) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Single<Map<String, Integer>> searchCounters(
        SearchRequest searchRequest, String xRequestID, String xExperiments, String bearerAuth
    ) {
        throw new UnsupportedOperationException();
    }
}
