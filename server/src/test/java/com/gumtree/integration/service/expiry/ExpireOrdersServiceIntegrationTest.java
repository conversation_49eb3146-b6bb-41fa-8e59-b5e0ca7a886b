package com.gumtree.integration.service.expiry;

import com.gumtree.api.service.expiry.ExpireOrdersServiceImpl;
import com.gumtree.test.CleanInsertTestExecutionListener;
import com.gumtree.test.DataSetLocation;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;

import java.util.List;
import java.util.Properties;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 *
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        locations = "expiry-jobs-service-integration-test-context.xml",
        initializers = ExpireOrdersServiceIntegrationTest.TestContextInitializer.class
)
@TestExecutionListeners(
        {
                DependencyInjectionTestExecutionListener.class,
                CleanInsertTestExecutionListener.class,
                TransactionalTestExecutionListener.class
        }
)
@DataSetLocation(value = "classpath:/com/gumtree/api/service/expiry/expire-orders-service-test-dataset.xml", dataSourceName="sellerDatasource")
public class ExpireOrdersServiceIntegrationTest {

    @Autowired
    private ExpireOrdersServiceImpl ordersService;

    private List<Long> idsToExpire;

    @Before
    public void init() {
        idsToExpire = ordersService.getIdsToExpire(2);
    }

    @Test
    public void whenOrdersUnpaidAndNotReachedExpiry() {
        assertThat(idsToExpire.contains(1L), equalTo(false));
    }

    @Test
    public void whenOrdersUnpaidButNotQuiteReachedExpiry() {
        assertThat(idsToExpire.contains(2L), equalTo(false));
    }

    @Test
    public void whenOrdersUnpaidAndExpired() {
        assertThat(idsToExpire.contains(3L), equalTo(true));
    }

    @Test
    public void whenOrdersExpiredButAlreadyPaid() {
        assertThat(idsToExpire.contains(4L), equalTo(false));
    }

    @Test
    public void whenOrderExpiredAndIsMixOfPaidAndUnpaid() {
        assertThat(idsToExpire.contains(5L), equalTo(false));
    }

    public static class TestContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public final void initialize(ConfigurableApplicationContext appContext) {
            Properties properties = new Properties();
            properties.setProperty("gumtree.seller.jobs.archive.draftdays", "0");
            ConfigurationManager.loadProperties(properties);
        }

    }
}
