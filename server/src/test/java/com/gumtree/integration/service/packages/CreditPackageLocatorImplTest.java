package com.gumtree.integration.service.packages;

import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.service.location.LocationService;
import com.gumtree.test.DataSetLocation;
import com.gumtree.wallet.packages.domain.PackageRequest;
import com.gumtree.wallet.packages.service.CreditPackageLocator;
import com.gumtree.wallet.packages.service.CreditPackageService;
import net.sf.ehcache.CacheManager;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/packages.xml", dataSourceName="sellerDatasource")
@ContextConfiguration(classes = CreditPackageLocatorTestConfig.class)
public class CreditPackageLocatorImplTest extends BaseIntegrationTest {

    private static final long ACCOUNT_WITH_VALID_AND_INVALID_PACKAGES = 9L;
    private static final long ADVERT_ELIGIBLE_FOR_AUTOMATIC_PACKAGE = 7L;
    private static final long ADVERT_WITH_NO_ASSIGNED_PACKAGE = 8L;
    private static final long ACCOUNT_WITH_NEGATIVE_LIMITED_CREDIT_PACKAGE = 10L;
    private static final long ACCOUNT_WITH_NEGATIVE_UNLIMITED_CREDIT_PACKAGE = 7L;
    private static final long ADVERT_WITH_EXPIRED_AUTOMATIC_PACKAGE = 12L;
    private static final long ADVERT_WITH_PACKAGE_HAVING_ZERO_CREDITS = 10L;
    private static final long ADVERT_WITH_NEGATIVE_UNLIMITED_CREDIT_PACKAGE = 6L;
    private static final long CATEGORY_CARS = 3L;
    private static final long CATEGORY_JOBS = 6L;

    @Autowired
    private CreditPackageLocator creditPackageLocator;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private LocationService locationService;

    @Autowired
    private CreditPackageService creditPackageService;

    @Autowired
    private CacheManager cacheManager;

    private DateTime now = new DateTime();
    private DateTime midnight = now.toDateMidnight().toDateTime();
    private DateTime yesterday = midnight.minusDays(1);
    private DateTime tomorrow = midnight.plusDays(1);
    private DateTime nextWeek = midnight.plusDays(7);
    private DateTime lastWeek = midnight.minusDays(7);
    private DateTime nextMonth = midnight.plusDays(30);
    private DateTime lastMonth = midnight.minusDays(30);

    /**
     * We cannot easily change the system time for the database to query against so we create some test data here
     * programmatically and relative to today's date for testing credit package start and expiry dates
     */
    @Before
    public void setup() {
        // TODO: can't seem to get this system property set early enough to turn off caching so
        // I manually clear the offending one here to allow our fixture data to take precedence.
        // System.setProperty("net.sf.ehcache.disabled", "true");
        cacheManager.getCache("categoryHierarchy").removeAll();

        setSubjectWithPermissionAndPrincipal("account:managepackages:9", "user1");

        setupInvalidPackages("SFID-PT-006", ACCOUNT_WITH_VALID_AND_INVALID_PACKAGES, 10L);
        setupValidPackages("SFID-PT-006", ACCOUNT_WITH_VALID_AND_INVALID_PACKAGES, 10L);
        setupInvalidPackages("SFID-PT-005", ACCOUNT_WITH_VALID_AND_INVALID_PACKAGES, 10L);
        setupValidPackages("SFID-PT-002", ACCOUNT_WITH_VALID_AND_INVALID_PACKAGES, 10L);
    }

    private void setupInvalidPackages(String salesforceProductId, long accountId, long initialCredits) {
        createPackage(salesforceProductId, accountId, initialCredits, lastWeek, yesterday, false);
        createPackage(salesforceProductId, accountId, initialCredits, lastWeek, nextMonth, true);
        createPackage(salesforceProductId, accountId, initialCredits, nextWeek, nextMonth, false);
        createPackage(salesforceProductId, accountId, initialCredits, tomorrow, nextWeek, false);
    }

    private void setupValidPackages(String salesforceProductId, long accountId, long initialCredits) {
        createPackage(salesforceProductId, accountId, initialCredits, lastMonth, tomorrow, false);
    }

    private void createPackage(String salesforceProductId,
                               long accountId, long initialCredits, DateTime startDate,
                               DateTime endDate, boolean isDeleted) {
        PackageRequest packageRequest = createPackageRequest(salesforceProductId, accountId, initialCredits);
        packageRequest.setStartDate(startDate);
        packageRequest.setEndDate(endDate);
        packageRequest.setDeleted(isDeleted);
        packageRequest.setSalesForceId(String.valueOf(System.currentTimeMillis()));
        creditPackageService.createPackage(packageRequest);
    }

    @Test
    public void hasEligiblePackagesShouldReturnTrueIfPackageWithCreditsIsAvailable() {
        Advert advert = advertService.getAdvert(ADVERT_WITH_NEGATIVE_UNLIMITED_CREDIT_PACKAGE);
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(advert.getSmallestLocation().getId(),
                advert.getCategory(), advert.getAccount().getId(), ProductName.INSERTION);
        assertTrue(hasEligiblePackages);
    }

    @Test
    public void hasEligiblePackagesShouldReturnTrueIfValidUnlimitedPackageWithNegativeCreditExists() {
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(
                locationService.getByName("london").getId(),
                CATEGORY_JOBS,
                ACCOUNT_WITH_NEGATIVE_UNLIMITED_CREDIT_PACKAGE,
                ProductName.INSERTION);
        assertTrue(hasEligiblePackages);
    }

    @Test
    public void hasEligiblePackagesShouldReturnFalseIfValidLimitedPackageWithNegativeCreditExists() {
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(
                locationService.getByName("london").getId(),
                CATEGORY_CARS,
                ACCOUNT_WITH_NEGATIVE_LIMITED_CREDIT_PACKAGE,
                ProductName.INSERTION);
        assertFalse(hasEligiblePackages);
    }

    @Test
    public void hasEligiblePackagesShouldNotFindInactivePackages() {
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(4L, CATEGORY_CARS, ACCOUNT_WITH_VALID_AND_INVALID_PACKAGES, ProductName.INSERTION);
        assertFalse(hasEligiblePackages);
    }

    @Test
    public void hasEligiblePackagesShouldFindActivePackage() {
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(4L, 6L, ACCOUNT_WITH_VALID_AND_INVALID_PACKAGES, ProductName.INSERTION);
        assertTrue(hasEligiblePackages);
    }

    @Test
    public void hasEligiblePackagesCreatesNewAutomaticPackageWhenEligible() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:10", "user1");
        Advert advert = advertService.getAdvert(ADVERT_ELIGIBLE_FOR_AUTOMATIC_PACKAGE);
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(advert.getSmallestLocation().getId(),
                advert.getCategory(), advert.getAccount().getId(), ProductName.INSERTION);
        assertTrue(hasEligiblePackages);
    }

    @Test
    public void hasEligiblePackagesReturnsFalseWhenNoPackageAssignedForProAccount() {
        Advert advert = advertService.getAdvert(ADVERT_WITH_NO_ASSIGNED_PACKAGE);
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(advert.getSmallestLocation().getId(), advert.getCategory(),
                advert.getAccount().getId(), ProductName.INSERTION);
        assertFalse(hasEligiblePackages);
    }
    
    @Test
    public void hasEligiblePackagesCreatesNewAutomaticPackageIfPreviousHasExpired() {
        setSubjectWithPermissionAndPrincipal("account:managepackages:12", "user1");
        assertThat(creditPackageService.getPackagesForAccount(ADVERT_WITH_EXPIRED_AUTOMATIC_PACKAGE, Boolean.FALSE).size(), equalTo(1));
        Advert advert = advertService.getAdvert(ACCOUNT_WITH_VALID_AND_INVALID_PACKAGES);
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(advert.getSmallestLocation().getId(), advert.getCategory(),
                advert.getAccount().getId(), ProductName.INSERTION);
        assertTrue(hasEligiblePackages);
    }

    @Test
    public void hasEligiblePackagesReturnsFalseIfLocatesAutomaticPackagesWithNoRemainingCredits() {
        Advert advert = advertService.getAdvert(ADVERT_WITH_PACKAGE_HAVING_ZERO_CREDITS);
        boolean hasEligiblePackages = creditPackageLocator.hasEligiblePackages(advert.getSmallestLocation().getId(), advert.getCategory(),
                advert.getAccount().getId(), ProductName.INSERTION);
        assertFalse(hasEligiblePackages);
    }

    private PackageRequest createPackageRequest(String salesforceProductId,
                                                long accountId,
                                                long initialCredits) {
        PackageRequest request = new PackageRequest();
        request.setAdjustedCredits(null);
        request.setSalesForceId("QX1");
        request.setSalesforceProductId(salesforceProductId);
        request.setAccountId(accountId);
        request.setInitialCredits(initialCredits);
        return request;
    }
}
