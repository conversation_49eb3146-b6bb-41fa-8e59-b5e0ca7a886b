package com.gumtree.integration.service.user;

import com.gumtree.common.util.security.exception.UserAccountNotActiveException;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.user.entity.ResetPasswordKey;
import com.gumtree.seller.domain.user.entity.ResetPasswordReason;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.seller.event.user.ResetPasswordKeyCreatedEvent;
import com.gumtree.seller.repository.user.ResetPasswordKeyRepository;
import com.gumtree.seller.repository.user.UserCredentialsRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.service.event.EventService;
import com.gumtree.seller.service.keygen.KeyGeneratorService;
import com.gumtree.seller.service.user.impl.PasswordServiceImpl;
import com.gumtree.seller.service.util.TestEventService;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import static com.gumtree.seller.util.DomainObjectBuilders.aUser;
import static com.gumtree.seller.util.DomainObjectBuilders.anEmailAddress;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PasswordServiceTest {
    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @InjectMocks
    private PasswordServiceImpl passwordService;

    @Mock
    private ResetPasswordKeyRepository resetPasswordKeyRepository;

    @Mock
    private KeyGeneratorService keyGeneratorService;

    @Mock
    private Clock clock;

    @Mock
    private TestEventService eventService;

    @Test
    public void initiatePasswordReset() {
        // given
        User user = aUser()
                .withId(3L)
                .withStatus(UserStatus.ACTIVE)
                .withEmailAddress(anEmailAddress().withEmail("<EMAIL>"))
                .build();
        when(clock.getDateTime()).thenReturn(DateTime.now());

        // when
        passwordService.initiatePasswordReset(user, false, ResetPasswordReason.USER);

        // then
        verify(resetPasswordKeyRepository).deleteByUser(user);
        verify(resetPasswordKeyRepository).save(any(ResetPasswordKey.class));
        verify(eventService).publishAfterTransactionCommitted(any(ResetPasswordKeyCreatedEvent.class));
    }

    @Test(expected = UserAccountNotActiveException.class)
    public void userAccountNotActiveExceptionExpected() {
        // given
        User user = aUser().withId(3L).withStatus(UserStatus.AWAITING_ACTIVATION).build();

        // when
        passwordService.initiatePasswordReset(user, false, ResetPasswordReason.CS_ATO);
    }
}
