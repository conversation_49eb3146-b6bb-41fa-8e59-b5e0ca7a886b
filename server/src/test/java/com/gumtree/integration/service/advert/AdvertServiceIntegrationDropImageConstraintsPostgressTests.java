package com.gumtree.integration.service.advert;

import com.gumtree.api.mvc.profile.IntegrationTests;
import com.gumtree.config.BapiServerContextInitializer;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.test.DataSetLocation;
import com.gumtree.test.PostgresCleanInsertTestExecutionListener;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;

import static java.lang.String.format;
import static org.fest.util.Lists.newArrayList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

/**
 * Test for investigation only (https://jira.corp.ebay.com/browse/GTALL-24876) - for local running with Postgres (instead of H2)
 */
@Ignore
@ActiveProfiles({"stubbed-external-api", "category-api-stub"})
@Category({IntegrationTests.class})
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        initializers = BapiServerContextInitializer.class,
        locations = "/com/gumtree/seller/postgress-seller-test-application-context.xml"
)
@TestExecutionListeners(
        {
                DependencyInjectionTestExecutionListener.class,
                PostgresCleanInsertTestExecutionListener.class,
                TransactionalTestExecutionListener.class
        }
)
@DataSetLocation(value = "classpath:/com/gumtree/seller/service/advert/advert-service-test-dataset.xml",
        dataSourceName = "sellerDatasource")
public class AdvertServiceIntegrationDropImageConstraintsPostgressTests extends AdvertServiceIntegrationDropImageConstraintsTests {

    @Override
    protected void switchOffConstraint() {
        new JdbcTemplate(sellerDatasource).execute("alter table advert_image DROP CONSTRAINT fk_advert_image_image");
    }

    @Override
    protected void switchOnConstraint() {
        new JdbcTemplate(sellerDatasource).execute("alter table advert_image ADD CONSTRAINT fk_advert_image_image FOREIGN KEY (image_id) REFERENCES image(id)");
    }

    @Test
    public void advertImagesCanBeRetrievedWhenDisabledTriggerAndImageDoesNoExits() {
        Advert advert = createAdvertWithAdditionalImage();

        String triggerName = new JdbcTemplate(sellerDatasource).queryForObject("select tgname from pg_constraint c join pg_trigger t on t.tgconstraint=c.oid join pg_class on pg_class.oid=tgrelid where c.conname in ('fk_advert_image_image') and pg_class.relname='image' and tgtype = 9", String.class);

        new JdbcTemplate(sellerDatasource).execute(String.format("alter table image DISABLE TRIGGER \"%s\"", triggerName));
        try {
            deleteNonMainImageAndAssertGetAdvertUsingController(advert);
        } finally {
            new JdbcTemplate(sellerDatasource).execute(String.format("alter table image ENABLE TRIGGER \"%s\"", triggerName));
        }
    }

    @Test
    public void weCanDisableTriggerOnDeletionWhileKeepingInsertTriggers() {
        createAdvertWithAdditionalImage();

        String triggerName = new JdbcTemplate(sellerDatasource).queryForObject("select tgname from pg_constraint c join pg_trigger t on t.tgconstraint=c.oid join pg_class on pg_class.oid=tgrelid where c.conname in ('fk_advert_image_image') and pg_class.relname='image' and tgtype = 9", String.class);

        new JdbcTemplate(sellerDatasource).execute(String.format("alter table image DISABLE TRIGGER \"%s\"", triggerName));
        try {
            long existingImageId = LAST_IMAGE_ID;
            long nonExistingImageId = LAST_IMAGE_ID + 1;
            assertThat(existImage(existingImageId), is(equalTo(true)));
            assertThat(existImage(nonExistingImageId), is(equalTo(false)));
            // can delete
            delImage(existingImageId);
            // keep table consistent
            delAdvertImage(existingImageId);
            Advert newAdvert = createAdvert(newArrayList(FIRST_IMAGE_ID), FIRST_IMAGE_ID);
            boolean errorOnInsert = false;
            try {
                new JdbcTemplate(sellerDatasource).execute(format("insert into advert_image(advert_id,image_id,main,position) values(%d,%d,false,2)", newAdvert.getId(), nonExistingImageId));
            } catch (DataIntegrityViolationException e) {
                assertThat(e.getMessage().indexOf("insert or update on table \"advert_image\" violates foreign key constraint \"fk_advert_image_image\"") > 0, is(equalTo(true)));
                errorOnInsert = true;
            }
            assertThat(errorOnInsert, is(equalTo(true)));
        } finally {
            new JdbcTemplate(sellerDatasource).execute(String.format("alter table image ENABLE TRIGGER \"%s\"", triggerName));
        }
    }

    private boolean existImage(long imageId) {
        int count = new JdbcTemplate(sellerDatasource).queryForObject("select count(id) from image where id ="+ imageId, Integer.class);
        return count == 1;
    }

}