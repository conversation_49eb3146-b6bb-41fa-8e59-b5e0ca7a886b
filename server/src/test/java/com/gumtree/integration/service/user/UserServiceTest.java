package com.gumtree.integration.service.user;

import com.gumtree.seller.domain.user.CreateUserCommand;
import com.gumtree.seller.domain.user.entity.ApiKey;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.entity.UserCredentials;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.seller.repository.user.UserActivationKeyRepository;
import com.gumtree.seller.repository.user.UserCredentialsRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.service.user.UserService;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.test.DataSetLocation;
import org.fest.assertions.api.Assertions;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 */
@DataSetLocation(value = "classpath:/com/gumtree/seller/service/user/user-service-test-dataset.xml", dataSourceName="sellerDatasource")
public class UserServiceTest extends BaseIntegrationTest {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserCredentialsRepository userCredentialsRepository;

    @Autowired
    private UserActivationKeyRepository activationKeyRepository;

    @Test
    @Transactional
    public void registerUserPersistsDetails() {
        CreateUserCommand request = new CreateUserCommand();
        request.setFirstName("Roger");
        request.setLastName("Climpson");
        request.setEmailAddress("<EMAIL>");
        request.setContactPhone(" 01632 960001");
        request.setPlainTextPassword("funnyman31");

        // when
        User user = userService.createUser(request);
        userRepository.flush();

        // then
        user = userService.getUser(user.getId());
        assertThat(user.getFirstName(), equalTo("Roger"));
        assertThat(user.getLastName(), equalTo("Climpson"));
        assertThat(user.getUsername(), equalTo("<EMAIL>"));
        assertThat(user.getUsername(), equalTo("<EMAIL>"));
        assertThat(user.getContactPhone(), equalTo(" 01632 960001"));
        assertThat(user.getStatus(), equalTo(UserStatus.AWAITING_ACTIVATION));
        assertThat(user.verifyPassword("funnyman31"), equalTo(true));

        // and
        UserCredentials credentials = userCredentialsRepository.findByUsername(user.getUsername());
        Assertions.assertThat(credentials).isNotNull();
        Assertions.assertThat(credentials.getUsername()).isEqualTo("<EMAIL>");
        Assertions.assertThat(credentials.getVersion()).isEqualTo(0L);
    }

    @Test
    @Transactional
    public void testCanCreateUserWhenEmailExistsButNotLinkedToUser() {
        CreateUserCommand request = new CreateUserCommand();
        request.setFirstName("Roger");
        request.setLastName("Climpson");
        request.setEmailAddress("<EMAIL>");
        request.setContactPhone(" 01632 960001");
        request.setPlainTextPassword("funnyman31");

        // when
        User user = userService.createUser(request);
        userRepository.flush();

        // then
        user = userService.getUser(user.getId());
        assertThat(user.getStatus(), equalTo(UserStatus.AWAITING_ACTIVATION));
        assertThat(user.getEmailAddress().getId(), equalTo(6L));

        // and
        UserCredentials credentials = userCredentialsRepository.findByUsername(user.getUsername());
        Assertions.assertThat(credentials).isNotNull();
        Assertions.assertThat(credentials.getUsername()).isEqualTo("<EMAIL>");
        Assertions.assertThat(credentials.getVersion()).isEqualTo(0L);
    }

    @Test
    @Transactional
    public void getUserReturnsAssociatedAccounts() {
        User user = userService.getUser(1L);
        assertThat(user.getAccounts().size(), equalTo(2));
        assertThat(user.getAccounts().get(0).getId(), equalTo(1L));
        assertThat(user.getAccounts().get(1).getId(), equalTo(2L));
    }

    @Test
    @Transactional
    public void getUserIsCaseInsensitive() {
        User user1 = userService.getUser("<EMAIL>");
        User user2 = userService.getUser("<EMAIL>");
        assertThat(user1.getId(), equalTo(user2.getId()));
    }

    @Test
    public void resetApiKey() {
        User user = userService.getUser("<EMAIL>");
        Long userVersionBeforeUpdate = user.getVersion();
        List<ApiKey> apiKeysBefore = user.getApiKeys();
        Assertions.assertThat(apiKeysBefore).hasSize(1);

        // when
        ApiKey newApiKey = userService.resetApiKey(user.getId());

        // then
        Assertions.assertThat(newApiKey.getId()).isNotEqualTo(apiKeysBefore.get(0).getId());
        Assertions.assertThat(newApiKey.getPrivateKey()).isNotEqualTo(apiKeysBefore.get(0).getPrivateKey());

        // and
        User userAfter = userService.getUser("<EMAIL>");
        Assertions.assertThat(userAfter.getApiKeys()).hasSize(1);
        Assertions.assertThat(userAfter.getApiKeys().get(0).getId()).isEqualTo(newApiKey.getId());
        Assertions.assertThat(userAfter.getVersion()).isEqualTo(userVersionBeforeUpdate);
    }

}
