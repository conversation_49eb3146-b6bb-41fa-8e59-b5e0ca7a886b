package com.gumtree.integration.service.advert;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.domain.advert.DeleteReason;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.sapi.spec.domain.FlatAd;
import com.gumtree.seller.domain.advert.AdvertRequest;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.advert.entity.AdvertAttribute;
import com.gumtree.seller.domain.advert.exception.AdvertNotFoundException;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.domain.attribute.dictionary.EnumValue;
import com.gumtree.seller.domain.attribute.entity.Attribute;
import com.gumtree.seller.domain.location.exception.LocationUndefinedException;
import com.gumtree.seller.domain.location.exception.PostcodeNotFoundException;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.repository.advert.AdvertRepository;
import com.gumtree.seller.repository.attribute.AttributeRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.security.exception.UnauthorizedActionException;
import com.gumtree.seller.service.advert.AdvertRequestBuilder;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.service.advert.FeatureApplicator;
import com.gumtree.test.DataSetLocation;
import org.apache.camel.ProducerTemplate;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.UnauthorizedException;
import org.fest.assertions.api.Assertions;
import org.fest.util.Lists;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.convert.ConversionService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gumtree.seller.test.TestUtils.createLocation;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

@DataSetLocation(value = "classpath:/com/gumtree/seller/service/advert/advert-service-test-dataset.xml",
        dataSourceName = "sellerDatasource")
@Transactional
public class AdvertServiceIntegrationTest extends BaseIntegrationTest {
    private static final Long DEFAULT_ACCOUNT_ID = 1L;
    private static final Long OTHER_ACCOUNT_ID = 2L;
    private static final Long NON_EXISTING_AD_ID = 9999999L;
    private static final String DEFAULT_ACCOUNT_PERMISSION = "account:manageads:1";

    private static final Long LIVE_AD_ID = 1l;
    public static final String FALSE = "false";
    public static final String TRUE = "true";

    private static DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    @Qualifier("realTimeAdvertExchange")
    ProducerTemplate camelRoute;
    @Autowired
    private AdvertService advertService;
    @Autowired
    private AdvertRepository advertRepository;
    @Autowired
    private AttributeRepository attributeRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private FeatureApplicator featureApplicator;
    @Autowired
    private StoppedClock testClock;
    @Autowired
    private CategoryModel categoryModel;
    @Autowired
    private ConversionService conversionService;

    @Before
    public void beforeEach() {
        Mockito.reset(camelRoute);
    }

    @After
    public void tearDownSubject() {
        clearSubject();
    }

    @Test(expected = UnauthorizedActionException.class)
    public void createNewAdvertAuthorizationFailsWhenTryingToCreateAgainstAccountWithoutPermission() throws Exception {
        setSubjectWithPermissionAndPrincipal("account:manageads:" + OTHER_ACCOUNT_ID, "user1");
        AdvertRequest createAdvertRequest = createAdvertRequest(DEFAULT_ACCOUNT_ID);
        advertService.createAdvert(createAdvertRequest);
    }

    @Test
    public void basicAdvertDetailsPersistedWhenNewAdvertCreated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();

        // when
        Advert advert = advertService.createAdvert(createAdvertRequest);

        // then
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getCreatedDate(), is(not(nullValue())));
        assertThat(advert.getLastModifiedDate(), is(not(nullValue())));
        assertThat(advert.getTitle(), equalTo("A test title"));
        assertThat(advert.getDescription(), equalTo("A test description"));
        assertThat(advert.getContactEmail(), equalTo("<EMAIL>"));
        assertThat(advert.getContactUrl(), equalTo("http://www.gumtree.com"));
        assertThat(advert.getContactTelephone(), equalTo("01632 960001"));
        assertThat(advert.getCategory(), equalTo(1L));
        assertThat(advert.getHierarchicalLocations().size(), equalTo(2));
        assertThat(advert.getAccount().getId(), equalTo(1L));
        assertThat(advert.getIp(), equalTo("127.0.0.1"));
        assertThat(advert.getArchivedDate(), nullValue());
        assertThat(advert.getYoutubeLink(), equalTo("http://www.youtube.com/watch?v=B8WHKRzkCOY&feature=g-all-esi"));
        assertThat(advert.getWebsiteUrl().getWebsiteUrl(), equalTo("http://www.gumtree.com"));
        assertThat(advert.getStatus(), equalTo(AdvertStatus.DRAFT));
        assertThat(advert.getVersion(), equalTo(0L));

        // then
        advert = advertService.getAdvert(advert.getId());
        FlatAd flatAd = conversionService.convert(advert, FlatAd.class);
        assertThat(advert.getVersion(), equalTo(0L));

    }

    @Test
    public void newAttributeDefinitionIsCreatedWhenNewAdvertCreatedWithUndefinedAttribute() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);

        // given an undefined attribute
        CategoryConstants.Attribute undefinedAttrName = CategoryConstants.Attribute.VEHICLE_MILEAGE;
        Assertions.assertThat(attributeRepository.findByName(undefinedAttrName.name())).isNull();
        Assertions.assertThat(attributeRepository.findByName(undefinedAttrName.getName())).isNull();

        long preTestAttrDefinitionCount = attributeRepository.count();

        // given an advert with an new attribute
        Map<String, String> attributes = new HashMap<>();
        attributes.put(CategoryConstants.Attribute.VEHICLE_MAKE.getName(), "Ford");
        attributes.put(CategoryConstants.Attribute.VEHICLE_COLOUR.getName(), "Black");
        attributes.put(CategoryConstants.Attribute.VEHICLE_FUEL_TYPE.getName(), EnumValue.DIESEL.getId());
        attributes.put(undefinedAttrName.getName(), "1000");

        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setAttributes(attributes);

        // when
        Advert advert = advertService.createAdvert(createAdvertRequest);

        // then new attribute definition is created
        Attribute newAttr = attributeRepository.findByName(undefinedAttrName.name());
        Assertions.assertThat(newAttr.getId()).isNotNull();
        Assertions.assertThat(newAttr.getName()).isEqualTo(undefinedAttrName.name());
        Assertions.assertThat(attributeRepository.count()).isEqualTo(preTestAttrDefinitionCount + 1);

        // then advert attributes should be persisted
        advert = advertService.getAdvert(advert.getId());

        Assertions.assertThat(advert.getAttributes()).hasSize(4);
        assertThat(getAttributeFor("VEHICLE_MAKE", advert.getAttributes()).getValue(), equalTo("Ford"));
        assertThat(getAttributeFor("VEHICLE_COLOUR", advert.getAttributes()).getValue(), equalTo("Black"));
        assertThat(getAttributeFor("VEHICLE_FUEL_TYPE", advert.getAttributes()).getValue(), equalTo(EnumValue.DIESEL.getId()));
        assertThat(getAttributeFor("VEHICLE_MILEAGE", advert.getAttributes()).getValue(), equalTo("1000"));
    }

    @Test
    public void areaPersistedWhenNewAdvertCreated() throws Exception {
        setSubjectWithPermissionAndPrincipal(DEFAULT_ACCOUNT_PERMISSION, "user1");
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setArea("My Local Area");

        // when
        Advert advert = advertService.createAdvert(createAdvertRequest);

        // then
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getArea(), equalTo("My Local Area"));
    }

    @Test
    public void accountIsMarkedAsForcedDealerWhenNewAdvertCreatedInCarsWithSellerTypeTrade() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);

        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setCategoryId(categoryModel.getByName("bmw").get().getId());

        Map<String, String> attributes = new HashMap<>();
        attributes.put(CategoryConstants.Attribute.SELLER_TYPE.getName(), "trade");
        createAdvertRequest.setAttributes(attributes);

        // when
        Advert advert = advertService.createAdvert(createAdvertRequest);

        // then account is marked as force post ad car dealer
        advert = advertService.getAdvert(advert.getId());
        Assertions.assertThat(advert.getAccount().isForcePostAsDealer()).isTrue();
    }

    @Test
    public void advertCreatedWhenDescriptionGreaterThan10000Characters() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setDescription(StringUtils.leftPad("foobar", 12000, 'a'));

        // when
        Advert advert = advertService.createAdvert(createAdvertRequest);

        // than
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getDescription().length(), equalTo(10000));
    }

    @Test
    public void advertImagesPersistedWhenNewAdvertCreated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();

        List<Long> imageIds = new ArrayList<Long>();
        imageIds.add(1L);
        imageIds.add(2L);

        createAdvertRequest.setImageIds(imageIds);

        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());

        assertThat(advert.getImages().size(), equalTo(2));
        assertThat(advert.getPrimaryImage().getId(), equalTo(1L));
        assertThat(advert.getImages().get(0).getId(), equalTo(1L));
        assertThat(advert.getImages().get(1).getId(), equalTo(2L));
    }

    @Test
    public void advertImagesReturnEpsOrCFUrls() {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();

        List<Long> imageIds = Lists.newArrayList(1L, 2L, 3L, 4L);

        createAdvertRequest.setImageIds(imageIds);

        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());

        assertThat(advert.getImages().size(), equalTo(4));
        assertThat(advert.getPrimaryImage().getUrl(), equalTo("https://i.ebayimg.com/00/s/1/$_86.PNG"));
        assertThat(advert.getImages().get(0).getUrl(), equalTo("https://i.ebayimg.com/00/s/1/$_86.PNG"));
        assertThat(advert.getImages().get(1).getUrl(), equalTo("https://imagedelivery.net/abc/2/1"));
        assertThat(advert.getImages().get(2).getUrl(), equalTo("https://i.ebayimg.com/00/s/3/$_86.PNG"));
        assertThat(advert.getImages().get(3).getUrl(), equalTo("https://imagedelivery.net/abc/4/1"));
    }

    @Test
    public void advertAttributesPersistedWhenNewAdvertCreated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);

        long preTestAttrDefinitionCount = attributeRepository.count();

        // given an advert with attributes (existing attributes)
        Map<String, String> attributes = new HashMap<>();
        attributes.put(CategoryConstants.Attribute.VEHICLE_MAKE.getName(), "Ford");
        attributes.put(CategoryConstants.Attribute.VEHICLE_COLOUR.getName(), "Black");
        attributes.put(CategoryConstants.Attribute.VEHICLE_FUEL_TYPE.getName(), EnumValue.DIESEL.getId());

        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setAttributes(attributes);

        // when
        Advert advert = advertService.createAdvert(createAdvertRequest);

        // then attributes should be persisted
        advert = advertService.getAdvert(advert.getId());

        Assertions.assertThat(advert.getAttributes()).hasSize(3);
        assertThat(getAttributeFor(CategoryConstants.Attribute.VEHICLE_MAKE.name(), advert.getAttributes()).getValue(), equalTo("Ford"));
        assertThat(getAttributeFor(CategoryConstants.Attribute.VEHICLE_COLOUR.name(), advert.getAttributes()).getValue(), equalTo("Black"));
        assertThat(getAttributeFor(CategoryConstants.Attribute.VEHICLE_FUEL_TYPE.name(), advert.getAttributes()).getValue(), equalTo(EnumValue.DIESEL.getId()));

        // then no attribute definition is auto-added
        Assertions.assertThat(attributeRepository.count()).isEqualTo(preTestAttrDefinitionCount);
    }

    @Test
    public void pricePersistedWhenNewAdvertCreated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        Map<String, String> attributes = new HashMap<>();
        attributes.put(CategoryConstants.Attribute.PRICE.getName(), "99.99");
        createAdvertRequest.setAttributes(attributes);
        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getAttributes().get(0).getValue(), equalTo("99.99"));
    }

    @Test
    public void visibleOnMapTrueByDefaultWhenNewAdvertCreated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.isVisibleOnMap(), equalTo(true));
    }

    @Test
    public void visibleOnMapPersistedWhenNewAdvertCreatedAndValueIsTrue() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setVisibleOnMap(true);
        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.isVisibleOnMap(), equalTo(true));
    }

    @Test
    public void visibleOnMapPersistedWhenNewAdvertCreatedAndValueIsFalse() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setVisibleOnMap(false);
        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void whenAdvertCreatedWithRecognisedPostcode() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setPostcode("SW185AS");
        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getHierarchicalLocations().size(), equalTo(2));
        assertThat(advert.getHierarchicalLocations().contains(createLocation(2)), equalTo(true));
        assertThat(advert.getHierarchicalLocations().contains(createLocation(3)), equalTo(true));
    }

    @Test(expected = PostcodeNotFoundException.class)
    public void whenAdvertCreatedWithRecognisedOutcode() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setPostcode("SW194LP");

        // when
        advertService.createAdvert(createAdvertRequest);
    }

    @Test(expected = PostcodeNotFoundException.class)
    public void whenAdvertCreatedWithUnrecognisedPostcode() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setPostcode("unrecognised");
        advertService.createAdvert(createAdvertRequest);
    }

    @Test(expected = LocationUndefinedException.class)
    public void whenAdvertCreatedWithNullPostcodeAndNullLocationId() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setPostcode(null);
        createAdvertRequest.setSpecifiedLocationId(null);
        advertService.createAdvert(createAdvertRequest);
    }

    @Test
    public void externalRefPersistedWhenNewAdvertPosted() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setExternalRef("foobar");
        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getExternalRef(), equalTo("foobar"));
    }

    @Test
    public void externalRefOnExistingAdvertCanBeRetrieved() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        Advert advert = advertService.getAdvert(4L);
        assertThat(advert.getExternalRef(), equalTo("an-existing-external-reference"));
    }


    @Test
    public void advertUrlPersistedWhenNewAdvertPosted() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setWebsiteUrl("http://www.gumtree.com");
        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getWebsiteUrl().getWebsiteUrl(), equalTo("http://www.gumtree.com"));
    }

    @Test
    @Transactional
    public void advertUrlOnExistingAdvertCanBeRetrieved() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);

        // given
        Advert advert = advertService.getAdvert(4L);

        // then
        assertThat(advert.getWebsiteUrl().getWebsiteUrl(), equalTo("http://existing.website.com"));
    }

    @Test
    public void advertUrlCanBeSetOnExistingAdvert() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();
        requestBuilder.withWebsiteUrl("http://www.gumtree.com");
        advertService.updateAdvert(3L, requestBuilder.toAdvertRequest());
        Advert advert = advertService.getAdvert(3L);
        assertThat(advert.getWebsiteUrl().getWebsiteUrl(), equalTo("http://www.gumtree.com"));
    }

    @Test
    public void advertVersionSetWhenNewAdvertCreated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequest createAdvertRequest = createAdvertRequest();
        Advert advert = advertService.createAdvert(createAdvertRequest);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getVersion(), is(not(nullValue())));
    }

    /**
     * **************
     * UPDATE METHODS
     * **************
     */

    @Test(expected = UnauthorizedActionException.class)
    public void updateAdvertAuthorizationFailsWhenTryingToUpdateAgainstAccountWithoutPermission() throws Exception {
        // given
        setSubjectWithPermissionAndPrincipal("account:manageads:" + OTHER_ACCOUNT_ID, "user1");
        AdvertRequest updateAdvertRequest = createAdvertRequest(DEFAULT_ACCOUNT_ID);

        // when
        advertService.updateAdvert(1L, updateAdvertRequest);
    }

    @Test(expected = AdvertNotFoundException.class)
    public void updateIsRejectedForUnknownAdvert() throws Exception {
        // given
        AdvertRequest updateAdvertRequest = createAdvertRequest(DEFAULT_ACCOUNT_ID);

        // when
        advertService.updateAdvert(NON_EXISTING_AD_ID, updateAdvertRequest);
    }

    @Test
    public void shouldSetDeleteReason() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);

        // given
        Advert newAd = advertService.createAdvert(createAdvertRequest());
        Assertions.assertThat(newAd.getStatus()).isEqualTo(AdvertStatus.DRAFT);

        // when
        advertService.updateAdvertDeleteReason(newAd.getId(), DeleteReason.GT_SUCCESS_YES);

        // than
        Advert updatedAd = advertService.getAdvert(newAd.getId());
        Assertions.assertThat(updatedAd.getDeleteReason()).isEqualTo(DeleteReason.GT_SUCCESS_YES);
        Assertions.assertThat(updatedAd.getStatus()).isEqualTo(AdvertStatus.DRAFT);
    }

    @Test(expected = UnauthorizedException.class)
    public void updateDeleteReasonAuthorizationFailsWhenTryingToUpdateAgainstAccountWithoutPermission() {
        setSubjectPermission("account:manageads:" + OTHER_ACCOUNT_ID);

        // when
        advertService.updateAdvertDeleteReason(1L, DeleteReason.GT_SUCCESS_YES);
    }

    @Test
    public void basicAdvertDetailsUpdatedWhenAdvertUpdated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(dateToString(advert.getCreatedDate()), equalTo("2011-01-01 09:30:00")); // Hasn't changed!
        assertThat(dateToString(advert.getLastModifiedDate()), not(equalTo("2011-01-01 10:25:00"))); // Changed from previous value!
        assertThat(advert.getTitle(), equalTo("New title"));
        assertThat(advert.getDescription(), equalTo("New description"));
        assertThat(advert.getContactEmail(), equalTo("<EMAIL>"));

        assertThat(advert.getContactUrl(), equalTo("http://www.newurl.com"));
        assertThat(advert.getContactTelephone(), equalTo("01632 960001"));
        assertThat(advert.getCategory(), equalTo(2L));
        assertThat(advert.getHierarchicalLocations().size(), equalTo(2));
        assertThat(advert.getAccount().getId(), equalTo(1L));
    }

    @Test
    public void areaUpdatedWhenAdvertUpdated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();
        requestBuilder.withArea("My Local Area");
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getArea(), equalTo("My Local Area"));
    }

    @Test
    public void advertUpdatedWhenDescriptionGreaterThan10000Characters() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();
        requestBuilder.withDescription(StringUtils.leftPad("foobar", 12000, 'a'));
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getDescription().length(), equalTo(10000));
    }

    @Test
    public void advertAttributesUpdatedWhenAdvertUpdatedWithNewAttributes() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();
        requestBuilder.withAttribute(CategoryConstants.Attribute.VEHICLE_COLOUR.getName(), "Black");
        requestBuilder.withAttribute(CategoryConstants.Attribute.VEHICLE_FUEL_TYPE.getName(), EnumValue.DIESEL.getId());
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getAttributes().size(), equalTo(2));
        assertThat(getAttributeFor(CategoryConstants.Attribute.VEHICLE_COLOUR.name(), advert.getAttributes()).getValue(), equalTo("Black"));
        assertThat(getAttributeFor(CategoryConstants.Attribute.VEHICLE_FUEL_TYPE.name(), advert.getAttributes()).getValue(), equalTo(EnumValue.DIESEL.getId()));
    }

    @Test
    public void advertAttributesCorrectWhenAdvertUpdatedWithAnAttributeThatAlreadyExists() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();
        requestBuilder.withAttribute(CategoryConstants.Attribute.VEHICLE_MAKE.getName(), "BMW");

        // when
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());

        // then
        advert = advertService.getAdvert(advert.getId());
        Assertions.assertThat(advert.getAttributes()).hasSize(1);
        assertThat(getAttributeFor(CategoryConstants.Attribute.VEHICLE_MAKE.name(), advert.getAttributes()).getValue(), equalTo("BMW"));
    }

    @Test
    public void priceUpdatedWhenAdvertUpdate() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();
        requestBuilder.withAttribute(CategoryConstants.Attribute.PRICE.getName(), "99.99");
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getAttributes().get(0).getValue(), equalTo("99.99"));
    }

    @Test
    public void visibleOnMapTrueByDefaultWhenAdvertUpdated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.isVisibleOnMap(), equalTo(true));
    }

    @Test
    public void visibleOnMapUpdatedWhenAdvertUpdatedAndValueIsTrue() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder().withVisibleOnMap(true);
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.isVisibleOnMap(), equalTo(true));
    }

    @Test
    public void visibleOnMapUpdatedWhenAdvertUpdatedAndValueIsFlase() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder().withVisibleOnMap(false);
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void whenAdvertUpdatedWithRecognisedPostcode() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder().withPostcode("TW91RP");
        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getHierarchicalLocations().size(), equalTo(2));
        assertThat(advert.getHierarchicalLocations().contains(createLocation(4)), equalTo(true));
        assertThat(advert.getHierarchicalLocations().contains(createLocation(5)), equalTo(true));
    }

    @Test
    public void whenAdvertUpdatedWithPhoneVerify() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);

        AdvertRequest createAdvertRequest = createAdvertRequest();
        createAdvertRequest.setAdvertStatus(AdvertStatus.DELETED_USER);
        // when
        Advert advert = advertService.createAdvert(createAdvertRequest);
        // then

        assertThat(advert.getStatus(), is(AdvertStatus.DRAFT));

        advertService.retractAdvert(advert.getId());
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getStatus(), is(AdvertStatus.DELETED_USER));

        AdvertRequestBuilder requestBuilder = updateRequestBuilder();

        AdvertRequest req = requestBuilder.toAdvertRequest();
        req.setAdvertStatus(AdvertStatus.AWAITING_PHONE_VERIFIED);

        advert = advertService.updateAdvert(advert.getId(), req);
        advert = advertService.getAdvert(advert.getId());

        assertThat(advert.getStatus(), equalTo(AdvertStatus.AWAITING_PHONE_VERIFIED));
    }

    @Test(expected = PostcodeNotFoundException.class)
    public void whenAdvertUpdatedWithRecognisedOutcode() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder().withPostcode("SW194LP");
        advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
    }

    @Test(expected = PostcodeNotFoundException.class)
    public void whenAdvertUpdatedWithUnrecognisedPostcode() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder().withPostcode("Unrecognised");
        advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
    }

    @Test(expected = LocationUndefinedException.class)
    public void whenAdvertUpdatedWithNullPostcodeAndNullLocationId() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder().withPostcode(null).withLocation(null);
        advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
    }

    @Test
    public void advertVersionIncrementedWhenAdvertUpdated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        Advert beforeAd = advertService.getAdvert(LIVE_AD_ID);
        Assertions.assertThat(beforeAd.getVersion()).isEqualTo(1L);
        Assertions.assertThat(beforeAd.getStatus()).isEqualTo(AdvertStatus.LIVE);

        // when
        advertService.updateAdvert(LIVE_AD_ID, updateRequestBuilder().toAdvertRequest());

        // then
        Advert afterAd = advertService.getAdvert(LIVE_AD_ID);
        assertThat(afterAd.getVersion(), equalTo(2L));
        Assertions.assertThat(afterAd.getTitle()).isEqualTo("New title");
    }

    @Test
    public void updateFailsWhenVersionHasChangedSince() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);

        // when
        Advert advert = advertService.updateAdvert(1L, updateRequestBuilder().toAdvertRequest());

        // then
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getVersion(), equalTo(2L));
    }

    @Test
    public void advertImageOrderUpdatedWhenAdvertUpdated() throws Exception {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        AdvertRequestBuilder requestBuilder = updateRequestBuilder();

        List<Long> imageIds = new ArrayList<Long>();
        imageIds.add(2L);
        imageIds.add(1L);

        requestBuilder.withImageIds(imageIds);

        Advert advert = advertService.updateAdvert(1L, requestBuilder.toAdvertRequest());
        advert = advertService.getAdvert(advert.getId());

        assertThat(advert.getImages().size(), equalTo(2));
        assertThat(advert.getPrimaryImage().getId(), equalTo(2L));
        assertThat(advert.getImages().get(0).getId(), equalTo(2L));
        assertThat(advert.getImages().get(1).getId(), equalTo(1L));
    }

    /**
     * ******************
     * UN-PUBLISH METHODS
     * ******************
     */

    @Test
    public void retractAdvertSuccessfullyFlagsAdvertAsDeletedByUser() {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        Advert advert = advertService.retractAdvert(1L);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getStatus(), equalTo(AdvertStatus.DELETED_USER));
        assertThat(advert.getDeletedDate(), is(not(nullValue())));
    }

    /**
     * ***************
     * PUBLISH METHODS
     * ***************
     */

    @Test
    public void publishAdvertSuccessfullyFlagsAdvertAsLive() {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        Advert advert = advertService.publishAdvert(2L);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getStatus(), equalTo(AdvertStatus.LIVE));
        assertThat(advert.getPublishedDate(), is(not(nullValue())));
        assertThat(advert.getExpiryDate(), is(not(nullValue())));
        assertThat(advert.getCsReviewed(), equalTo(null));
    }

    @Test
    public void publishAdverFromCsReview() {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        final Long ADVERT_IN_CS_REVIEW = 5L;

        // when
        Advert advert = advertService.publishAdvert(ADVERT_IN_CS_REVIEW);

        // then
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getStatus(), equalTo(AdvertStatus.LIVE));
        assertThat(advert.getPublishedDate(), is(not(nullValue())));
        assertThat(advert.getExpiryDate(), is(not(nullValue())));
        assertThat(advert.getCsReviewed(), equalTo(true));
    }

    @Test
    public void publishAdvertFromDeletedByUser() {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        final Long ADVERT_IN_DELETED_USER = 7L;
        Advert advert = advertService.publishAdvert(ADVERT_IN_DELETED_USER);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getStatus(), equalTo(AdvertStatus.LIVE));
        assertThat(advert.getPublishedDate(), is(not(nullValue())));
        assertThat(advert.getExpiryDate(), is(not(nullValue())));
    }

    /**
     * ***************
     * EXPIRY METHODS
     * ***************
     */

    @Test
    public void expireAdvertSuccessfullyFlagsAdvertAsExpired() {
        Advert advert = advertService.expireAdvert(3L);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getStatus(), equalTo(AdvertStatus.EXPIRED));
    }

    @Test
    public void testFindByUser() {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        User user = userRepository.findByUsername("user1");
        System.out.println(user);
    }

    /**
     * ***************
     * CS REJECT
     * ***************
     */

    @Test
    public void shouldSetAdvertAsDeletedCSAndCsReviewedWhenCSRetractAdvert() {
        final Long LIVE_ADVERT = 6L;
        Advert advert = advertService.customerServiceRetractAdvert(LIVE_ADVERT);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getStatus(), equalTo(AdvertStatus.DELETED_CS));
        assertThat(advert.getCsReviewed(), equalTo(true));
    }

    /**
     * ***************
     * ADVERT NEEDS EDITING
     * ***************
     */

    @Test
    public void shouldSetAdvertAsNeedsEditingAndCsReviewedWhenCSRequireEditingAdvert() {
        final Long LIVE_ADVERT = 6L;
        Advert advert = advertService.customerServiceRequiresEditingAdvert(LIVE_ADVERT);
        advert = advertService.getAdvert(advert.getId());
        assertThat(advert.getStatus(), equalTo(AdvertStatus.NEEDS_EDITING));
        assertThat(advert.getCsReviewed(), equalTo(true));
    }

    /**
     * **************
     * FEATURE METHODS
     * **************
     */

    @Test
    @Transactional
    public void advertFeaturePersistedWhenAdvertFeaturedNew() {
        setSubjectPermission(DEFAULT_ACCOUNT_PERMISSION);
        DateTime expiry = new DateTime().plusDays(1);

        Advert advert = advertService.getAdvert(1L);

        // when
        featureApplicator.featureAdvert(advert, ProductName.FEATURE_3_DAY, expiry, null);

        // then
        advert = advertRepository.findOne(1L);
        assertThat(advert.getFeatures().size(), equalTo(1));
        assertThat(advert.getFeatures().get(0).getProduct().getName(), equalTo(ProductName.FEATURE_3_DAY));
        assertThat(advert.getFeatures().get(0).getExpiryDate(), equalTo(expiry));
    }

    /**
     * **************
     * HELPER METHODS
     * **************
     */

    private AdvertRequestBuilder updateRequestBuilder() {
        AdvertRequestBuilder requestBuilder = new AdvertRequestBuilder();
        requestBuilder.withTitle("New title")
                .withDescription("New description")
                .withContactEmail("<EMAIL>")
                .withContactTelephone("01632 960001")
                .withContactUrl("http://www.newurl.com")
                .inCategory(2L)
                .withPostcode("SW18 5AS")
                .toAccount(DEFAULT_ACCOUNT_ID);

        return requestBuilder;
    }

    private AdvertRequest createAdvertRequest() {
        return createAdvertRequest(DEFAULT_ACCOUNT_ID);
    }

    public AdvertRequest updateStatusRequest(AdvertStatus status) {
        AdvertRequest req = new AdvertRequest();
        req.setAdvertStatus(status);
        req.setAccountId(DEFAULT_ACCOUNT_ID);
        return req;

    }

    private AdvertRequest createAdvertRequest(Long accountId) {
        AdvertRequest createAdvertRequest = new AdvertRequest();
        createAdvertRequest.setCategoryId(1L);
        createAdvertRequest.setPostcode("SW18 5AS");
        createAdvertRequest.setContactUrl("http://www.gumtree.com");
        createAdvertRequest.setContactEmail("<EMAIL>");
        createAdvertRequest.setContactTelephone("01632 960001");
        createAdvertRequest.setDescription("A test description");
        createAdvertRequest.setTitle("A test title");
        createAdvertRequest.setAccountId(accountId);
        createAdvertRequest.setIp("127.0.0.1");
        createAdvertRequest.setYoutubeLink("http://www.youtube.com/watch?v=B8WHKRzkCOY&feature=g-all-esi");
        createAdvertRequest.setWebsiteUrl("http://www.gumtree.com");
        return createAdvertRequest;
    }

    private AdvertAttribute getAttributeFor(String attributeName, List<AdvertAttribute> attributes) {
        for (AdvertAttribute attribute : attributes) {
            if (attribute.getAttribute().getName().equals(attributeName)) {
                return attribute;
            }
        }
        return null;
    }

    private String dateToString(DateTime dateTime) {
        return dateTimeFormatter.print(dateTime);
    }
}
