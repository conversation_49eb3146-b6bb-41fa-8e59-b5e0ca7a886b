package com.gumtree.integration.service.account;

import com.gumtree.api.SellerType;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.domain.account.AccountCommand;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.account.exception.AccountNotFoundException;
import com.gumtree.seller.domain.account.status.AccountStatus;
import com.gumtree.seller.domain.account.type.AccountReplyType;
import com.gumtree.seller.domain.account.type.AccountType;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.domain.image.exception.ImageNotFoundException;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.repository.account.AccountImageRepository;
import com.gumtree.seller.repository.account.AccountRepository;
import com.gumtree.seller.repository.advert.AdvertRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.service.account.AccountService;
import com.gumtree.seller.service.account.DefaultAccountService;
import com.gumtree.seller.service.advert.AdvertRepositoryService;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.service.event.EventService;
import com.gumtree.seller.service.image.ImageService;
import com.gumtree.seller.service.security.SecurityService;
import com.gumtree.seller.service.user.UserService;
import com.gumtree.test.DataSetLocation;
import com.gumtree.test.Fixtures;
import org.apache.shiro.authz.UnauthorizedException;
import org.fest.assertions.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;

import static com.gumtree.seller.domain.account.entity.Account.ForcePostType.CAR_DEALER;
import static com.gumtree.seller.domain.advert.status.AdvertStatus.CREATED;
import static com.gumtree.seller.domain.advert.status.AdvertStatus.DELETED_USER;
import static com.gumtree.seller.domain.advert.status.AdvertStatus.EXPIRED;
import static com.gumtree.seller.domain.advert.status.AdvertStatus.LIVE;
import static com.gumtree.test.Fixtures.Builders.ad;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@DataSetLocation(value = "classpath:/com/gumtree/seller/service/account/account-service-test-dataset.xml", dataSourceName="sellerDatasource")
public class DefaultAccountServiceIntegrationTest extends BaseIntegrationTest {

    public static final long NON_EXISTING_ACCOUNT_ID = 100002L;
    public static final long PRO_ACCOUNT_ID = 31111L;
    public static final long CARS_DEALER_ACCOUNT_ID = 31113L;
    public static final long VANS_DEALER_ACCOUNT_ID = 31114L;
    public static final long NON_PRO_ACCOUNT_ID = 2222L;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private AdvertRepository advertRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AccountService accountService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private ImageService imageService;

    @Autowired
    private CategoryModel categoryModel;

    @Autowired
    private AdvertRepositoryService advertRepositoryService;

    @Autowired
    private AccountImageRepository accountImageRepository;

    @Autowired
    private EventService eventService;

    @Before
    public void beforeEach() {
        setupDefaultPrincipal();
    }

    @Test
    public void createsAccountCorrectly() {

        AccountRepository accountRepository = mock(AccountRepository.class);
        SecurityService securityService = mock(SecurityService.class);
        ImageService imageService = mock(ImageService.class);
        DefaultAccountService accountService = new DefaultAccountService(accountRepository, securityService, imageService,
                advertRepositoryService, accountImageRepository, categoryModel, eventService);

        when(accountRepository.save(any(Account.class))).thenAnswer(new Answer<Account>() {
            @Override
            public Account answer(InvocationOnMock invocation) throws Throwable {
                Account account = (Account) invocation.getArguments()[0];
                Assert.hasLength(account.getName());
                Assert.hasLength(account.getDescription());
                ReflectionTestUtils.setField(account, "id", 20L);
                return account;
            }
        });

        Account account = accountService.createAccount("account", "account description");

        verify(accountRepository).save(account);

        assertThat(account.getId(), equalTo(20L));
        assertThat(account.getName(), equalTo("account"));
        assertThat(account.getDescription(), equalTo("account description"));
        assertNull(account.getCreditPackages());
        assertThat(account.isProAccount(), equalTo(false));
    }

    @Test
    public void createsAccountWithAddresses() {
        AccountCommand accountCommand = createAccountCommandObject();

        setSubjectWithPermissionAndPrincipal("account:manageaccounts:*", "user1");
        Account account = accountService.createAccount(accountCommand);

        assertAccountProperties(account);
    }

    @Test
    public void updatesValidAccount() {
        setSubjectWithPermissionAndPrincipal("account:manageaccounts:*", "user1");
        AccountCommand accountCommand = createAccountCommandObject();
        Account updatedAccount = accountService.updateAccount(1L, accountCommand);
        assertAccountProperties(updatedAccount);
    }

    @Test(expected = AccountNotFoundException.class)
    public void updateAccountWithInvalidAccountId() {
        setSubjectWithPermissionAndPrincipal("account:manageaccounts:*", "user1");
        AccountCommand accountCommand = new AccountCommand();
        accountService.updateAccount(NON_EXISTING_ACCOUNT_ID, accountCommand);
    }

    @Test(expected = UnauthorizedException.class)
    public void userWithNoPermissionCannotUpdateAccount() {
        setSubjectWithPermissionAndPrincipal("account:manageads:1", "user1");
        AccountCommand accountCommand = new AccountCommand();
        accountService.updateAccount(1L, accountCommand);
    }

    @Test(expected = UnauthorizedException.class)
    public void userWithNoPermissionCannotCreateAccount() {
        setSubjectWithPermissionAndPrincipal("account:manageads:1", "user1");
        accountService.createAccount(new AccountCommand());
    }

    @Test
    @Transactional
    public void accountROIsReturned() {
        AccountService service = new DefaultAccountService(accountRepository, securityService, imageService, advertRepositoryService,
                accountImageRepository, categoryModel, eventService);
        Account account = service.getAccountRO(1L);
        assertThat(account.getId(), equalTo(1L));
        assertThat(account.getName(), equalTo("accountName"));
    }

    @Test
    @Transactional
    public void accountIsReturned() {
        AccountService service = new DefaultAccountService(accountRepository, securityService, imageService, advertRepositoryService,
                accountImageRepository, categoryModel, eventService);
        Account account = service.getAccount(1L);
        assertThat(account.getId(), equalTo(1L));
        assertThat(account.getName(), equalTo("accountName"));
    }

    @Test
    @Transactional
    public void accountIsNotReturned() {
        AccountService service = new DefaultAccountService(accountRepository, securityService, imageService, advertRepositoryService,
                accountImageRepository, categoryModel, eventService);
        Account account = service.getAccount(1000L);
        assertNull(account);
    }

    @Test
    @Transactional
    public void accountIsReturnedByPublicId() {
        AccountService service = new DefaultAccountService(accountRepository, securityService, imageService, advertRepositoryService,
                accountImageRepository, categoryModel, eventService);
        Account account = service.getAccountByPublicId("hash1");
        assertThat(account.getId(), equalTo(1L));
        assertThat(account.getName(), equalTo("accountName"));
        assertThat(account.getPublicId(), equalTo("hash1"));
    }

    @Test
    @Transactional
    public void accountIsNotReturnedByPublicId() {
        AccountService service = new DefaultAccountService(accountRepository, securityService, imageService, advertRepositoryService,
                accountImageRepository, categoryModel, eventService);
        Account account = service.getAccountByPublicId("SOME OTHER HASH");
        assertNull(account);
    }

    @Test
    @Transactional
    public void accountStatusDefaultsToActive() {
        AccountCommand accountCommand = createAccountCommandObject();
        setSubjectWithPermissionAndPrincipal("account:manageaccounts:*", "user1");
        Account account = accountService.createAccount(accountCommand);
        assertThat(account.getStatus(), equalTo(AccountStatus.ACTIVE));
    }

    @Test
    @Transactional
    public void shouldDeleteAutomaticPackagesWhenConvertingAccountToPro() {
        setSubjectWithPermissionAndPrincipal("account:manageaccounts:*", "user1");
        AccountCommand accountCommand = createAccountCommandObject();
        accountCommand.setPro(true);
        Account account = accountService.updateAccount(NON_PRO_ACCOUNT_ID, accountCommand);
        assertThat(account.getCreditPackages().size(), is(2));
        assertThat(account.getCreditPackages().get(0).getDeleted(), is(true));
        assertThat(account.getCreditPackages().get(1).getDeleted(), is(false));
    }

    @Test
    @Transactional
    public void shouldNotChangePackagesWhenUpdatingProAccount() {
        setSubjectWithPermissionAndPrincipal("account:manageaccounts:*", "user1");
        AccountCommand accountCommand = createAccountCommandObject();
        accountCommand.setPro(true);
        Account account = accountService.updateAccount(PRO_ACCOUNT_ID, accountCommand);
        assertThat(account.getCreditPackages().size(), is(2));
        assertThat(account.getCreditPackages().get(0).getDeleted(), is(false));
        assertThat(account.getCreditPackages().get(1).getDeleted(), is(false));
    }

    @Test
    @Transactional
    public void shouldConvertToProAccount() {
        AccountCommand accountCommand = createAccountCommandObject();
        accountCommand.setPro(true);
        Account account = accountService.convertTo(NON_PRO_ACCOUNT_ID, AccountType.PRO);
        assertThat(account.isProAccount(), is(true));
    }

    @Test
    @Transactional
    public void shouldConvertToStandardAccount() {
        AccountCommand accountCommand = createAccountCommandObject();
        accountCommand.setPro(true);
        Account account = accountService.convertTo(PRO_ACCOUNT_ID, AccountType.STANDARD);
        assertThat(account.isProAccount(), is(false));
    }

    @Test(expected = AccountNotFoundException.class)
    public void setDefaultImageThrowsAccountNotFoundExceptionIfAccountIdIsNotRecognised() {
        accountService.setDefaultImage(10L, 1L);
    }

    @Test(expected = ImageNotFoundException.class)
    public void setDefaultImageThrowsImageNotFoundExceptionIfImageIdIsNotRecognised() {
        accountService.setDefaultImage(1L, 10L);
    }

    @Test
    public void defaultImageIsPersistedCorrectly() {
        accountService.setDefaultImage(1L, 1L);
        assertThat(accountService.getAccount(1L).getDefaultImage().getId(), equalTo(1L));
    }

    @Test
    public void getDefaultImageIsNotFound() {
        assertThat(accountService.getDefaultImage(1010101010L), equalTo(null));
    }

    @Test
    public void defaultImageIsRetrievedCorrectly() {
        accountService.setDefaultImage(1L, 1L);
        assertThat(accountService.getDefaultImage(1L).getId(), equalTo(1L));
    }

    @Test
    public void defaultImageIsRemovedCorrectly() {
        accountService.setDefaultImage(1L, null);
        assertThat(accountService.getAccount(1L).getDefaultImage(), equalTo(null));
    }

    @Test
    public void defaultImageReturnsEpsOrCFUrl() {
        assertThat(accountService.getAccount(31114L).getDefaultImage().getUrl(), equalTo("https://i.ebayimg.com/00/s/1/$_86.PNG"));
        assertThat(accountService.getAccount(31115L).getDefaultImage().getUrl(), equalTo("https://imagedelivery.net/abc/2/1"));
        assertThat(accountService.getAccount(31116L).getDefaultImage().getUrl(), equalTo("https://i.ebayimg.com/00/s/3/$_86.PNG"));
        assertThat(accountService.getAccount(31117L).getDefaultImage().getUrl(), equalTo("https://imagedelivery.net/abc/4/1"));
    }

    @Test
    public void forcePostFlagIsUpdated() {
        // given
        final Account account = accountService.getAccount(1L);
        assertThat(account.isForcePostAsDealer(), is(false));

        // when
        accountService.updateForcePost(1L, CAR_DEALER, true);

        // then
        assertThat(accountService.getAccount(1L).isForcePostAsDealer(), is(true));
    }

    @Test
    public void sellerTypeForNonSellerTypeCategory() {
        // when
        SellerType sellerType = accountService.getSellerType(1l, CategoryConstants.TICKETS_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.NO_INFO);
    }

    @Test
    public void sellerTypeForAccountWithoutLiveAdsFSBOCategory() {
        // when
        SellerType sellerType = accountService.getSellerType(1l, Fixtures.AUDI_CAT_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.NO_INFO);
    }

    @Test
    public void sellerTypeForNonExistingAccount() {
        // when
        SellerType sellerType = accountService.getSellerType(NON_EXISTING_ACCOUNT_ID, CategoryConstants.CARS_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.NO_INFO);
    }

    @Test
    public void sellerTypeForAccountWithForceAdTradeFlag() {
        Assertions.assertThat(accountService.getSellerType(CARS_DEALER_ACCOUNT_ID, CategoryConstants.CARS_ID)).isEqualTo(SellerType.BUSINESS);
        Assertions.assertThat(accountService.getSellerType(CARS_DEALER_ACCOUNT_ID, Fixtures.BMW_CAT_ID)).isEqualTo(SellerType.BUSINESS);
        Assertions.assertThat(accountService.getSellerType(CARS_DEALER_ACCOUNT_ID, CategoryConstants.VANS_ID)).isEqualTo(SellerType.NO_INFO);
        Assertions.assertThat(accountService.getSellerType(CARS_DEALER_ACCOUNT_ID, CategoryConstants.TICKETS_ID)).isEqualTo(SellerType.NO_INFO);

        Assertions.assertThat(accountService.getSellerType(VANS_DEALER_ACCOUNT_ID, CategoryConstants.VANS_ID)).isEqualTo(SellerType.BUSINESS);
        Assertions.assertThat(accountService.getSellerType(VANS_DEALER_ACCOUNT_ID, CategoryConstants.CARS_ID)).isEqualTo(SellerType.NO_INFO);
    }

    @Test
    public void sellerTypeForProAccount() {
        // when
        SellerType sellerType = accountService.getSellerType(PRO_ACCOUNT_ID, CategoryConstants.CARS_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.BUSINESS);
    }

    @Test
    public void sellerTypeForAccountWithSingleLiveCarAd() {
        // given
        Assertions.assertThat(advertRepository.findByAccountId(31115L).get(0).getStatus()).isEqualTo(LIVE);

        // when
        SellerType sellerType = accountService.getSellerType(31115L, Fixtures.BMW_CAT_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.PRIVATE_WITH_LIVE_AD);
    }

    @Test
    public void sellerTypeForAccountWithSingleAwaitingCsReviewCarAd() {
        // given
        Assertions.assertThat(advertRepository.findByAccountId(31116L).get(0).getStatus()).isEqualTo(AdvertStatus.AWAITING_CS_REVIEW);

        // when
        SellerType sellerType = accountService.getSellerType(31116L, Fixtures.AUDI_CAT_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.PRIVATE_WITH_LIVE_AD);
    }

    @Test
    public void sellerTypeForAccountWithSingleAwaitingActivationCarAd() {
        // given
        Assertions.assertThat(advertRepository.findByAccountId(31117L).get(0).getStatus()).isEqualTo(AdvertStatus.AWAITING_ACTIVATION);

        // when
        SellerType sellerType = accountService.getSellerType(31117L, Fixtures.AUDI_CAT_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.PRIVATE_WITH_LIVE_AD);
    }

    @Test
    @Transactional
    public void shouldReturnEmailListGivenAccountId() {
        //        Given
        AccountService service = new DefaultAccountService(accountRepository, securityService, imageService, advertRepositoryService,
                accountImageRepository, categoryModel, eventService);

        //        When
        List<String> accountEmails = service.getAccountEmails(1L);

        //        Then
        assertThat(accountEmails.size(), equalTo(1));
        assertThat(accountEmails.get(0), equalTo("user1"));
    }

    @Test
    @Transactional
    public void shouldEmptyEmailListIfNoAccountFound() {
        //        Given
        AccountService service = new DefaultAccountService(accountRepository, securityService, imageService, advertRepositoryService,
                accountImageRepository, categoryModel, eventService);

        //        When
        List<String> accountEmails = service.getAccountEmails(345599L);

        //        Then
        assertThat(accountEmails.size(), equalTo(0));
    }

    @Test
    @Transactional
    public void accountThatPosted3AndMoreAdsInFSBOCatInLastYearIsSuspectedBusiness() {
        User usr = userRepository.findOne(Fixtures.DEF_USR_ID);

        // given
        advertRepository.save(ad(usr, LIVE).withCategory(Fixtures.BMW_CAT_ID).build());
        advertRepository.save(ad(usr, CREATED).withCategory(Fixtures.BMW_CAT_ID).build());
        advertRepository.save(ad(usr, EXPIRED).withCategory(Fixtures.BMW_CAT_ID).build());

        // when
        SellerType sellerType = accountService.getSellerType(Fixtures.DEF_ACC_ID, Fixtures.AUDI_CAT_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.SUSPECTED_BUSINESS);
    }

    @Test
    @Transactional
    public void accWith3AndMoreAdsInOtherFSBOCategory() {
        User usr = userRepository.findOne(Fixtures.DEF_USR_ID);

        // given - ads in non VANS categories
        advertRepository.save(ad(usr, LIVE).withCategory(Fixtures.BMW_CAT_ID).build());
        advertRepository.save(ad(usr, CREATED).withCategory(Fixtures.BMW_CAT_ID).build());
        advertRepository.save(ad(usr, EXPIRED).withCategory(Fixtures.BMW_CAT_ID).build());

        // when
        SellerType sellerType = accountService.getSellerType(Fixtures.DEF_ACC_ID, CategoryConstants.VANS_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.NO_INFO);
    }

    @Test
    @Transactional
    public void accountThatHasNotPosted3AndMoreAdsInFSBOCatInLastYear() {
        User usr = userRepository.findOne(Fixtures.DEF_USR_ID);

        // given
        advertRepository.save(ad(usr).withStatus(LIVE).withCategory(Fixtures.BMW_CAT_ID).build());
        advertRepository.save(ad(usr).withStatus(DELETED_USER).withCategory(Fixtures.BMW_CAT_ID).build());
        advertRepository.save(ad(usr).withStatus(EXPIRED).withCategory(CategoryConstants.VANS_ID).build());

        // when
        SellerType sellerType = accountService.getSellerType(Fixtures.DEF_ACC_ID, Fixtures.AUDI_CAT_ID);

        // then
        Assertions.assertThat(sellerType).isEqualTo(SellerType.PRIVATE_WITH_LIVE_AD);
    }

    @Test(expected = AccountNotFoundException.class)
    public void shouldThrowExceptionWhenUpdatingReplyTypeForNonExistingAccount() {
        accountService.updateReplyType(-1L, AccountReplyType.TRIFECTA);
    }

    @Test
    @Transactional
    public void shouldSuccessfullyUpdateReplyType() {
        accountService.updateReplyType(1L, AccountReplyType.TRIFECTA);

        final Account account = accountService.getAccount(1L);

        assertThat(account.getReplyType(), equalTo(AccountReplyType.TRIFECTA));
    }

    @Test
    @Transactional
    public void shouldSuccessfullyDelete() {
        // given
        Account account1 = accountService.getAccount(1L);
        Account account2 = accountService.getAccount(PRO_ACCOUNT_ID);
        assertThat(account1, is(notNullValue()));
        assertThat(account2, is(notNullValue()));

        // when
        accountService.delete(PRO_ACCOUNT_ID);
        accountRepository.flush(); // see in the SQL logs

        // then
        account1 = accountService.getAccount(1L);
        account2 = accountService.getAccount(PRO_ACCOUNT_ID);
        assertThat(account1, is(notNullValue()));
        assertThat(account2, is(nullValue()));
    }

    private void assertAccountProperties(Account account) {
        assertThat(account.getAddress1(), equalTo("address1"));
        assertThat(account.getAddress2(), equalTo("address2"));
        assertThat(account.getCity(), equalTo("city"));
        assertThat(account.getCounty(), equalTo("county"));
        assertThat(account.getCountry(), equalTo("country"));
        assertThat(account.isProAccount(), equalTo(true));
        assertThat(account.getId(), notNullValue());
        assertThat(account.getDescription(), equalTo("description"));
        assertThat(account.getName(), equalTo("name"));
    }

    private AccountCommand createAccountCommandObject() {
        AccountCommand accountCommand = new AccountCommand();
        accountCommand.setAddress1("address1");
        accountCommand.setAddress2("address2");
        accountCommand.setCity("city");
        accountCommand.setCounty("county");
        accountCommand.setCountry("country");
        accountCommand.setPro(true);
        accountCommand.setDescription("description");
        accountCommand.setPostcode("postcode");
        accountCommand.setName("name");
        accountCommand.setAccountStatus(AccountStatus.ACTIVE);
        return accountCommand;
    }
}
