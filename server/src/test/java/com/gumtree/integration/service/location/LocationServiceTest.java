package com.gumtree.integration.service.location;

import com.gumtree.seller.service.location.LocationService;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/locations-and-categories.xml", dataSourceName="sellerDatasource")
public class LocationServiceTest extends BaseIntegrationTest {

    @Autowired
    LocationService locationService;

    @Test
    public void locationLoadsNearbys() {
        assertThat(locationService.getById(1L).getNearbys().size(), equalTo(2));
        assertThat(locationService.getById(1L).getNearbys().get(0).getId(), equalTo(2L));
        assertThat(locationService.getById(1L).getNearbys().get(1).getId(), equalTo(3L));
    }

    @Test
    public void locationLoadsZoomIns() {
        assertThat(locationService.getById(2L).getZoomIns().size(), equalTo(2));
        assertThat(locationService.getById(2L).getZoomIns().get(0).getId(), equalTo(3L));
        assertThat(locationService.getById(2L).getZoomIns().get(1).getId(), equalTo(4L));
    }

    @Test
    public void locationLoadsZoomOuts() {
        assertThat(locationService.getById(3L).getZoomOuts().size(), equalTo(3));
        assertThat(locationService.getById(3L).getZoomOuts().get(0).getId(), equalTo(7L));
        assertThat(locationService.getById(3L).getZoomOuts().get(1).getId(), equalTo(8L));
        assertThat(locationService.getById(3L).getZoomOuts().get(2).getId(), equalTo(9L));
    }
}
