package com.gumtree.integration.email;

import com.gumtree.common.email.template.TemplateEmailModel;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.email.EmailCreator;
import com.gumtree.seller.email.EmailSender;
import com.gumtree.seller.email.EmailUrlScheme;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.subethamail.wiser.Wiser;
import org.subethamail.wiser.WiserMessage;

import javax.mail.internet.MimeMessage;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.equalTo;

@RunWith(SpringJUnit4ClassRunner.class)
public class EmailSenderIntegrationTest extends BaseIntegrationTest {

    @Autowired
    @Qualifier("defaultEmailSender")
    private EmailSender emailSender;

    private Wiser wiser;

    @Before
    public void init() {
        wiser = new Wiser();
        wiser.setPort(2500);
        wiser.start();
    }

    @After
    public void teardown() throws Exception {
        wiser.stop();
        wiser = null;
    }

    @Test
    public void testSendsEmail() throws Exception {

        // when
        emailSender.send(new TestEmailCreator());

        // then
        List<WiserMessage> messages = wiser.getMessages();
        assertThat(messages.size(), equalTo(1));

        MimeMessage message = messages.get(0).getMimeMessage();
        assertThat(message.getSubject(), equalTo("A Test Message"));
        assertThat(message.getAllRecipients()[0].toString(), equalTo("<EMAIL>"));
        assertThat(message.getFrom()[0].toString(), equalTo("<EMAIL>"));
        assertThat(message.getContent().toString(), containsString("Hello Joe!"));
    }

    public class TestEmailCreator implements EmailCreator {
        @Override
        public void createEmail(TemplateEmailModel model, EmailUrlScheme urlScheme) {
            model.setToAddress("<EMAIL>");
            model.setSubject("A Test Message");
            model.setFromAddress("<EMAIL>");
            model.addObject("name", "Joe");
            model.setPlainTextTemplate("test-email-template");
        }
    }
}
