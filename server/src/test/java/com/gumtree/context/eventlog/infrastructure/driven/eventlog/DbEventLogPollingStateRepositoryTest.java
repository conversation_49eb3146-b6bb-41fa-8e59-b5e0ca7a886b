package com.gumtree.context.eventlog.infrastructure.driven.eventlog;

import com.gumtree.context.eventlog.domain.EventLogPollingStateRepository;
import com.gumtree.context.bapi.infrastructure.BapiAggregateType;
import com.gumtree.integration.BaseIntegrationTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class DbEventLogPollingStateRepositoryTest extends BaseIntegrationTest {

    @Autowired
    private EventLogPollingStateRepository eventLogPollingStateManager;

    @Test
    @Transactional
    public void shouldBePossibleToRetrieveInitialValue() {
        assertThat(eventLogPollingStateManager.retrieveAndLockSequenceNumber(BapiAggregateType.USER, "test1"), equalTo(-1L));
    }

    @Test
    @Ignore // postgres syntax error
    @Transactional
    public void shouldBePossibleToUpdateValueOfTheSequence() {
        //when
        eventLogPollingStateManager.storeNewSequenceNumber(BapiAggregateType.USER, 10L, "test1");

        //then
        assertThat(eventLogPollingStateManager.retrieveAndLockSequenceNumber(BapiAggregateType.USER, "test1"), equalTo(10L));
        assertThat(eventLogPollingStateManager.retrieveAndLockSequenceNumber(BapiAggregateType.ADVERT, "test1"), equalTo(-1L));
        assertThat(eventLogPollingStateManager.retrieveAndLockSequenceNumber(BapiAggregateType.ADVERT, "test2"), equalTo(-1L));
    }

    @Test
    @Ignore // postgres syntax error
    @Transactional
    public void shouldNotBePossibleToDecrementTheSequence() {
        //given
        eventLogPollingStateManager.storeNewSequenceNumber(BapiAggregateType.USER, 10L, "test3");

        //when
        eventLogPollingStateManager.storeNewSequenceNumber(BapiAggregateType.USER, 5L, "test3");

        //then
        assertThat(eventLogPollingStateManager.retrieveAndLockSequenceNumber(BapiAggregateType.USER, "test3"), equalTo(10L));
        assertThat(eventLogPollingStateManager.retrieveAndLockSequenceNumber(BapiAggregateType.ADVERT, "test3"), equalTo(-1L));
    }

}