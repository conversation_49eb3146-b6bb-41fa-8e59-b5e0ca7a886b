package com.gumtree.context.eventlog.infrastructure.driven.eventlog;

import com.gumtree.context.bapi.domain.EventPopulationService;
import com.gumtree.seller.domain.common.EventProducingAggregate;
import com.gumtree.seller.event.AggregateEvent;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEvent;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class DomainEventPopulationApplicationListenerTest {

    static class TestApplicationEvent extends AggregateEvent {


        public TestApplicationEvent(Object source, EventProducingAggregate aggregate) {
            super(source, aggregate);
        }
    }

    static class SpecificApplicationEvent extends TestApplicationEvent {

        public SpecificApplicationEvent(Object source, EventProducingAggregate aggregate) {
            super(source, aggregate);
        }
    }

    @Mock
    private EventPopulationService<TestApplicationEvent> eventPopulationService;

    private DomainEventPopulationApplicationListener<TestApplicationEvent> eventListener;

    @Before
    public void setUp() {
        eventListener = new DomainEventPopulationApplicationListener<>(eventPopulationService, TestApplicationEvent.class);
    }

    @Test
    public void shouldForwardEventToEventPopulationService() {
        //given
        TestApplicationEvent event = new TestApplicationEvent(this, null);

        //when
        eventPopulationService.handleEvent(event);

        //then
        verify(eventPopulationService).handleEvent(event);
    }

    @Test
    public void shouldSupportAllSenderTypes() {
        assertThat(eventListener.supportsSourceType(String.class), equalTo(true));
        assertThat(eventListener.supportsSourceType(DomainEventPopulationApplicationListenerTest.class), equalTo(true));
    }

    @Test
    public void shouldSupportOnlySpecifiedEventTypeAndItsSubclasses() {
        assertThat(eventListener.supportsEventType(TestApplicationEvent.class), equalTo(true));
        assertThat(eventListener.supportsEventType(SpecificApplicationEvent.class), equalTo(true));
    }

    @Test
    public void shouldNotSupportOtherEvents() {
        assertThat(eventListener.supportsEventType(ApplicationEvent.class), equalTo(false));
    }


}