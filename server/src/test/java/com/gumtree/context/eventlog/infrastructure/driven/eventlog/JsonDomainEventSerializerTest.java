package com.gumtree.context.eventlog.infrastructure.driven.eventlog;

import com.gumtree.ddd.patterns.DomainEventBase;
import org.junit.Test;

import java.time.Instant;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class JsonDomainEventSerializerTest {

    JsonDomainEventSerializer<DomainEventBase<Long>> serializer = new JsonDomainEventSerializer<>();

    static class TestEvent extends DomainEventBase<Long> {

        TestEvent() {
        }

        public TestEvent(Long id, Long version, Instant timestamp) {
            super(id, version, timestamp);
        }
    }

    @Test
    public void shouldSerializeAndDeserializeEvent() {

        //given
        TestEvent event = new TestEvent(1L, 2L, Instant.now());

        //when
        String serializedEvent = serializer.serialize(event);

        TestEvent deserializedEvent = (TestEvent)serializer.deserialize(TestEvent.class.getName(), serializedEvent);

        //then
        assertThat(deserializedEvent.getAggregateId(), equalTo(event.getAggregateId()));
        assertThat(deserializedEvent.getSequenceNumber(), equalTo(event.getSequenceNumber()));
        assertThat(deserializedEvent.getTimestamp(), equalTo(event.getTimestamp()));
        assertThat(deserializedEvent.getType(), equalTo(event.getType()));

    }

    @Test(expected = RuntimeException.class)
    public void deserializationShouldFailIfUnknownType() {
        serializer.deserialize("unkown", "{}");
    }

    @Test
    public void deserializationShouldFailIfInvalidContent() {
        serializer.deserialize(TestEvent.class.getName(), "{ \"id\" : \"false\"}");
    }

}