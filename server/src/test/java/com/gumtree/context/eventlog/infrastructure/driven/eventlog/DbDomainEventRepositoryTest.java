package com.gumtree.context.eventlog.infrastructure.driven.eventlog;

import static java.time.Duration.ofMinutes;
import static java.util.stream.Collectors.toList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import com.google.common.collect.Lists;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.context.bapi.infrastructure.BapiAggregateType;
import com.gumtree.context.eventlog.domain.DomainEventRepository;
import com.gumtree.context.eventlog.domain.EventLogEntry;
import com.gumtree.context.listing.domain.event.AdvertUnpublished;
import com.gumtree.context.user.domain.event.AccountDetails;
import com.gumtree.context.user.domain.event.UserRegistered;
import com.gumtree.ddd.patterns.DomainEventBase;
import com.gumtree.integration.BaseIntegrationTest;
import com.gumtree.seller.domain.account.type.AccountType;
import com.gumtree.seller.domain.user.entity.Role;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DbDomainEventRepositoryTest extends BaseIntegrationTest {

    @Autowired
    private DomainEventRepository<BapiAggregateType> domainEventRepository;

    @Autowired
    private DomainEventSerializer<DomainEventBase> domainEventSerializer;

    @Autowired
    @Qualifier("sellerDatabaseOperations")
    private JdbcOperations jdbcOperations;

    @Autowired
    private StoppedClock testClock;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Before
    public void setUp() {
        jdbcOperations.update("delete from event_log");

    }

    private List<EventLogEntry> serialize(List<DomainEventBase<Long>> events) {
        return events.stream().map(e -> new EventLogEntry(
                null,
                e.getType(),
                e.getAggregateId(),
                e.getSequenceNumber(),
                domainEventSerializer.serialize(e),
                e.getTimestamp())).collect(toList());
    }

    @Test
    @Transactional
    public void shouldPurgeEvents() {
        List<AccountDetails> accounts = new ArrayList<AccountDetails>();
        accounts.add(new AccountDetails(1L, "Test Account Name", AccountType.STANDARD, true, false));

        List<Role.RoleName> roles = new ArrayList<Role.RoleName>();
        roles.add(Role.RoleName.API_USER);
        roles.add(Role.RoleName.JOBS_USER);


        //given
        List<DomainEventBase<Long>> events1 = Lists.newArrayList(
                new UserRegistered(fixedNow(), 11L, 21L, "<EMAIL>", "<EMAIL>", "Test", "User", accounts, roles),
                new UserRegistered(fixedNow(), 12L, 22L, "<EMAIL>", "<EMAIL>", "Test", "User", accounts, roles)
        );
        domainEventRepository.storeDomainEvents(BapiAggregateType.USER, serialize(events1));

        Date testTime = testClock.getDate();

        testClock.setDate(Date.from(fixedNow().minus(ofMinutes(60))));

        List<DomainEventBase<Long>> events3 = Lists.newArrayList(
                new UserRegistered(fixedNow().minus(ofMinutes(60)), 13L, 23L, "<EMAIL>", "<EMAIL>","Test", "User", accounts, roles),
                new UserRegistered(fixedNow().minus(ofMinutes(60)), 14L, 24L, "<EMAIL>", "<EMAIL>","Test", "User", accounts, roles)
        );
        domainEventRepository.storeDomainEvents(BapiAggregateType.USER, serialize(events3));

        testClock.setDate(testTime);

        List<DomainEventBase<Long>> events2 = Lists.newArrayList(
                new AdvertUnpublished(11L, 21L, fixedNow()),
                new AdvertUnpublished(12L, 22L, fixedNow())
        );

        domainEventRepository.storeDomainEvents(BapiAggregateType.ADVERT, serialize(events2));

        //when
        int numberOfEventsDeleted = domainEventRepository.purgeEventsByDate(BapiAggregateType.USER, fixedNowLocalDate().minusMinutes(59));
        assertThat(numberOfEventsDeleted, equalTo(2));

        //then
        List<DomainEventBase> domainEventPage = domainEventRepository.getEvents(BapiAggregateType.USER, 0, 100).stream().
                map(e -> domainEventSerializer.deserialize(e.getType(), e.getPayload())).collect(toList());;
        assertThat(domainEventPage.size(), equalTo(2));

        assertThat(domainEventPage.get(0).getType(), equalTo(UserRegistered.class.getName()));
        assertThat(domainEventPage.get(0).getSequenceNumber(), equalTo(21L));
        assertThat(domainEventPage.get(0).getAggregateId(), equalTo(11L));
        assertThat(domainEventPage.get(0).getTimestamp(), equalTo(fixedNow()));

        assertThat(domainEventPage.get(1).getType(), equalTo(UserRegistered.class.getName()));
        assertThat(domainEventPage.get(1).getSequenceNumber(), equalTo(22L));
        assertThat(domainEventPage.get(1).getAggregateId(), equalTo(12L));
        assertThat(domainEventPage.get(1).getTimestamp(), equalTo(fixedNow()));

        // and the purge job did not touch any of the advert events
        assertThat(domainEventRepository.getEvents(BapiAggregateType.ADVERT, 0, 100).size(), equalTo(2));
    }

    @Test
    @Transactional
    public void shouldAllowToStoreAndReadEvents() {
        List<AccountDetails> accounts = new ArrayList<AccountDetails>();
        accounts.add(new AccountDetails(1L, "Test Account Name", AccountType.STANDARD, true, false));

        List<Role.RoleName> roles = new ArrayList<Role.RoleName>();
        roles.add(Role.RoleName.API_USER);
        roles.add(Role.RoleName.JOBS_USER);

        //given
        List<DomainEventBase<Long>> events = Lists.newArrayList(
                new UserRegistered(fixedNow(), 11L, 21L, "<EMAIL>", "<EMAIL>", "Test", "User", accounts, roles),
                new UserRegistered(fixedNow(), 12L, 22L, "<EMAIL>", "<EMAIL>", "Test", "User", accounts, roles)
        );

        //when
        domainEventRepository.storeDomainEvents(BapiAggregateType.ADVERT, serialize(events));

        //then
        List<DomainEventBase> domainEventPage = domainEventRepository.getEvents(BapiAggregateType.ADVERT, 0, 100).stream().
                map(e -> domainEventSerializer.deserialize(e.getType(), e.getPayload())).collect(toList());;
        assertThat(domainEventPage.size(), equalTo(2));

        assertThat(domainEventPage.get(0).getType(), equalTo(UserRegistered.class.getName()));
        assertThat(domainEventPage.get(0).getSequenceNumber(), equalTo(21L));
        assertThat(domainEventPage.get(0).getAggregateId(), equalTo(11L));
        assertThat(domainEventPage.get(0).getTimestamp(), equalTo(fixedNow()));

        assertThat(domainEventPage.get(1).getType(), equalTo(UserRegistered.class.getName()));
        assertThat(domainEventPage.get(1).getSequenceNumber(), equalTo(22L));
        assertThat(domainEventPage.get(1).getAggregateId(), equalTo(12L));
        assertThat(domainEventPage.get(1).getTimestamp(), equalTo(fixedNow()));

    }

    @Test
    @Transactional
    public void shouldStoreEventsOfDifferentAggregateTypesAndSuccessfullyQueryByType() {
        List<AccountDetails> accounts = new ArrayList<AccountDetails>();
        accounts.add(new AccountDetails(1L, "Test Account Name", null, true, false));

        List<Role.RoleName> roles = new ArrayList<Role.RoleName>();
        roles.add(Role.RoleName.API_USER);
        roles.add(Role.RoleName.JOBS_USER);

        //given
        List<DomainEventBase<Long>> userEvents = Lists.newArrayList(
                new UserRegistered(fixedNow(), 12L, 22L, "<EMAIL>", "<EMAIL>", "Test", "User", accounts, roles)
        );

        List<DomainEventBase<Long>> advertEvents = Lists.newArrayList(
                new AdvertUnpublished(11L, 21L, fixedNow())
        );

        //when
        domainEventRepository.storeDomainEvents(BapiAggregateType.USER, serialize(userEvents));
        domainEventRepository.storeDomainEvents(BapiAggregateType.ADVERT, serialize(advertEvents));

        //then
        List<DomainEventBase> advertEventPage = domainEventRepository.getEvents(BapiAggregateType.ADVERT, 0, 100).stream().
                map(e -> domainEventSerializer.deserialize(e.getType(), e.getPayload())).collect(toList());
        assertThat(advertEventPage.size(), equalTo(1));

        assertThat(advertEventPage.get(0).getType(), equalTo(AdvertUnpublished.class.getName()));
        assertThat(advertEventPage.get(0).getSequenceNumber(), equalTo(21L));
        assertThat(advertEventPage.get(0).getAggregateId(), equalTo(11L));
        assertThat(advertEventPage.get(0).getTimestamp(), equalTo(fixedNow()));

        List<DomainEventBase> userEventPage = domainEventRepository.getEvents(BapiAggregateType.USER, 0, 100).stream().
            map(e -> domainEventSerializer.deserialize(e.getType(), e.getPayload())).collect(toList());

        assertThat(userEventPage.size(), equalTo(1));

        assertThat(userEventPage.get(0).getType(), equalTo(UserRegistered.class.getName()));
        assertThat(userEventPage.get(0).getSequenceNumber(), equalTo(22L));
        assertThat(userEventPage.get(0).getAggregateId(), equalTo(12L));
        assertThat(userEventPage.get(0).getTimestamp(), equalTo(fixedNow()));
    }

    @Test
    @Transactional
    public void shouldProperlyHandlePaging() {

        //given
        List<DomainEventBase<Long>> events = Lists.newArrayList();

        for (int i = 0; i < 15; i++) {
            events.add(new AdvertUnpublished((long)i, 21L, fixedNow()));
        }

        //when
        domainEventRepository.storeDomainEvents(BapiAggregateType.ADVERT, serialize(events));

        //then
        List<EventLogEntry> firstPage = domainEventRepository.getEvents(BapiAggregateType.ADVERT, 0, 10);
        assertThat(firstPage.size(), equalTo(10));

        List<EventLogEntry> secondPage = domainEventRepository.getEvents(BapiAggregateType.ADVERT, (Long)firstPage.get(9).getId(), 10);
        assertThat(secondPage.size(), equalTo(5));

    }

    @Test
    @Transactional
    public void shouldThrowExceptionWhenTryingToStoreUnknownEventType() throws Exception {
        List<AccountDetails> accounts = new ArrayList<AccountDetails>();
        accounts.add(new AccountDetails(1L, "Test Account Name", AccountType.STANDARD, true, false));

        List<Role.RoleName> roles = new ArrayList<Role.RoleName>();
        roles.add(Role.RoleName.API_USER);
        roles.add(Role.RoleName.JOBS_USER);

        //then
        thrown.expect(NullPointerException.class);

        //given
        List<DomainEventBase<Long>> events = Lists.newArrayList(
                new UserRegistered(fixedNow(), 11L, 21L, "<EMAIL>", "<EMAIL>","Test", "User", accounts, roles),
                new UserRegistered(fixedNow(), 12L, 22L, "<EMAIL>", "<EMAIL>","Test", "User", accounts, roles)
        );

        //when
        domainEventRepository.storeDomainEvents(null, serialize(events));
    }

    @Test
    @Transactional
    public void shouldThrowExceptionWhenTryingToQueryUnknownEventTypes() {
        //then
        thrown.expect(NullPointerException.class);

        //when
        domainEventRepository.getEvents(null, 0, 100);
    }

    private Instant fixedNow() {
        return Instant.ofEpochMilli(testClock.now());
    }

    private LocalDateTime fixedNowLocalDate() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(testClock.now()), ZoneId.systemDefault());
    }
}
