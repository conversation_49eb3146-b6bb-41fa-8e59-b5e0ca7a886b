package com.gumtree.context.eventlog.integration;

import com.gumtree.api.mvc.AbstractMvcTest;
import com.gumtree.common.util.json.SnakeCaseObjectMapperFactory;
import com.gumtree.context.eventlog.domain.EventLogEntry;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class EventLogPollingControllerMvcTest extends AbstractMvcTest {

    ObjectMapper objectMapper = new SnakeCaseObjectMapperFactory().create();

    @Override
    protected String getDataset() {
        return "classpath:mvc/data/eventlog-dataset.xml";
    }

    @Test
    public void shouldBePossibleToPurgeOldEvents() throws Exception {
        //given
        MockHttpServletRequestBuilder request = DELETE(signRequestPath("/eventlog/ADVERT", ACCESS_KEY, PRIVATE_KEY)).
                param("beforeTimestamp", "2016-06-30T10:00:00");


        //when
        MvcResult result = mvc.perform(request).andReturn();

        //then
        assertThat(result.getResponse().getStatus(), equalTo(200));

        Integer numberOfEventsPurged = Integer.parseInt(result.getResponse().getContentAsString());
        assertThat(numberOfEventsPurged, equalTo(2));
    }

    @Test
    public void shouldBePossibleToRetrieveNewEvents() throws Exception {


        //given
        MockHttpServletRequestBuilder request = GET(signRequestPath("/eventlog/ADVERT", ACCESS_KEY, PRIVATE_KEY)).
                param("consumerGroupId", "CID1");

        //when
        MvcResult result = mvc.perform(request).andReturn();

        //then
        assertThat(result.getResponse().getStatus(), equalTo(200));

        List<EventLogEntry> domainEvents = objectMapper.readValue(
                result.getResponse().getContentAsString(), new TypeReference<List<EventLogEntry>>(){});

        assertThat(domainEvents.size(), equalTo(3));
        assertThat(domainEvents.get(0).getId(), equalTo(1L));
        assertThat(domainEvents.get(1).getId(), equalTo(2L));
        assertThat(domainEvents.get(2).getId(), equalTo(3L));


    }

    @Test
    public void shouldBePossibleToRetrieveEventsFromArbitraryPosition() throws Exception {


        //given
        MockHttpServletRequestBuilder request = GET(signRequestPath("/eventlog/ADVERT", ACCESS_KEY, PRIVATE_KEY)).
                param("position", "2");

        //when
        MvcResult result = mvc.perform(request).andReturn();

        //then
        assertThat(result.getResponse().getStatus(), equalTo(200));

        List<EventLogEntry> domainEvents = objectMapper.readValue(
                result.getResponse().getContentAsString(), new TypeReference<List<EventLogEntry>>(){});

        assertThat(domainEvents.size(), equalTo(1));
        assertThat(domainEvents.get(0).getId(), equalTo(3L));

    }

    @Ignore // postgres syntax error
    @Test
    public void shouldBePossibleToStoreEventPollingPosition() throws Exception {
        //given
        mvc.perform(POST(signRequestPath("/eventlog/ADVERT/commit", ACCESS_KEY, PRIVATE_KEY)).
                param("consumerGroupId", "CID1").
                param("position", "2"));

        MockHttpServletRequestBuilder request = GET(signRequestPath("/eventlog/ADVERT", ACCESS_KEY, PRIVATE_KEY)).
                param("consumerGroupId", "CID1");

        //when
        MvcResult result = mvc.perform(request).andReturn();

        //then
        assertThat(result.getResponse().getStatus(), equalTo(200));

        List<EventLogEntry> domainEvents = objectMapper.readValue(
                result.getResponse().getContentAsString(), new TypeReference<List<EventLogEntry>>(){});

        assertThat(domainEvents.size(), equalTo(1));
        assertThat(domainEvents.get(0).getId(), equalTo(3L));
    }

    @Ignore // postgres syntax error
    @Test
    public void shouldReturnEmptyResultWhenNoNewEvents() throws Exception{
        //given
        mvc.perform(POST(signRequestPath("/eventlog/ADVERT/commit", ACCESS_KEY, PRIVATE_KEY)).
                param("consumerGroupId", "CID1").
                param("position", "10"));

        MockHttpServletRequestBuilder request = GET(signRequestPath("/eventlog/ADVERT", ACCESS_KEY, PRIVATE_KEY)).
                param("consumerGroupId", "CID1");

        //when
        MvcResult result = mvc.perform(request).andReturn();

        //then
        assertThat(result.getResponse().getStatus(), equalTo(200));

        List<EventLogEntry> domainEvents = objectMapper.readValue(
                result.getResponse().getContentAsString(), new TypeReference<List<EventLogEntry>>(){});

        assertThat(domainEvents.size(), equalTo(0));
    }

    protected boolean isDirty() {
        return true;
    }

}

