package com.gumtree.context.bapi.domain;

import com.gumtree.common.properties.GtPropManager;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.ddd.patterns.DomainEventBase;
import org.junit.Before;
import org.junit.Test;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import static java.time.Instant.now;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class EventPopulationServiceImplTest {

    static class TestEvent extends DomainEventBase<Long> {

        public Object baseEvent;

        public TestEvent(Long id, Long version, Instant timestamp, Object baseEvent) {
            super(id, version, timestamp);
            this.baseEvent = baseEvent;
        }
    }

    private List<DomainEventBase> eventsProcessedByInSink1;

    private List<DomainEventBase> eventsProcessedByInSink2;

    PropSupplier<Boolean> enabledProperty;

    DomainEventFactory<?> domainEventFactory = source ->
            "EMPTY".equals(source) ?
            emptyList() :
            asList(new TestEvent(1L, 1L, now(), source), new TestEvent(2L, 2L, now(), source)
    );

    DomainEventSink<DomainEventBase<?>> sink1 = events -> eventsProcessedByInSink1.addAll(events);

    DomainEventSink<DomainEventBase<?>> sink2 = events -> eventsProcessedByInSink2.addAll(events);

    EventPopulationService eventPopulationService;

    @Before
    public void setUp() {
        eventsProcessedByInSink1 = new ArrayList<>();
        eventsProcessedByInSink2 = new ArrayList<>();
        GtPropManager.setProperty("testProperty", "true");
        enabledProperty = GtProps.getDBool("testProperty");
        eventPopulationService = new EventPopulationServiceImpl(domainEventFactory, enabledProperty, sink1, sink2);
    }


    @Test
    public void shouldEmitEventsToAllSinks() {

        //when
        eventPopulationService.handleEvent("TEST");

        //then
        assertThat(eventsProcessedByInSink1.get(0).getAggregateId(), equalTo(1L));
        assertThat(eventsProcessedByInSink1.get(0).getType(), equalTo(TestEvent.class.getName()));
        assertThat(((TestEvent)eventsProcessedByInSink1.get(0)).baseEvent, equalTo("TEST"));

        assertThat(eventsProcessedByInSink1.get(1).getAggregateId(), equalTo(2L));
        assertThat(eventsProcessedByInSink1.get(1).getType(), equalTo(TestEvent.class.getName()));
        assertThat(((TestEvent)eventsProcessedByInSink1.get(1)).baseEvent, equalTo("TEST"));


        assertThat(eventsProcessedByInSink2.get(0).getAggregateId(), equalTo(1L));
        assertThat(eventsProcessedByInSink2.get(0).getType(), equalTo(TestEvent.class.getName()));
        assertThat(((TestEvent)eventsProcessedByInSink2.get(0)).baseEvent, equalTo("TEST"));

        assertThat(eventsProcessedByInSink2.get(1).getAggregateId(), equalTo(2L));
        assertThat(eventsProcessedByInSink2.get(1).getType(), equalTo(TestEvent.class.getName()));
        assertThat(((TestEvent)eventsProcessedByInSink2.get(1)).baseEvent, equalTo("TEST"));
    }

    @Test
    public void shouldNotProcessEventIfNotEnabled() {

        GtPropManager.setProperty("testProperty", "false");

        //when
        eventPopulationService.handleEvent("TEST");

        //then
        assertThat(eventsProcessedByInSink1.size(), equalTo(0));
        assertThat(eventsProcessedByInSink2.size(), equalTo(0));
    }

    @Test
    public void shouldNotCallSinksIfNoEventsEmited() {
        //when
        eventPopulationService.handleEvent("EMPTY");

        //then
        assertThat(eventsProcessedByInSink1.size(), equalTo(0));
        assertThat(eventsProcessedByInSink2.size(), equalTo(0));
    }

}