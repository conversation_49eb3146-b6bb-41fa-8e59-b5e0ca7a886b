package com.gumtree.context.bapi.domain.listingevents;

import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.image.entity.Image;
import org.junit.Test;

import static com.google.common.collect.Lists.newArrayList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

public class AdvertConversionHelperTest {

    public static final int IMAGE_COUNT = 3;

    @Test
    public void convertPrimaryImageUrl() {
        assertThat(AdvertConversionHelper.convertPrimaryImageUrl(advertWithPrimaryImage()), is(image(1).getUrl()));
        assertThat(AdvertConversionHelper.convertPrimaryImageUrl(advertWithoutPrimaryImage()), is(image(1).getUrl()));
    }

    @Test
    public void convertAdditionalImageUrls() {
        assertThat(AdvertConversionHelper.convertAdditionalImageUrls(advertWithPrimaryImage()), contains(image(2).getUrl(), image(3).getUrl()));
        assertThat(AdvertConversionHelper.convertAdditionalImageUrls(advertWithoutPrimaryImage()), contains(image(2).getUrl(), image(3).getUrl()));
    }

    private Advert advertWithPrimaryImage() {
        Advert advert = new Advert();
        advert.setImages(newArrayList(image(1L), image(2L), image(3L)), image(1L));
        assertThat(advert.getImages(), hasSize(IMAGE_COUNT));
        assertThat(advert.getPrimaryImage(), is(notNullValue()));
        return advert;
    }

    private Advert advertWithoutPrimaryImage() {
        Advert advert = new Advert();
        advert.setImages(newArrayList(image(1L), image(2L), image(3L)), image(4L));
        assertThat(advert.getImages(), hasSize(IMAGE_COUNT));
        return advert;
    }

    private Image image(long id) {
        Image image = new Image();
        image.setId(id);
        image.setUrl("http://"+id);
        return image;
    }

}