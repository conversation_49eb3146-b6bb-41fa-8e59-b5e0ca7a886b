package com.gumtree.context.bapi.domain.userevents;

import com.google.common.collect.Lists;
import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.context.user.domain.event.AccountDetails;
import com.gumtree.context.user.domain.event.UserDetailsChanged;
import com.gumtree.context.user.domain.event.UserRegistered;
import com.gumtree.ddd.patterns.DomainEventBase;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.account.type.AccountType;
import com.gumtree.seller.domain.email.entity.ContactEmail;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.event.user.BeforeUserActivatedEvent;
import com.gumtree.seller.event.user.BeforeUserDeactivatedEvent;
import com.gumtree.seller.event.user.BeforeUserDetailsChangedEvent;
import com.gumtree.seller.event.user.UserPreChangeEvent;
import com.gumtree.seller.repository.email.ContactEmailRepository;
import org.junit.Test;

import java.io.IOException;
import java.time.Instant;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class UserDomainEventFactoryTest {

    Clock clock = new StoppedClock();

    ContactEmailRepository contactEmailRepository = mock(ContactEmailRepository.class);

    UserDomainEventFactory userDomainEventFactory = new UserDomainEventFactory(clock, contactEmailRepository);

    @Test
    public void shouldEmitUserRegisteredEventWhenUserActivated() throws IOException {
        //given
        User user = new User.Builder().withId(1L).withEmailAddress(new EmailAddress.Builder().withEmail("<EMAIL>")).build();
        user.setLastEventSequenceNumber(2L);
        BeforeUserActivatedEvent userActivatedEvent = new BeforeUserActivatedEvent(this, user);

        //when
        List<DomainEventBase<?>> storedEvents = userDomainEventFactory.buildEvents(userActivatedEvent);

        //then
        assertThat(storedEvents.size(), equalTo(1));
        DomainEventBase<?> event = storedEvents.get(0);

        assertThat(event.getAggregateId(), equalTo(user.getId()));
        assertThat(event.getSequenceNumber(), equalTo(3L));
        assertThat(event.getType(), equalTo("com.gumtree.context.user.domain.event.UserRegistered"));
        assertThat(event.getTimestamp(), equalTo(now()));

        UserRegistered eventPayload = (UserRegistered) event;
        assertThat(eventPayload.getAggregateId(), equalTo(user.getId()));
        assertThat(eventPayload.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(eventPayload.getTimestamp(), equalTo(now()));

    }

    @Test
    public void shouldEmitUserDeactivationEventWhenUserDeactivated() throws IOException {
        //given
        User user = new User.Builder().withId(1L).withEmailAddress(new EmailAddress.Builder().withEmail("<EMAIL>")).build();
        user.setLastEventSequenceNumber(2L);
        BeforeUserDeactivatedEvent userActivatedEvent = new BeforeUserDeactivatedEvent(this, user);

        //when
        List<DomainEventBase<?>> storedEvents = userDomainEventFactory.buildEvents(userActivatedEvent);

        //then
        assertThat(storedEvents.size(), equalTo(1));
        DomainEventBase<?> event = storedEvents.get(0);

        assertThat(event.getAggregateId(), equalTo(user.getId()));
        assertThat(event.getSequenceNumber(), equalTo(3L));
        assertThat(event.getType(), equalTo("com.gumtree.context.user.domain.event.UserUnregistered"));
        assertThat(event.getTimestamp(), equalTo(now()));

    }

    @Test
    public void shouldEmitUserDetailsChangedEventWhenUserModified() throws IOException {
        Account account = new Account.Builder().isPro(true).forcePostAsAgency(true).withId(10L).withName("foobar").build();
        User user = new User.Builder().withId(1L)
                .withEmailAddress(new EmailAddress.Builder().withEmail("<EMAIL>"))
                .linkedToAccount(account)
                .build();
        user.setLastEventSequenceNumber(2L);
        BeforeUserDetailsChangedEvent userDetailsChangedEvent = new BeforeUserDetailsChangedEvent(this, user);

        List<DomainEventBase<?>> storedEvents = userDomainEventFactory.buildEvents(userDetailsChangedEvent);

        assertThat(storedEvents.size(), equalTo(1));
        DomainEventBase<?> event = storedEvents.get(0);

        UserDetailsChanged eventPayload = (UserDetailsChanged) event;

        assertThat(event.getAggregateId(), equalTo(user.getId()));
        assertThat(event.getSequenceNumber(), equalTo(3L));
        assertThat(event.getType(), equalTo("com.gumtree.context.user.domain.event.UserDetailsChanged"));
        assertThat(event.getTimestamp(), equalTo(now()));

        assertThat(eventPayload.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(eventPayload.getAccounts().size(), equalTo(1));
        AccountDetails accountDetails = eventPayload.getAccounts().get(0);
        assertThat(accountDetails.getAccountId(), equalTo(10L));
        assertThat(accountDetails.getAccountType(), equalTo(AccountType.PRO));
        assertThat(accountDetails.getAgency(), equalTo(true));
        assertThat(accountDetails.getDealer(), equalTo(false));
        assertThat(accountDetails.getAccountName(), equalTo("foobar"));
    }

    @Test
    public void shouldEmitEventWithPreferredEmailIfSet() throws IOException {
        ContactEmail contactEmail1 = ContactEmail.builder().email("<EMAIL>").preferred(false).build();
        ContactEmail contactEmail2 = ContactEmail.builder().email("<EMAIL>").preferred(true).build();

        UserDetailsChanged eventPayload = getUserDetailsChangedGivenContactEmails(Lists.newArrayList(contactEmail1, contactEmail2));
        assertThat(eventPayload.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(eventPayload.getContactEmail(), equalTo("<EMAIL>"));
    }

    @Test
    public void shouldEmitEventWithUserEmailIfNoPreferredEmailSet() throws IOException {
        ContactEmail contactEmail1 = ContactEmail.builder().email("<EMAIL>").preferred(false).build();
        ContactEmail contactEmail2 = ContactEmail.builder().email("<EMAIL>").preferred(false).build();

        UserDetailsChanged eventPayload = getUserDetailsChangedGivenContactEmails(Lists.newArrayList(contactEmail1, contactEmail2));
        assertThat(eventPayload.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(eventPayload.getContactEmail(), equalTo("<EMAIL>"));
    }

    @Test
    public void shouldEmitEventWithUserEmailIfNoContactEmailsSet() throws IOException {
        UserDetailsChanged eventPayload = getUserDetailsChangedGivenContactEmails(Collections.emptyList());
        assertThat(eventPayload.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(eventPayload.getContactEmail(), equalTo("<EMAIL>"));
    }

    private UserDetailsChanged getUserDetailsChangedGivenContactEmails(List<ContactEmail> contactEmails) {
        User user = new User.Builder().withId(1L)
                .withEmailAddress(new EmailAddress.Builder().withEmail("<EMAIL>"))
                .build();
        when(contactEmailRepository.findByUsrIdAndStatusIn(anyLong(), anyList())).thenReturn(contactEmails);

        user.setLastEventSequenceNumber(2L);
        BeforeUserDetailsChangedEvent userDetailsChangedEvent = new BeforeUserDetailsChangedEvent(this, user);

        List<DomainEventBase<?>> storedEvents = userDomainEventFactory.buildEvents(userDetailsChangedEvent);
        assertThat(storedEvents.size(), equalTo(1));
        DomainEventBase<?> event = storedEvents.get(0);

        return (UserDetailsChanged) event;
    }


    @Test
    public void shouldNotEmitEventInCaseAnythingOtherHappened() {
        //given
        final User user = new User.Builder().withId(1L).withEmailAddress(new EmailAddress.Builder().withEmail("<EMAIL>")).build();
        user.setLastEventSequenceNumber(2L);
        UserPreChangeEvent otherEvent = new UserPreChangeEvent(UserDomainEventFactoryTest.this, user) {
        };

        //when
        List<DomainEventBase<?>> storedEvents = userDomainEventFactory.buildEvents(otherEvent);

        //then
        assertThat(storedEvents.size(), equalTo(0));

    }

    private Instant now() {
        return clock.getDate().toInstant();
    }
}
