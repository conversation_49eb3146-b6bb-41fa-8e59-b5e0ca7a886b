package com.gumtree.context.bapi.domain.accountevents;

import com.google.common.collect.Lists;
import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.context.account.domain.event.AccountDetailsChanged;
import com.gumtree.context.account.domain.event.UserDetails;
import com.gumtree.context.bapi.domain.userevents.UserDomainEventFactory;
import com.gumtree.context.user.domain.event.AccountDetails;
import com.gumtree.context.user.domain.event.UserDetailsChanged;
import com.gumtree.context.user.domain.event.UserRegistered;
import com.gumtree.ddd.patterns.DomainEventBase;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.account.status.AccountStatus;
import com.gumtree.seller.domain.account.type.AccountType;
import com.gumtree.seller.domain.email.entity.ContactEmail;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.event.account.AccountPreChangeEvent;
import com.gumtree.seller.event.account.BeforeAccountDetailsChangedEvent;
import com.gumtree.seller.event.user.BeforeUserActivatedEvent;
import com.gumtree.seller.event.user.BeforeUserDeactivatedEvent;
import com.gumtree.seller.event.user.BeforeUserDetailsChangedEvent;
import com.gumtree.seller.event.user.UserPreChangeEvent;
import com.gumtree.seller.repository.email.ContactEmailRepository;
import org.junit.Test;

import java.io.IOException;
import java.time.Instant;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class AccountDomainEventFactoryTest {

    Clock clock = new StoppedClock();

    AccountDomainEventFactory accountDomainEventFactory = new AccountDomainEventFactory(clock);

    @Test
    public void shouldEmitAccountDetailsChangedEventWhenAccountActivated() throws IOException {
        //given
        Account account = new Account.Builder().withId(1L).withName("Test").withStatus(AccountStatus.ACTIVE).build();
        account.setLastEventSequenceNumber(2L);

        BeforeAccountDetailsChangedEvent accountActivatedEvent = new BeforeAccountDetailsChangedEvent(this, account);

        //when
        List<DomainEventBase<?>> storedEvents = accountDomainEventFactory.buildEvents(accountActivatedEvent);

        //then
        assertThat(storedEvents.size(), equalTo(1));
        DomainEventBase<?> event = storedEvents.get(0);

        assertThat(event.getAggregateId(), equalTo(account.getId()));
        assertThat(event.getSequenceNumber(), equalTo(3L));
        assertThat(event.getType(), equalTo("com.gumtree.context.account.domain.event.AccountDetailsChanged"));
        assertThat(event.getTimestamp(), equalTo(now()));

        AccountDetailsChanged eventPayload = (AccountDetailsChanged) event;
        assertThat(eventPayload.getAggregateId(), equalTo(account.getId()));
        assertThat(eventPayload.getStatus(), equalTo(AccountStatus.ACTIVE));
        assertThat(eventPayload.getTimestamp(), equalTo(now()));

    }

    @Test
    public void shouldEmitAccountDeactivationEventWhenAccountDeactivated() throws IOException {
        //given
        Account account = new Account.Builder().withId(1L).withName("Test").withStatus(AccountStatus.SUSPENDED).build();
        account.setLastEventSequenceNumber(2L);

        BeforeAccountDetailsChangedEvent accountDetailsChangedEvent = new BeforeAccountDetailsChangedEvent(this, account);

        //when
        List<DomainEventBase<?>> storedEvents = accountDomainEventFactory.buildEvents(accountDetailsChangedEvent);

        //then
        assertThat(storedEvents.size(), equalTo(1));
        DomainEventBase<?> event = storedEvents.get(0);

        assertThat(event.getAggregateId(), equalTo(account.getId()));
        assertThat(event.getSequenceNumber(), equalTo(3L));
        assertThat(event.getType(), equalTo("com.gumtree.context.account.domain.event.AccountDetailsChanged"));
        assertThat(event.getTimestamp(), equalTo(now()));

    }

    @Test
    public void shouldEmitUserDetailsChangedEventWhenAccountModified() throws IOException {
        User user = new User.Builder().withId(1L)
                .withFirstName("FirstName")
                .withLastName("LastName")
                .withEmailAddress(new EmailAddress.Builder().withEmail("<EMAIL>"))
                .build();

        Account account = new Account.Builder().withId(1L).withName("Test").withStatus(AccountStatus.ACTIVE).withUsers(Collections.singletonList(user)).build();

        account.setLastEventSequenceNumber(2L);
        user.addAccount(account);

        BeforeAccountDetailsChangedEvent accountDetailsChangedEvent = new BeforeAccountDetailsChangedEvent(this, account);

        List<DomainEventBase<?>> storedEvents = accountDomainEventFactory.buildEvents(accountDetailsChangedEvent);

        assertThat(storedEvents.size(), equalTo(1));
        DomainEventBase<?> event = storedEvents.get(0);

        AccountDetailsChanged eventPayload = (AccountDetailsChanged) event;

        assertThat(event.getAggregateId(), equalTo(user.getId()));
        assertThat(event.getSequenceNumber(), equalTo(3L));
        assertThat(event.getType(), equalTo("com.gumtree.context.account.domain.event.AccountDetailsChanged"));
        assertThat(event.getTimestamp(), equalTo(now()));

        assertThat(eventPayload.getAccountName(), equalTo("Test"));
        assertThat(eventPayload.getUserDetails().size(), equalTo(1));
        UserDetails userDetails = eventPayload.getUserDetails().get(0);
        assertThat(userDetails.getUserId(), equalTo(1L));
        assertThat(userDetails.getFirstName(), equalTo("FirstName"));
        assertThat(userDetails.getLastName(), equalTo("LastName"));
    }
//
//
    @Test
    public void shouldNotEmitEventInCaseAnythingOtherHappened() {
        //given
        Account account = new Account.Builder().withId(1L).withName("Test").withStatus(AccountStatus.ACTIVE).build();
        account.setLastEventSequenceNumber(2L);

        final User user = new User.Builder().withId(1L).withEmailAddress(new EmailAddress.Builder().withEmail("<EMAIL>")).build();
        user.setLastEventSequenceNumber(2L);
        AccountPreChangeEvent otherEvent = new AccountPreChangeEvent(AccountDomainEventFactoryTest.this, account) {
        };

        //when
        List<DomainEventBase<?>> storedEvents = accountDomainEventFactory.buildEvents(otherEvent);

        //then
        assertThat(storedEvents.size(), equalTo(0));

    }

    private Instant now() {
        return clock.getDate().toInstant();
    }
}