package com.gumtree.context.bapi.infrastructure;

import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class BapiAggregateTypeMapperTest {

    BapiAggregateTypeMapper mapper = new BapiAggregateTypeMapper();

    @Test
    public void shouldProperlyMapKnownTypeToId() {
        assertThat(mapper.resolveAggregateDbId(BapiAggregateType.ADVERT), equalTo(BapiAggregateType.ADVERT.getId()));
    }

    @Test
    public void shouldProperlyMapNameToKnownAggregateType() {
        assertThat(mapper.getTypeByName("ADVERT"), equalTo(BapiAggregateType.ADVERT));
    }

    @Test(expected = RuntimeException.class)
    public void shouldFailWhenMappingUnknownName() {
        mapper.getTypeByName("NOT-KNOWN");
    }

}