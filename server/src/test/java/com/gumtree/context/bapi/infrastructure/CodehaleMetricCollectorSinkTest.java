package com.gumtree.context.bapi.infrastructure;

import com.codahale.metrics.MetricRegistry;
import com.gumtree.ddd.patterns.DomainEventBase;
import org.junit.Test;

import java.time.Instant;
import java.util.Arrays;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class CodehaleMetricCollectorSinkTest {

    MetricRegistry metricRegistry = new MetricRegistry();

    CodehaleMetricCollectorSink sink = new CodehaleMetricCollectorSink(metricRegistry);

    static class TestEvent1 extends DomainEventBase<Long> {

        TestEvent1() {
        }

        public TestEvent1(Long id, Long version, Instant timestamp) {
            super(id, version, timestamp);
        }
    }

    static class TestEvent2 extends DomainEventBase<Long> {

        TestEvent2() {
        }

        public TestEvent2(Long id, Long version, Instant timestamp) {
            super(id, version, timestamp);
        }
    }

    @Test
    public void shouldProperlyStoreMetricsForEvents() {

        //when
        sink.consumeEvents(Arrays.asList(
                new TestEvent1(1L, 11L, Instant.now()),
                new TestEvent1(2L, 21L, Instant.now()),
                new TestEvent2(3L, 31L, Instant.now()))
        );

        //then
        assertThat(metricRegistry.getMeters().get(
                "event.com.gumtree.context.bapi.infrastructure.CodehaleMetricCollectorSinkTest$TestEvent1"
        ).getCount(), equalTo(2L));
        assertThat(metricRegistry.getMeters().get(
                "event.com.gumtree.context.bapi.infrastructure.CodehaleMetricCollectorSinkTest$TestEvent2"
        ).getCount(), equalTo(1L));

    }

}