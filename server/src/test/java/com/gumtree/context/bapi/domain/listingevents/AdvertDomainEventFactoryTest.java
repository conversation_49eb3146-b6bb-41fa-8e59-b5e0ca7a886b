package com.gumtree.context.bapi.domain.listingevents;

import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.context.listing.domain.event.AdvertBumpedUp;
import com.gumtree.context.listing.domain.event.AdvertExpired;
import com.gumtree.context.listing.domain.event.AdvertPosted;
import com.gumtree.context.listing.domain.event.AdvertPublished;
import com.gumtree.context.listing.domain.event.AdvertUnpublished;
import com.gumtree.context.listing.domain.event.FeaturedPromotionActivated;
import com.gumtree.context.listing.domain.event.FeaturedPromotionDeactivated;
import com.gumtree.context.listing.domain.event.SpotlightPromotionActivated;
import com.gumtree.context.listing.domain.event.UrgentPromotionActivated;
import com.gumtree.context.listing.domain.event.UrlPromotionActivated;
import com.gumtree.context.listing.domain.event.UrlPromotionDeactivated;
import com.gumtree.ddd.patterns.DomainEventBase;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.advert.entity.AdvertAttribute;
import com.gumtree.seller.domain.advert.entity.AdvertFeature;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.domain.attribute.entity.Attribute;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.location.entity.Postcode;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.event.advert.AdvertOldState;
import com.gumtree.seller.event.advert.BeforeAdvertExpiredEvent;
import com.gumtree.seller.event.advert.BeforeAdvertFeatureExpiredEvent;
import com.gumtree.seller.event.advert.BeforeAdvertFeaturedEvent;
import com.gumtree.seller.event.advert.BeforeAdvertPostedEvent;
import com.gumtree.seller.event.advert.BeforeAdvertPublishedEvent;
import com.gumtree.seller.event.advert.BeforeAdvertRemovedEvent;
import com.gumtree.seller.event.advert.BeforeAdvertRepostedEvent;
import com.gumtree.seller.repository.location.PostcodeLocator;
import com.gumtree.seller.service.advert.AdvertLocationResolver;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import static java.util.stream.Collectors.toList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AdvertDomainEventFactoryTest {

    private Clock clock = new StoppedClock();

    @Mock
    private AdvertLocationResolver advertLocationResolver;

    private AdvertDomainEventFactory factory;

    @Before
    public void setUp() {
        factory = new AdvertDomainEventFactory(clock);
    }

    @Test
    public void shouldEmitPostedEventWhenUserPostedFullAdvert() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.AWAITING_SCREENING);
        BeforeAdvertPostedEvent advertCreatedEvent = new BeforeAdvertPostedEvent(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(advertCreatedEvent);

        //then
        assertThat(events.size(), equalTo(1));

        AdvertPosted eventPayload = (AdvertPosted) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));

        assertThat(eventPayload.getTitle(), equalTo(advert.getTitle()));
        assertThat(eventPayload.getDescription(), equalTo(advert.getDescription()));
        assertThat(eventPayload.getAccountId(), equalTo(advert.getAccount().getId()));
        assertThat(eventPayload.getLocationId(), equalTo(advert.getSpecifiedLocation().getId()));
        assertThat(eventPayload.getCategoryId(), equalTo(advert.getCategory()));
        assertThat(eventPayload.getCentroid().getLatitude(), equalTo(advert.getLatitude()));
        assertThat(eventPayload.getCentroid().getLongitude(), equalTo(advert.getLongitude()));
        assertThat(eventPayload.getContactEmail(), equalTo(advert.getContactEmail()));
        assertThat(eventPayload.getContactName(), equalTo(advert.getContactName()));
        assertThat(eventPayload.getContactTelephone(), equalTo(advert.getContactTelephone()));
        assertThat(eventPayload.getContactUrl(), equalTo(advert.getContactUrl()));
        assertThat(eventPayload.getLocalArea(), equalTo(advert.getArea()));
        assertThat(eventPayload.getVisibleOnMap(), equalTo(advert.isVisibleOnMap()));
        assertThat(eventPayload.getYouTubeUrl(), equalTo(advert.getYoutubeLink()));
        assertThat(eventPayload.getIpAddress(), equalTo(advert.getIp()));
        assertThat(eventPayload.getRemoteHost(), equalTo(advert.getHostname()));
        assertThat(eventPayload.getCookie(), equalTo(advert.getCookie()));

        assertThat(eventPayload.getPostcode().getOutcode(), equalTo(advert.getOutcode()));
        assertThat(eventPayload.getPostcode().getIncode(), equalTo(advert.getIncode()));

        assertThat(eventPayload.getAttributes().size(), equalTo(2));
        assertThat(eventPayload.getAttributes().get("attribute_one"), equalTo("attribute_one_value"));
        assertThat(eventPayload.getAttributes().get("attribute_two"), equalTo("attribute_two_value"));

        assertThat(eventPayload.getPrimaryImageUrl(), equalTo(advert.getPrimaryImage().getUrl()));
        assertThat(eventPayload.getAdditionalImageUrls().size(), equalTo(2));
        assertThat(eventPayload.getAdditionalImageUrls(), contains("http://my.images.com/2", "http://my.images.com/3"));
    }


    @Test
    public void shouldEmitPostedEventWhenUserBumpedUpInactiveMinimalAdvert() throws IOException {

        //given
        Advert advert = createMinimalSampleAdvert(AdvertStatus.AWAITING_SCREENING);
        BeforeAdvertPostedEvent advertCreatedEvent = new BeforeAdvertPostedEvent(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(advertCreatedEvent);

        //then
        assertThat(events.size(), equalTo(1));

        AdvertPosted eventPayload = (AdvertPosted) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));

        assertThat(eventPayload.getTitle(), equalTo(advert.getTitle()));
        assertThat(eventPayload.getDescription(), equalTo(advert.getDescription()));
        assertThat(eventPayload.getAccountId(), equalTo(advert.getAccount().getId()));
        assertThat(eventPayload.getLocationId(), equalTo(advert.getSpecifiedLocation().getId()));
        assertThat(eventPayload.getCategoryId(), equalTo(advert.getCategory()));
        assertThat(eventPayload.getCentroid().getLatitude(), equalTo(advert.getLatitude()));
        assertThat(eventPayload.getCentroid().getLongitude(), equalTo(advert.getLongitude()));

        assertThat(eventPayload.getPostcode(), equalTo(null));

    }


    @Test
    public void shouldEmitPublishedEventWhenFullAdvertPassedScreening() throws IOException {

        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.LIVE);
        Instant publishedDate = Instant.now();
        advert.setPublishedDate(new DateTime(publishedDate.toEpochMilli()));

        BeforeAdvertPublishedEvent advertCreatedEvent = new BeforeAdvertPublishedEvent(buildOldState(AdvertStatus.AWAITING_SCREENING), advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(advertCreatedEvent);

        //then
        assertThat(events.size(), equalTo(1));

        AdvertPublished eventPayload = (AdvertPublished) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp(), equalTo(publishedDate));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));

        assertThat(eventPayload.getTitle(), equalTo(advert.getTitle()));
        assertThat(eventPayload.getDescription(), equalTo(advert.getDescription()));
        assertThat(eventPayload.getAccountId(), equalTo(advert.getAccount().getId()));
        assertThat(eventPayload.getLocationId(), equalTo(advert.getSpecifiedLocation().getId()));
        assertThat(eventPayload.getCategoryId(), equalTo(advert.getCategory()));
        assertThat(eventPayload.getCentroid().getLatitude(), equalTo(advert.getLatitude()));
        assertThat(eventPayload.getCentroid().getLongitude(), equalTo(advert.getLongitude()));
        assertThat(eventPayload.getContactEmail(), equalTo(advert.getContactEmail()));
        assertThat(eventPayload.getContactName(), equalTo(advert.getContactName()));
        assertThat(eventPayload.getContactTelephone(), equalTo(advert.getContactTelephone()));
        assertThat(eventPayload.getContactUrl(), equalTo(advert.getContactUrl()));
        assertThat(eventPayload.getLocalArea(), equalTo(advert.getArea()));
        assertThat(eventPayload.getVisibleOnMap(), equalTo(advert.isVisibleOnMap()));
        assertThat(eventPayload.getYouTubeUrl(), equalTo(advert.getYoutubeLink()));

        assertThat(eventPayload.getPostcode().getOutcode(), equalTo(advert.getOutcode()));
        assertThat(eventPayload.getPostcode().getIncode(), equalTo(advert.getIncode()));

        assertThat(eventPayload.getAttributes().size(), equalTo(2));
        assertThat(eventPayload.getAttributes().get("attribute_one"), equalTo("attribute_one_value"));
        assertThat(eventPayload.getAttributes().get("attribute_two"), equalTo("attribute_two_value"));

        assertThat(eventPayload.getPrimaryImageUrl(), equalTo(advert.getPrimaryImage().getUrl()));
        assertThat(eventPayload.getAdditionalImageUrls().size(), equalTo(2));
        assertThat(eventPayload.getAdditionalImageUrls(), contains("http://my.images.com/2", "http://my.images.com/3"));
    }

    @Test
    public void shouldEmitPublishedEventWhenMinimalAdvertPassedScreening() throws IOException {

        //given
        Advert advert = createMinimalSampleAdvert(AdvertStatus.LIVE);
        Instant publishedDate = Instant.now();
        advert.setPublishedDate(new DateTime(publishedDate.toEpochMilli()));

        BeforeAdvertPublishedEvent advertCreatedEvent = new BeforeAdvertPublishedEvent(buildOldState(AdvertStatus.AWAITING_SCREENING), advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(advertCreatedEvent);

        //then
        assertThat(events.size(), equalTo(1));

        AdvertPublished eventPayload = (AdvertPublished) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp(), equalTo(publishedDate));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));

        assertThat(eventPayload.getTitle(), equalTo(advert.getTitle()));
        assertThat(eventPayload.getDescription(), equalTo(advert.getDescription()));
        assertThat(eventPayload.getAccountId(), equalTo(advert.getAccount().getId()));
        assertThat(eventPayload.getLocationId(), equalTo(advert.getSpecifiedLocation().getId()));
        assertThat(eventPayload.getCategoryId(), equalTo(advert.getCategory()));
        assertThat(eventPayload.getCentroid().getLatitude(), equalTo(advert.getLatitude()));
        assertThat(eventPayload.getCentroid().getLongitude(), equalTo(advert.getLongitude()));

        assertThat(eventPayload.getPostcode(), equalTo(null));
    }

    @Test
    public void shouldEmitUnpublishedEventWhenAdvertExpired() throws IOException {

        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.EXPIRED);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertExpiredEvent(buildOldState(AdvertStatus.LIVE), advert)).
                stream().filter(e -> e.getType().equals(AdvertUnpublished.class.getName())).collect(toList());

        assertThat(events.size(), equalTo(1));

        AdvertUnpublished eventPayload = (AdvertUnpublished) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));
    }

    @Test
    public void shouldEmitUnpublishedEventWhenUserDeletedAdvert() throws IOException {

        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.DELETED_USER);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertRemovedEvent(buildOldState(AdvertStatus.LIVE), advert));

        //then

        assertThat(events.size(), equalTo(1));

        AdvertUnpublished eventPayload = (AdvertUnpublished) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));
    }

    @Test
    public void shouldEmitUnpublishedEventWhenCSDeletedAdvert() throws IOException {

        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.DELETED_CS);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertRemovedEvent(buildOldState(AdvertStatus.LIVE), advert));

        //then
        assertThat(events.size(), equalTo(1));

        AdvertUnpublished eventPayload = (AdvertUnpublished) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));
    }

    @Test
    public void shouldNotEmitUnpublishedEventWhenDeletedInactiveAdvert() {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.DELETED_USER);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertRemovedEvent(buildOldState(AdvertStatus.DRAFT), advert));

        //then
        assertThat(events.size(), equalTo(0));
    }

    @Test
    public void shouldEmitBumpedUpEventWhenBumpingUpActiveAdvert() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.LIVE);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertRepostedEvent(buildOldState(AdvertStatus.LIVE), advert));

        //then
        assertThat(events.size(), equalTo(1));

        AdvertBumpedUp eventPayload = (AdvertBumpedUp) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));
    }

    @Test
    public void shouldEmitFeaturedEventWhenNewFeaturePurchased() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.LIVE);
        advert.setWebsiteUrl("http://gumtree.com");
        AdvertFeature newFeature = new AdvertFeature();
        newFeature.setExpiryDate(clock.getDateTime().plusDays(1));
        newFeature.setProduct(new Product());
        newFeature.getProduct().setName(ProductName.WEBSITE_URL);
        newFeature.setAdvert(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertFeaturedEvent(advert, newFeature));

        //then
        assertThat(events.size(), equalTo(1));

        UrlPromotionActivated eventPayload = (UrlPromotionActivated) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));

        assertThat(eventPayload.getUrl(), equalTo(advert.getWebsiteUrl().getWebsiteUrl()));
    }

    @Test
    public void shouldEmitFeaturedEventWhenFeaturedFeaturePurchased2() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.LIVE);
        advert.setWebsiteUrl("http://gumtree.com");
        AdvertFeature newFeature = new AdvertFeature();
        newFeature.setExpiryDate(clock.getDateTime().plusDays(1));
        newFeature.setProduct(new Product());
        newFeature.getProduct().setName(ProductName.FEATURE_3_DAY);
        newFeature.setAdvert(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertFeaturedEvent(advert, newFeature));

        //then
        assertThat(events.size(), equalTo(1));

        FeaturedPromotionActivated eventPayload = (FeaturedPromotionActivated) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));
    }

    @Test
    public void shouldEmitFeaturedEventWhenSpotlightFeaturePurchased() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.LIVE);
        advert.setWebsiteUrl("http://gumtree.com");
        AdvertFeature newFeature = new AdvertFeature();
        newFeature.setExpiryDate(clock.getDateTime().plusDays(1));
        newFeature.setProduct(new Product());
        newFeature.getProduct().setName(ProductName.HOMEPAGE_SPOTLIGHT);
        newFeature.setAdvert(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertFeaturedEvent(advert, newFeature));

        //then
        assertThat(events.size(), equalTo(1));

        SpotlightPromotionActivated eventPayload = (SpotlightPromotionActivated) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));
    }

    @Test
    public void shouldEmitFeaturedEventWhenUrgentFeaturePurchased() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.LIVE);
        advert.setWebsiteUrl("http://gumtree.com");
        AdvertFeature newFeature = new AdvertFeature();
        newFeature.setExpiryDate(clock.getDateTime().plusDays(1));
        newFeature.setProduct(new Product());
        newFeature.getProduct().setName(ProductName.URGENT);
        newFeature.setAdvert(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertFeaturedEvent(advert, newFeature));

        //then
        assertThat(events.size(), equalTo(1));

        UrgentPromotionActivated eventPayload = (UrgentPromotionActivated) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));
    }


    @Test
    public void shouldNotEmitFeaturedEventWhenNewFeaturePurchasedForNonLiveAd() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.EXPIRED);
        advert.setWebsiteUrl("http://gumtree.com");
        AdvertFeature newFeature = new AdvertFeature();
        newFeature.setExpiryDate(clock.getDateTime().plusDays(1));
        newFeature.setProduct(new Product());
        newFeature.getProduct().setName(ProductName.WEBSITE_URL);
        newFeature.setAdvert(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertFeaturedEvent(advert, newFeature));

        //then
        assertThat(events.size(), equalTo(0));
    }



    @Test
    public void shouldEmitFeaturedEventsWhenPublishingAdWithFeatures() throws IOException {
        //given
        Advert advert = createMinimalSampleAdvert(AdvertStatus.LIVE);

        AdvertFeature feature1 = new AdvertFeature();
        feature1.setExpiryDate(clock.getDateTime().plusDays(1));
        feature1.setProduct(new Product());
        feature1.getProduct().setName(ProductName.WEBSITE_URL);
        advert.setWebsiteUrl("http://www.gumtree.com");
        feature1.setAdvert(advert);

        advert.getFeatures().add(feature1);

        AdvertFeature feature2 = new AdvertFeature();
        feature2.setExpiryDate(clock.getDateTime().plusDays(1));
        feature2.setProduct(new Product());
        feature2.getProduct().setName(ProductName.FEATURE_14_DAY);
        feature2.setAdvert(advert);

        advert.getFeatures().add(feature2);

        Instant publishedDate = Instant.now();
        advert.setPublishedDate(new DateTime(publishedDate.toEpochMilli()));

        BeforeAdvertPublishedEvent advertCreatedEvent = new BeforeAdvertPublishedEvent(buildOldState(AdvertStatus.AWAITING_SCREENING), advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(advertCreatedEvent);

        //then
        assertThat(events.size(), equalTo(3));

        assertThat(events.stream().filter(e -> e.getType().equals(FeaturedPromotionActivated.class.getName())).findFirst().isPresent(), equalTo(true));
        assertThat(events.stream().filter(e -> e.getType().equals(UrlPromotionActivated.class.getName())).findFirst().isPresent(), equalTo(true));

        UrlPromotionActivated urlPromotionActivated = (UrlPromotionActivated)events.get(1);
        assertThat(urlPromotionActivated.getAggregateId(), equalTo(advert.getId()));
        assertThat(urlPromotionActivated.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(urlPromotionActivated.getSequenceNumber(), equalTo(2L));
        assertThat(urlPromotionActivated.getUrl(), equalTo(advert.getWebsiteUrl().getWebsiteUrl()));

        FeaturedPromotionActivated featuredPromotionActivated = (FeaturedPromotionActivated)events.get(2);
        assertThat(featuredPromotionActivated.getAggregateId(), equalTo(advert.getId()));
        assertThat(featuredPromotionActivated.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(featuredPromotionActivated.getSequenceNumber(), equalTo(3L));

    }

    @Test
    public void shouldEmitAdvertFeatureExpiredWhenCausingAdvertToUnpublish() throws IOException {

        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.DELETED_CS);

        AdvertFeature feature1 = new AdvertFeature();
        feature1.setExpiryDate(clock.getDateTime().plusDays(1));
        feature1.setProduct(new Product());
        feature1.getProduct().setName(ProductName.WEBSITE_URL);
        advert.setWebsiteUrl("http://www.gumtree.com");
        feature1.setAdvert(advert);

        advert.getFeatures().add(feature1);

        AdvertFeature feature2 = new AdvertFeature();
        feature2.setExpiryDate(clock.getDateTime().plusDays(1));
        feature2.setProduct(new Product());
        feature2.getProduct().setName(ProductName.FEATURE_14_DAY);
        feature2.setAdvert(advert);

        advert.getFeatures().add(feature2);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertRemovedEvent(buildOldState(AdvertStatus.LIVE), advert));

        //then
        assertThat(events.size(), equalTo(3));

        assertThat(events.stream().filter(e -> e.getType().equals(FeaturedPromotionDeactivated.class.getName())).findFirst().isPresent(), equalTo(true));
        assertThat(events.stream().filter(e -> e.getType().equals(UrlPromotionDeactivated.class.getName())).findFirst().isPresent(), equalTo(true));

        UrlPromotionDeactivated urlPromotionDeactivated = (UrlPromotionDeactivated)events.get(0);
        assertThat(urlPromotionDeactivated.getAggregateId(), equalTo(advert.getId()));
        assertThat(urlPromotionDeactivated.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(urlPromotionDeactivated.getSequenceNumber(), equalTo(1L));

        FeaturedPromotionDeactivated featuredPromotionDeactivated = (FeaturedPromotionDeactivated)events.get(1);
        assertThat(featuredPromotionDeactivated.getAggregateId(), equalTo(advert.getId()));
        assertThat(featuredPromotionDeactivated.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(featuredPromotionDeactivated.getSequenceNumber(), equalTo(2L));

        AdvertUnpublished eventPayload = (AdvertUnpublished) events.get(2);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(3L));
    }


    @Test
    public void shouldEmitFeatureExpiredEventWhenFeatureExpired() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.LIVE);
        advert.setWebsiteUrl("http://gumtree.com");

        AdvertFeature feature = new AdvertFeature();
        feature.setExpiryDate(clock.getDateTime().plusDays(1));
        feature.setProduct(new Product());
        feature.getProduct().setName(ProductName.WEBSITE_URL);
        feature.setAdvert(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertFeatureExpiredEvent(advert, feature));

        //then

        assertThat(events.size(), equalTo(1));

        UrlPromotionDeactivated eventPayload = (UrlPromotionDeactivated) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(1L));

        assertThat(eventPayload.getUrl(), equalTo(advert.getWebsiteUrl().getWebsiteUrl()));
    }

    @Test
    public void shouldEmitFeatureExpiredEventWhenFeatureExpiredForNonLiveAd() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.EXPIRED);
        advert.setWebsiteUrl("http://gumtree.com");

        AdvertFeature feature = new AdvertFeature();
        feature.setExpiryDate(clock.getDateTime().plusDays(1));
        feature.setProduct(new Product());
        feature.getProduct().setName(ProductName.WEBSITE_URL);
        feature.setAdvert(advert);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertFeatureExpiredEvent(advert, feature));

        //then

        assertThat(events.size(), equalTo(0));
    }

    @Test
    public void shouldEmitExpiredEventWhenAdvertExpiredWithExpiryDate() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.EXPIRED);
        DateTime expiryDate = DateTime.now();
        advert.setExpiryDate(expiryDate);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertExpiredEvent(buildOldState(AdvertStatus.LIVE), advert)).
                stream().
                filter(e -> e.getType().equals(AdvertExpired.class.getName())).collect(toList());

        //then
        assertThat(events.size(), equalTo(1));

        AdvertExpired eventPayload = (AdvertExpired) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(expiryDate.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(2L));
    }

    @Test
    public void shouldEmitExpiredEventWhenAdvertExpiredWithoutExpiryDate() throws IOException {
        //given
        Advert advert = createFullSampleAdvert(AdvertStatus.EXPIRED);

        //when
        List<DomainEventBase<?>> events = factory.buildEvents(new BeforeAdvertExpiredEvent(buildOldState(AdvertStatus.LIVE), advert)).
                stream().
                filter(e -> e.getType().equals(AdvertExpired.class.getName())).collect(toList());

        //then
        assertThat(events.size(), equalTo(1));

        AdvertExpired eventPayload = (AdvertExpired) events.get(0);

        assertThat(eventPayload.getAggregateId(), equalTo(advert.getId()));
        assertThat(eventPayload.getTimestamp().toEpochMilli(), equalTo(clock.getMillis()));
        assertThat(eventPayload.getSequenceNumber(), equalTo(2L));
    }

    private Advert createMinimalSampleAdvert(AdvertStatus status) {
        Advert advert = new Advert();
        advert.setId(1L);
        advert.setVersion(1L);
        advert.setTitle("My advert");
        advert.setDescription("My advert description");
        advert.setStatus(status);
        advert.setAccount(new Account());
        advert.getAccount().setId(12L);
        advert.setSpecifiedLocation(new Location());
        advert.getSpecifiedLocation().setId(13L);
        advert.getSpecifiedLocation().setLatitude(new BigDecimal("54.0134"));
        advert.getSpecifiedLocation().setLongitude(new BigDecimal("-1.0134"));




        advert.setCategory(50L);
        when(advertLocationResolver.getHierarchicalLocationsFromLeaf(advert.getSpecifiedLocation())).thenReturn(Arrays.asList(advert.getSpecifiedLocation()));
        advert.initialiseHierarchicalLocations(advertLocationResolver);

        return advert;
    }

    private Advert createFullSampleAdvert(AdvertStatus status) {
        Advert advert = createMinimalSampleAdvert(status);

        advert.setContactEmail("<EMAIL>");
        advert.setContactName("Joe Example");
        advert.setContactTelephone("0800 50 50 50");
        advert.setContactUrl("http://my.website.com");
        advert.setArea("My local area");
        advert.setVisibleOnMap(true);
        advert.setYoutubeLink("http://my.youtube.link");
        advert.setIp("*********");
        advert.setCookie("my_cookie_value");
        advert.setHostname("myhost.com");

        PostcodeLocator postcodeLocator = Mockito.mock(PostcodeLocator.class);
        Postcode pc = Mockito.mock(Postcode.class);
        Mockito.when(pc.getPostcode()).thenReturn("TW9 1EJ");
        Mockito.when(pc.getLongitude()).thenReturn(advert.getSpecifiedLocation().getLongitude());
        Mockito.when(pc.getLatitude()).thenReturn(advert.getSpecifiedLocation().getLatitude());
        Mockito.when(postcodeLocator.findPostcode("TW9 1EJ")).thenReturn(pc);

        advert.setPostcode("TW9 1EJ", postcodeLocator);

        Image image = new Image();
        image.setId(1L);
        image.setUrl("http://my.images.com/1");
        Image image2 = new Image();
        image2.setId(2L);
        image2.setUrl("http://my.images.com/2");
        Image image3 = new Image();
        image3.setId(3L);
        image3.setUrl("http://my.images.com/3");
        advert.setImages(Arrays.asList(image, image2, image3), image);

        Attribute attribute = new Attribute();
        attribute.setId(1L);
        attribute.setName("attribute_one");
        AdvertAttribute advertAttribute = new AdvertAttribute();
        advertAttribute.setAttribute(attribute);
        advertAttribute.setValue("attribute_one_value");

        Attribute attribute2 = new Attribute();
        attribute2.setId(2L);
        attribute2.setName("attribute_two");
        AdvertAttribute advertAttribute2 = new AdvertAttribute();
        advertAttribute2.setAttribute(attribute2);
        advertAttribute2.setValue("attribute_two_value");

        advert.setAttributes(Arrays.asList(advertAttribute, advertAttribute2));

        return advert;
    }

    private AdvertOldState buildOldState(AdvertStatus previousStatus) {
        Advert oldAdvert = new Advert();
        oldAdvert.setStatus(previousStatus);
        return AdvertOldState.build(oldAdvert);
    }

    //TODO Test for feature events emited when publishing / unpublishing ads

}
