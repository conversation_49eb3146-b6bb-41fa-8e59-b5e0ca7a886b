package com.gumtree.context.bapi.infrastructure;

import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.context.eventlog.domain.AggregateType;
import com.gumtree.context.eventlog.domain.DomainEventRepository;
import com.gumtree.context.eventlog.domain.EventLogEntry;
import com.gumtree.context.eventlog.infrastructure.driven.eventlog.DomainEventSerializer;
import com.gumtree.ddd.patterns.DomainEventBase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EventLogSinkTest {

    static class TestEvent extends DomainEventBase<Long> {

        TestEvent() {
        }

        public TestEvent(Long id, Long version, Instant timestamp) {
            super(id, version, timestamp);
        }
    }

    public enum TestAggregateType implements AggregateType {
        TEST {
            @Override
            public String getName() {
                return "TEST";
            }
        }
    }

    @Mock
    DomainEventSerializer<TestEvent> serializer;

    @Mock
    DomainEventRepository<TestAggregateType> repository;

    @Captor
    ArgumentCaptor<List<EventLogEntry>> storedEventLogEntriesCaptor;

    Clock clock = new StoppedClock();

    EventLogSink<TestAggregateType, TestEvent> eventLogSink;

    @Before
    public void setUp() {
        eventLogSink = new EventLogSink<>(TestAggregateType.TEST, serializer, repository, clock);

    }

    @Test
    public void shouldProperlyProcessValidMessage() {
        //given
        List<TestEvent> testEvent = Arrays.asList(
                new TestEvent(1L, 11L, clock.getDate().toInstant().plusSeconds(10)),
                new TestEvent(2L, 22L, clock.getDate().toInstant().plusSeconds(20))
        );

        when(serializer.serialize(Mockito.any())).thenAnswer(e -> {
            TestEvent event = (TestEvent)e.getArguments()[0];
            return event.getAggregateId().toString() + "/" + event.getSequenceNumber();
        });

        //when
        eventLogSink.consumeEvents(testEvent);

        //then
        verify(repository).storeDomainEvents(Mockito.eq(TestAggregateType.TEST), storedEventLogEntriesCaptor.capture());
        List<EventLogEntry> entries = storedEventLogEntriesCaptor.getValue();
        assertThat(entries.size(), equalTo(2));

        assertThat(entries.get(0).getAggregateId(), equalTo(1L));
        assertThat(entries.get(0).getAggregateVersion(), equalTo(11L));
        assertThat(entries.get(0).getPayload(), equalTo("1/11"));
        assertThat(entries.get(0).getTimestamp(), equalTo(clock.getDate().toInstant()));

        assertThat(entries.get(1).getAggregateId(), equalTo(2L));
        assertThat(entries.get(1).getAggregateVersion(), equalTo(22L));
        assertThat(entries.get(1).getPayload(), equalTo("2/22"));
        assertThat(entries.get(1).getTimestamp(), equalTo(clock.getDate().toInstant()));

    }

    @Test
    public void shouldNotWriteToSinksInCaseOfError() {
        //given
        List<TestEvent> testEvent = Arrays.asList(
                new TestEvent(1L, 11L, clock.getDate().toInstant().plusSeconds(10)),
                new TestEvent(2L, 22L, clock.getDate().toInstant().plusSeconds(20))
        );

        when(serializer.serialize(Mockito.any())).thenThrow(new RuntimeException());

        //when
        try {
            eventLogSink.consumeEvents(testEvent);
        } catch (Exception e) {
            //do nothing
        }

        //then
        verifyZeroInteractions(repository);
    }

}