package com.gumtree.wallet.packages.service.impl;

import com.google.common.collect.Lists;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.account.exception.AccountNotFoundException;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.wallet.packages.domain.PackageRequest;
import com.gumtree.wallet.packages.domain.entity.CreditPackage;
import com.gumtree.wallet.packages.domain.entity.PackageType;
import com.gumtree.wallet.packages.domain.exception.PackageEndDateBeforeStartDateException;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.repository.account.AccountRepository;
import com.gumtree.wallet.packages.repository.CreditPackageRepository;
import com.gumtree.wallet.packages.repository.PackageTypeRepository;
import com.gumtree.seller.service.security.SecurityService;
import org.apache.shiro.authz.UnauthorizedException;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThat;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 */
public class CreditPackageServiceImplCreatePackagesTest {

    private CreditPackageServiceImpl service;

    @Mock
    private CreditPackageRepository creditPackageRepository;
    @Mock
    private PackageTypeRepository packageTypeRepository;
    @Mock
    private AccountRepository accountRepository;
    @Mock
    private SecurityService securityService;
    @Mock
    private JdbcOperations jdbcOperations;
    @Mock
    private Clock clock;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        service = new CreditPackageServiceImpl(creditPackageRepository, packageTypeRepository, accountRepository,
                securityService, jdbcOperations, clock);
    }

    @Test(expected = UnauthorizedException.class)
    public void shouldThrowUnauthorizedExceptionIfUserDoesNotHaveRequiredPermissions() {
        //given
        List<PackageRequest> createPackagesRequest = getPackageRequests(2, 1L);
        when(securityService.currentUserHasPermission(anyString())).thenReturn(false);
        //when
        service.createPackages(createPackagesRequest);
        //then throw exception
    }

    @Test(expected = AccountNotFoundException.class)
    public void shouldThrowAccountNotFoundExceptionIfAccountDoesNotExist() {
        //given
        List<PackageRequest> createPackagesRequest = getPackageRequests(2, 1L);
        when(securityService.currentUserHasPermission(anyString())).thenReturn(true);
        PackageType packageType = new PackageType();
        when(packageTypeRepository.findBySalesforceProductId(anyString())).thenReturn(packageType);
        CreditPackage creditPackage = new CreditPackage();
        when(creditPackageRepository.findBySalesforceId(anyString())).thenReturn(creditPackage);
        //when
        service.createPackages(createPackagesRequest);
        //then throw exception
    }

    @Test(expected = PackageEndDateBeforeStartDateException.class)
    public void shouldThrowPackageEndDateBeforeStartDateExceptionIfEndDateBeforeStartDate() {
        //given
        Account account = new Account();
        account.setId(1L);
        List<PackageRequest> createPackagesRequest = getPackageRequests(2, account.getId());
        when(securityService.currentUserHasPermission(anyString())).thenReturn(true);
        PackageType packageType = new PackageType();
        when(packageTypeRepository.findBySalesforceProductId(anyString())).thenReturn(packageType);
        CreditPackage creditPackage = new CreditPackage();
        when(creditPackageRepository.findBySalesforceId(anyString())).thenReturn(creditPackage);
        when(accountRepository.findOne(anyLong())).thenReturn(account);
        PackageRequest packageRequest = createPackagesRequest.get(0);
        packageRequest.setStartDate(now().plusDays(1));
        //when
        service.createPackages(createPackagesRequest);
        //then throw exception
    }

    @Test
    public void shouldReturnListOfInitialisedCreditPackages() {
        //given
        Account account = new Account();
        account.setId(1L);
        List<PackageRequest> createPackagesRequest = getPackageRequests(2, account.getId());
        when(securityService.currentUserHasPermission(anyString())).thenReturn(true);
        PackageType packageTypeA = createPackageType();
        PackageType packageTypeB = createPackageType();
        when(packageTypeRepository.findBySalesforceProductId("sfpid0")).thenReturn(packageTypeA);
        when(packageTypeRepository.findBySalesforceProductId("sfpid1")).thenReturn(packageTypeB);
        CreditPackage creditPackageA = new CreditPackage();
        CreditPackage creditPackageB = new CreditPackage();
        when(creditPackageRepository.findBySalesforceId("sf0")).thenReturn(creditPackageA);
        when(creditPackageRepository.findBySalesforceId("sf1")).thenReturn(creditPackageB);
        when(accountRepository.findOne(account.getId())).thenReturn(account);
        //when
        List<CreditPackage> creditPackages = service.createPackages(createPackagesRequest);
        //then
        assertNotNull(creditPackages);
        assertThat(creditPackages.size(), equalTo(2));
        assertCreditPackagesInitialised(creditPackages);
    }

    private PackageType createPackageType() {
        PackageType packageType = new PackageType();
        List<Long> categories = new ArrayList<Long>();
        categories.add(1L);
        ReflectionTestUtils.setField(packageType, "categories", categories);
        List<Location> locations;
        locations = new ArrayList<>();
        locations.add(new Location());
        ReflectionTestUtils.setField(packageType, "locations", locations);
        List<Product> products = new ArrayList<>();
        products.add(new Product());
        ReflectionTestUtils.setField(packageType, "products", products);
        return packageType;
    }

    private void assertCreditPackagesInitialised(List<CreditPackage> creditPackages) {
        for (CreditPackage creditPackage : creditPackages) {
            assertThat((ArrayList<Long>)creditPackage.getPackageType().getCategories(), equalTo(Lists.newArrayList(1L)));
            assertThat(creditPackage.getPackageType().getLocations().size(), equalTo(1));
            assertThat(creditPackage.getPackageType().getProducts().size(), equalTo(1));
        }
    }

    private List<PackageRequest> getPackageRequests(int numberRequired, Long accountId) {
        List<PackageRequest> packageRequests = new ArrayList<>();
        for (int i = 0; i < numberRequired; i++) {
            PackageRequest request = new PackageRequest();
            request.setSalesForceId("sf" + i);
            request.setSalesforceProductId("sfpid" + i);
            request.setStartDate(now());
            request.setEndDate(now());
            request.setInitialCredits(2L);
            request.setAccountId(accountId);
            packageRequests.add(request);
        }
        return packageRequests;
    }

    private DateTime now() {
        return new DateTime();
    }
}