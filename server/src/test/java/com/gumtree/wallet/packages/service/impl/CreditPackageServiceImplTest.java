package com.gumtree.wallet.packages.service.impl;

import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.account.exception.AccountNotFoundException;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.repository.account.AccountRepository;
import com.gumtree.wallet.packages.domain.PackageSearchFilter;
import com.gumtree.seller.service.security.SecurityService;
import com.gumtree.wallet.packages.domain.PackageRequest;
import com.gumtree.wallet.packages.domain.entity.CreditPackage;
import com.gumtree.wallet.packages.domain.entity.PackageType;
import com.gumtree.wallet.packages.domain.exception.InvalidUsedCreditsException;
import com.gumtree.wallet.packages.domain.exception.PackageEndDateBeforeStartDateException;
import com.gumtree.wallet.packages.domain.exception.PackageNotFoundException;
import com.gumtree.wallet.packages.repository.CreditPackageRepository;
import com.gumtree.wallet.packages.repository.PackageTypeRepository;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.UnauthorizedException;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class CreditPackageServiceImplTest {

    private CreditPackageServiceImpl creditPackageService;

    @Autowired
    private CreditPackageRepository creditPackageRepository;

    private AccountRepository accountRepository;
    private PackageRequest packageRequest;
    private Account account;
    private User user;
    private SecurityService securityService;

    @Before
    public void setup() {
        creditPackageService = new CreditPackageServiceImpl(creditPackageRepository, null,accountRepository,
                securityService, null, null);
        creditPackageRepository = mock(CreditPackageRepository.class);
        accountRepository = mock(AccountRepository.class);
        securityService = mock(SecurityService.class);
        packageRequest = buildValidPackageRequest();
        PackageTypeRepository packageTypeRepository = mock(PackageTypeRepository.class);
        PackageType packageType = buildPackageType();
        account = createValidAccount();

        when(packageTypeRepository.findBySalesforceProductId("ASALESFORCEID")).thenReturn(packageType);
        when(packageTypeRepository.findBySalesforceProductId("NOTASALESFORCEID")).thenReturn(null);
        when(accountRepository.findOne(anyLong())).thenReturn(account);

        user = new User();
        ReflectionTestUtils.setField(user, "id", 1L);
        when(securityService.getCurrentUser()).thenReturn(user);
        when(securityService.currentUserHasPermission("account:managepackages:" + 1L)).thenReturn(true);

        ReflectionTestUtils.setField(creditPackageService, "creditPackageRepository", creditPackageRepository);
        ReflectionTestUtils.setField(creditPackageService, "packageTypeRepository", packageTypeRepository);
        ReflectionTestUtils.setField(creditPackageService, "accountRepository", accountRepository);
        ReflectionTestUtils.setField(creditPackageService, "securityService", securityService);
    }

    @Test(expected = AuthorizationException.class)
    public void userWithNoPermissionCantCreatePackage() {
        when(securityService.currentUserHasPermission("account:managepackages:" + 1L)).thenReturn(false);
        creditPackageService.createPackage(packageRequest);
    }

    @Test
    public void testCreatePackageReturnsValidPackageWithAValidRequestObject() {
        CreditPackage creditPackage = creditPackageService.createPackage(packageRequest);
        assertThat(creditPackage != null, equalTo(true));
    }

    @Test
    public void testCreatePackageReturnsWithDefaultCreditValues() {
        CreditPackage creditPackage = creditPackageService.createPackage(packageRequest);
        assertThat(creditPackage != null, equalTo(true));
        assertThat(creditPackage.getAdjustedCredits(), equalTo(0l));
        assertThat(creditPackage.getInitialCredits(), equalTo(100l));
        assertThat(creditPackage.getUsedCredits(), equalTo(0l));
    }

    @Test
    public void testCreatePackageReturnsUsedCreditsZeroIfSetToZeroInPackageRequest() {
        PackageRequest packageRequest = buildValidPackageRequest();
        packageRequest.setUsedCredits(0L);

        CreditPackage creditPackage = creditPackageService.createPackage(packageRequest);
        assertThat(creditPackage != null, equalTo(true));
        assertThat(creditPackage.getUsedCredits(), equalTo(0l));
    }

    @Test(expected = InvalidUsedCreditsException.class)
    public void testCreatePackageReturnsErrorIfUsedCreditsAreSetToNonZero() {
        PackageRequest packageRequest = buildValidPackageRequest();
        packageRequest.setUsedCredits(100L);

        creditPackageService.createPackage(packageRequest);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCreatePackageReturnsPositiveInitialCredits() {
        PackageRequest negativeInitialCredits = buildValidPackageRequest();
        negativeInitialCredits.setInitialCredits(-10l);
        CreditPackage creditPackage = creditPackageService.createPackage(negativeInitialCredits);
    }

    @Test
    public void testCreatePackageWithInValidSalesForceIdDoesntCreate() {
        PackageRequest invalidRequest = buildValidPackageRequest();
        invalidRequest.setSalesforceProductId("NOTASALESFORCEID");
        CreditPackage creditPackage = creditPackageService.createPackage(invalidRequest);
        assertThat(creditPackage, is(nullValue()));
    }

    @Test
    public void testCreatePackagesWithInvalidSalesforceIdIgnoresInvalid() {
        PackageRequest request1 = buildValidPackageRequest();
        PackageRequest request2 = buildValidPackageRequest();
        request2.setSalesForceId("somethingdifferent");
        request2.setSalesforceProductId("NOTASALESFORCEID");

        List<CreditPackage> packages = creditPackageService.createPackages(Arrays.asList(request1, request2));
        assertThat(packages.size(), equalTo(1));
    }

    @Test
    public void testCreatePackagesCreatesMultiplePackages() {
        PackageRequest request1 = buildValidPackageRequest();
        PackageRequest request2 = buildValidPackageRequest();
        request2.setSalesForceId("somethingdifferent");

        List<CreditPackage> packages = creditPackageService.createPackages(Arrays.asList(request1, request2));
        assertThat(packages.size(), equalTo(2));
    }

    @Test(expected = PackageEndDateBeforeStartDateException.class)
    public void endDateBeforeStartDateThrowsException() {
        PackageRequest request = buildValidPackageRequest();
        DateTime endDate = new DateTime();
        request.setEndDate(endDate);
        DateTime startDate = new DateTime();
        startDate = endDate.plus(10000);
        request.setStartDate(startDate);
        creditPackageService.createPackage(request);
    }

    @Test
    public void testUpdateWithSalesforceId() {
        PackageRequest request = buildValidPackageRequest();
        CreditPackage creditPackage = creditPackageService.createPackage(request);
        assertThat(creditPackage.getCreationDate() != null, equalTo(true));

        when(creditPackageRepository.findBySalesforceId("********")).thenReturn(creditPackage);
        PackageRequest uRequest = buildValidPackageRequest();
        uRequest.setAdjustedCredits(50L);
        CreditPackage uCreditPackage = creditPackageService.createPackage(uRequest);

        assertThat(uCreditPackage.getId(), equalTo(creditPackage.getId()));
        assertThat(uCreditPackage.getCreationDate().toString(), equalTo(creditPackage.getCreationDate().toString()));
        assertThat(uCreditPackage.getAdjustedCredits(), equalTo(50L));
    }

    @Test
    public void testGetPackageBySalesforceId() {
        CreditPackage creditPackage = new CreditPackage();
        ReflectionTestUtils.setField(creditPackage, "id", 1L);
        ReflectionTestUtils.setField(creditPackage, "salesforceId", "SF-1234");
        ReflectionTestUtils.setField(creditPackage, "account", account);
        PackageType packageType = new PackageType();
        ReflectionTestUtils.setField(packageType, "products", new ArrayList());
        ReflectionTestUtils.setField(packageType, "categories", new ArrayList());
        ReflectionTestUtils.setField(packageType, "locations", new ArrayList());
        ReflectionTestUtils.setField(creditPackage, "packageType", packageType);
        when(creditPackageRepository.findBySalesforceId("SF-1234")).thenReturn(creditPackage);

        assertThat(creditPackageService.getPackageBySalesforceId("SF-1234"), equalTo(creditPackage));
    }

    @Test(expected = PackageNotFoundException.class)
    public void testGetPackageBySalesforceIdThrowsExceptionWhenPackageNotFound() {
        when(creditPackageRepository.findBySalesforceId("SF-4567")).thenReturn(null);
        creditPackageService.getPackageBySalesforceId("SF-4567");
    }

    @Test(expected = UnauthorizedException.class)
    public void testGetPackageBySalesforceIdThrowsExceptionWhenUserHasNoPermissionForAccount() {
        when(securityService.currentUserHasPermission("account:managepackages:" + 1L)).thenReturn(false);
        CreditPackage creditPackage = new CreditPackage();
        ReflectionTestUtils.setField(creditPackage, "id", 1L);
        ReflectionTestUtils.setField(creditPackage, "salesforceId", "SF-1234");
        ReflectionTestUtils.setField(creditPackage, "account", account);
        when(creditPackageRepository.findBySalesforceId("SF-1234")).thenReturn(creditPackage);

        creditPackageService.getPackageBySalesforceId("SF-1234");
    }

    @Test(expected = UnauthorizedException.class)
    public void testGetPackageByAccountThrowsExceptionWhenUserHasNoPermissoiNForAccount() {
        when(securityService.currentUserHasPermission("account:managepackages:" + 1L)).thenReturn(false);
        creditPackageService.getPackagesForAccount(account.getId(), Boolean.FALSE);
    }

    @Test
    public void testGetPackageByAccountReturnsListOfPackages() {
        CreditPackage c1 = new CreditPackage();
        CreditPackage c2 = new CreditPackage();
        when(creditPackageRepository.findByAccountId(account.getId())).thenReturn(Arrays.asList(c1, c2));

        assertThat(creditPackageService.getPackagesForAccount(account.getId(), Boolean.FALSE).size(), equalTo(2));
        verify(creditPackageRepository).findByAccountId(anyLong());
    }

    @Test(expected = AccountNotFoundException.class)
    public void testGetPackageByAccountThrowsExceptionWhenAccountDoesntExist() {
        when(accountRepository.findOne(5L)).thenReturn(null);
        creditPackageService.getPackagesForAccount(5L, Boolean.FALSE);
    }

    @Test
    public void testGetPackageByAccountIncludeCapabilities() {
        creditPackageService.getPackagesForAccount(5L, Boolean.TRUE);
        verify(creditPackageRepository).findByAccountIdWithCapabilities(anyLong());
    }

    @Test(expected = PackageNotFoundException.class)
    public void getPackageUsagesThrowsPackageNotFoundExceptionWhenPackageIdDoesNotExist() {
        when(creditPackageRepository.findOne(anyLong())).thenReturn(null);
        creditPackageService.getPackageUsages(10L, 5L, 1, 1, false);
    }

    @Test(expected = UnauthorizedException.class)
    public void getPackageUsagesThrowsUnauthorizedExceptionWhenPackageDoesNotBelongToAccount() {
        CreditPackage creditPackage = new CreditPackage();
        creditPackage.setAccount(createValidAccount());
        when(creditPackageRepository.findOne(5L)).thenReturn(creditPackage);
        when(securityService.currentUserHasPermission(anyString())).thenReturn(true);
        creditPackageService.getPackageUsages(10L, 5L, 1, 1, false);
    }

    @Test(expected = UnauthorizedException.class)
    public void getPackageUsagesThrowsUnauthorizedExceptionWhenCurrentUserDoesNotHavePermissionToManagePackagesForAccount() {
        CreditPackage creditPackage = new CreditPackage();
        creditPackage.setAccount(createValidAccount());
        when(creditPackageRepository.findOne(5L)).thenReturn(creditPackage);
        when(securityService.currentUserHasPermission(anyString())).thenReturn(false);
        creditPackageService.getPackageUsages(1L, 5L, 1, 1, false);
    }

    @Test(expected = UnauthorizedException.class)
    public void getAllPackageUsagesThrowsUnauthorizedExceptionWhenCurrentUserDoesNotHavePermissionToManagePackagesForAccount() {
        when(securityService.currentUserHasPermission(anyString())).thenReturn(false);
        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(1L, 5L, null, null, null, false);
        creditPackageService.getPackageUsages(packageSearchFilter, 1, 1);
    }

    private PackageType buildPackageType() {
        PackageType packageType = new PackageType();
        ReflectionTestUtils.setField(packageType, "categories", new ArrayList<Long>());
        ReflectionTestUtils.setField(packageType, "locations", new ArrayList<Location>());
        ReflectionTestUtils.setField(packageType, "products", new ArrayList<Product>());
        return packageType;
    }

    private PackageRequest buildValidPackageRequest() {
        DateTime startDate = new DateTime();
        DateTime endDate = startDate.plusYears(1);
        PackageRequest packageRequest = new PackageRequest();
        packageRequest.setSalesForceId("********");
        packageRequest.setSalesforceProductId("ASALESFORCEID");
        packageRequest.setStartDate(startDate);
        packageRequest.setEndDate(endDate);
        packageRequest.setAccountId(1l);
        packageRequest.setInitialCredits(100l);
        return packageRequest;

    }

    private Account createValidAccount() {
        Account account = new Account();
        ReflectionTestUtils.setField(account, "id", 1L);
        account.setName("account-name");
        account.setDescription("account-description");
        return account;
    }
}