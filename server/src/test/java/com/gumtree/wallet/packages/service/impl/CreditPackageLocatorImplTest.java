package com.gumtree.wallet.packages.service.impl;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.repository.account.AccountRepository;
import com.gumtree.seller.repository.product.ProductRepository;
import com.gumtree.seller.service.location.LocationService;
import com.gumtree.wallet.packages.repository.CreditPackageRepository;
import com.gumtree.wallet.packages.service.PackageTypeLocator;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import com.gumtree.seller.domain.product.entity.ProductName;

@RunWith(MockitoJUnitRunner.class)
public class CreditPackageLocatorImplTest {

    @InjectMocks
    private CreditPackageLocatorImpl creditPackageLocatorImpl;

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private LocationService locationService;

    @Mock
    private PackageTypeLocator packageTypeLocator;

    @Mock
    private ProductRepository productRepository;

    @Mock
    private Clock clock;

    @Mock
    private CategoryModel categoryModel;

    @Mock
    private CreditPackageRepository creditPackageRepository;

    @Test
    public void hasEligiblePackages_callsAccountRepositoryWhenIdSupplied() {
        Long accountId = 3L;

        creditPackageLocatorImpl.hasEligiblePackages(
                1L, 2L, accountId, ProductName.INSERTION
        );

        Mockito.verify(accountRepository).findOne(accountId);
    }

    @Test
    public void hasEligiblePackages_doesNotCallAccountRepositoryWhenAccountSupplied() {
        Long locationId = 1L;

        Mockito.when(locationService.getById(locationId)).thenReturn(new Location());
        Mockito.when(clock.getDateTime()).thenReturn(new DateTime());
        Mockito.when(productRepository.findByName(ProductName.INSERTION)).thenReturn(new Product(ProductName.INSERTION));

        creditPackageLocatorImpl.hasEligiblePackages(
                locationId, 2L, new Account(), ProductName.INSERTION
        );

        Mockito.verifyZeroInteractions(accountRepository);
    }
}
