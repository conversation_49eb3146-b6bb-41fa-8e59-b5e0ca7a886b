package com.gumtree.wallet;

import com.google.common.collect.ImmutableList;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.account.entity.Account;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.order.entity.OrderItem;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.repository.product.ProductRepository;
import com.gumtree.wallet.packages.domain.PaymentInstrument;
import com.gumtree.wallet.packages.domain.entity.CreditPackage;
import com.gumtree.wallet.packages.domain.entity.PackageType;
import com.gumtree.wallet.packages.service.CreditPackageLocator;
import com.gumtree.wallet.packages.service.CreditPackageService;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created by reweber on 05/08/2016
 */
@RunWith(MockitoJUnitRunner.class)
public class WalletServiceImplTest {

    @Mock private CreditPackageLocator creditPackageLocator;
    @Mock private CreditPackageService creditPackageService;
    @Mock private ProductRepository productRepository;
    @Mock private Clock clock;

    private static final List<ProductName> NON_CALL_TRACKING_PRODUCTS = ImmutableList.of(
            ProductName.EXTENDED_VEHICLE_HISTORY_CHECK,
            ProductName.SEARCH_STANDOUT
    );

    private static final List<ProductName> ORDERED_CALL_TRACKING_PRODUCTS = ImmutableList.of(
            ProductName.CALL_TRACKING_ADVERT_LEVEL_WITH_BLACKLISTING,
            ProductName.CALL_TRACKING_ADVERT_LEVEL,
            ProductName.CALL_TRACKING_ACCOUNT_LEVEL,
            ProductName.CALL_TRACKING_ACCOUNT_LEVEL_HIDDEN,
            ProductName.EMG_FREESPEE_PERMISSION
            );

    @Test
    public void coverByPackageShouldDelegateToHasEligiblePackages() {
        when(creditPackageLocator.hasEligiblePackages(1L, 2L, 3L, ProductName.BUMP_UP)).thenReturn(true);

        WalletServiceImpl walletService = new WalletServiceImpl(creditPackageLocator, creditPackageService, productRepository, clock);
        boolean coveredByPackage = walletService.isCoveredByPackage(1L, 2L, 3L, ProductName.BUMP_UP);
        assertTrue(coveredByPackage);
    }

    @Test
    public void usePackageShouldFinCreditPackageAndPayWithIt() {
        CreditPackage creditPackage = new CreditPackage();
        Long id = 1000L;
        creditPackage.setId(id);
        OrderItem orderItem = mock(OrderItem.class);

        Account account = new Account();
        Location location = new Location();
        long categoryId = 1L;

        when(creditPackageLocator.findBestCreditPackage(categoryId, location, account, ProductName.BUMP_UP)).thenReturn(creditPackage);
        DateTime dateTime = DateTime.now();
        when(clock.getDateTime()).thenReturn(dateTime);

        WalletServiceImpl walletService = new WalletServiceImpl(creditPackageLocator, null, productRepository, clock);

        Long result = walletService.usePackage(orderItem, account, location, categoryId, ProductName.BUMP_UP);

        verify(orderItem).payWithCreditPackage(creditPackage, dateTime);
        assertEquals(id, result);
    }

    @Test
    public void getPackageShouldReturnCreditPackage() {
        CreditPackage creditPackageFromMock = new CreditPackage();
        Long packageId = 123L;
        PackageType packageType = new PackageType();
        Product product = new Product();
        List<Product> productList = new ArrayList<>();
        creditPackageFromMock.setId(packageId);
        product.setName(ProductName.INSERTION);
        productList.add(product);
        packageType.setProducts(productList);
        packageType.setPaymentInstrument(PaymentInstrument.CREDIT);
        creditPackageFromMock.setPackageType(packageType);

        when(creditPackageService.getPackage(packageId)).thenReturn(creditPackageFromMock);

        WalletServiceImpl walletService = new WalletServiceImpl(creditPackageLocator, creditPackageService, productRepository, clock);
        CreditPackage creditPackage = walletService.getPackage(packageId);

        assertEquals(packageId, creditPackage.getId());
        assertEquals(PaymentInstrument.CREDIT, creditPackage.getPackageType().getPaymentInstrument());
    }

    @Test
    public void getPackagesByAccountShouldDelegateToCreditPackageService() {
        Long accountId = 1L;

        List<CreditPackage> mockResult = mock(List.class);
        when(creditPackageService.getPackagesForAccount(accountId, Boolean.FALSE)).thenReturn(mockResult);

        WalletServiceImpl walletService = new WalletServiceImpl(creditPackageLocator, creditPackageService, productRepository, clock);
        List<CreditPackage> packagesForAccount = walletService.getPackagesForAccount(accountId, Boolean.FALSE);

        assertThat(packagesForAccount, equalTo(mockResult));
    }

    @Test
    public void getDefaultProductsShouldReturnOnlyDefaultProducts() {
        Long locationId = 1L;
        Long categoryId = 2L;
        Long accountId = 3l;

        NON_CALL_TRACKING_PRODUCTS.stream()
                .forEach(productName -> setDefaultProductExpectation(locationId, categoryId, accountId, productName));

        WalletServiceImpl walletService = new WalletServiceImpl(creditPackageLocator, creditPackageService, productRepository, clock);
        List<Product> defaultProducts = walletService.getDefaultProducts(locationId, categoryId, accountId);
        assertThat(defaultProducts, hasSize(NON_CALL_TRACKING_PRODUCTS.size()));

        Set<ProductName> defaultProductSet = defaultProducts.stream()
                .map(defaultProduct -> defaultProduct.getName())
                .collect(Collectors.toSet());

        NON_CALL_TRACKING_PRODUCTS.stream().forEach(productName -> assertTrue(defaultProductSet.contains(productName)));
    }

    @Test
    public void getDefaultProductsShouldReturnCallTrackingProductOfHighestPrecedence() {
        Long locationId = 1L;
        Long categoryId = 2L;
        Long accountId = 3l;

        for (int i = 0; i < ORDERED_CALL_TRACKING_PRODUCTS.size(); i++) {
            for (int j = i + 1; j < ORDERED_CALL_TRACKING_PRODUCTS.size(); j++) {
                NON_CALL_TRACKING_PRODUCTS.stream()
                        .forEach(productName -> setDefaultProductExpectation(locationId, categoryId, accountId, productName));
                ProductName higherCallTracking = ORDERED_CALL_TRACKING_PRODUCTS.get(i);
                ProductName lowerCallTracking = ORDERED_CALL_TRACKING_PRODUCTS.get(j);
                setDefaultProductExpectation(locationId, categoryId, accountId, higherCallTracking);
                setDefaultProductExpectation(locationId, categoryId, accountId, lowerCallTracking);
                WalletServiceImpl walletService = new WalletServiceImpl(creditPackageLocator, creditPackageService, productRepository, clock);

                List<Product> defaultProducts = walletService.getDefaultProducts(locationId, categoryId, accountId);

                assertThat(defaultProducts, hasSize(NON_CALL_TRACKING_PRODUCTS.size() + 1));
                Set<ProductName> defaultProductSet = defaultProducts.stream()
                        .map(defaultProduct -> defaultProduct.getName())
                        .collect(Collectors.toSet());
                NON_CALL_TRACKING_PRODUCTS.stream().forEach(productName -> assertTrue(defaultProductSet.contains(productName)));
                assertTrue(higherCallTracking.getDisplayValue() + " should take precedence over " + lowerCallTracking.getDisplayValue(),
                        defaultProductSet.contains(higherCallTracking));

                MockitoAnnotations.initMocks(this);
            }
        }
    }

    private void setDefaultProductExpectation(Long locationId, Long categoryId, Long accountId, ProductName productName) {
        when(creditPackageLocator.hasEligiblePackages(locationId, categoryId, accountId, productName)).thenReturn(true);
        Product product = new Product();
        product.setName(productName);
        when(productRepository.findByName(productName)).thenReturn(product);
    }
}