package com.gumtree.wallet;

import com.gumtree.seller.domain.packages.CreditPackageUsage;
import com.gumtree.wallet.LedgerServiceImpl;
import com.gumtree.wallet.packages.domain.PackageSearchFilter;
import com.gumtree.wallet.packages.service.CreditPackageService;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.domain.Page;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class LedgerServiceImplTest {

    @Mock
    CreditPackageService creditPackageService;

    private final Long accountId = 1L;
    private final Long packageId = 2L;
    private final int page = 3;
    private final int batchSize = 4;

    @Test
    public void testGetPackageUsagesByPackageId() {
        Page<CreditPackageUsage> mockResult = mock(Page.class);

        when(creditPackageService.getPackageUsages(accountId, packageId, page, batchSize, false)).thenReturn(mockResult);

        LedgerServiceImpl ledger = new LedgerServiceImpl(creditPackageService);
        Page<CreditPackageUsage> packageUsages = ledger.getPackageUsages(accountId, packageId, page, batchSize, false);

        assertThat(packageUsages, equalTo(mockResult));
    }

    @Test
    public void testGetPackageUsagesByAccountId() {
        Page<CreditPackageUsage> mockResult = mock(Page.class);
        Long packageTypeId = 3L;
        DateTime fromDate = DateTime.now();
        DateTime toDate = DateTime.now().plusDays(3);
        Boolean active = false;

        PackageSearchFilter packageSearchFilter = new PackageSearchFilter(accountId, packageTypeId, fromDate, toDate, active, false);
        when(creditPackageService.getPackageUsages(packageSearchFilter, page, batchSize)).thenReturn(mockResult);

        LedgerServiceImpl ledger = new LedgerServiceImpl(creditPackageService);
        Page<CreditPackageUsage> packageUsages = ledger.getPackageUsages(packageSearchFilter, page, batchSize);

        assertThat(packageUsages, equalTo(mockResult));
    }
}