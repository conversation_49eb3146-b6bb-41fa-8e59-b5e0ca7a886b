package com.gumtree.personalization.domain.savedsearches.entity;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;

import java.util.HashSet;

import static org.fest.assertions.api.Assertions.assertThat;

public class SavedAdsTest {
    private static final long ADVERT_ID_1 = 1L;
    private static final long ADVERT_ID_2 = 2L;
    private static final long ADVERT_ID_3 = 3L;
    private static final int LIMIT = 2;

    @Test
    public void shouldAddAdvert() {
        // given
        SavedAds savedAds = new SavedAds();

        // when
        savedAds.addAdvert(ADVERT_ID_1, LIMIT);

        // then
        assertThat(savedAds.getAdverts()).isEqualTo(Sets.newHashSet(ADVERT_ID_1));
    }

    @Test
    public void shouldRemoveOldestAdvertIfAddingNewAdvertWillExceedLimit() {
        // given
        SavedAds savedAds = new SavedAds();
        savedAds.setAdverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_1, ADVERT_ID_2)));

        // when
        savedAds.addAdvert(ADVERT_ID_3, LIMIT);

        // then
        assertThat(savedAds.getAdverts()).isEqualTo(Sets.newHashSet(ADVERT_ID_2, ADVERT_ID_3));
    }

    @Test
    public void shouldAddAdvertToNonEmptySavedAds() {
        // given
        SavedAds savedAds = new SavedAds();
        savedAds.setAdverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_2)));

        // when
        savedAds.addAdvert(ADVERT_ID_1, LIMIT);

        // then
        assertThat(savedAds.getAdverts()).isEqualTo(Sets.newHashSet(ADVERT_ID_1, ADVERT_ID_2));
    }

    @Test
    public void shouldRemoveAdvert() {
        // given
        SavedAds savedAds = new SavedAds();
        savedAds.setAdverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_1, ADVERT_ID_2, ADVERT_ID_3)));

        // when
        savedAds.removeAdvert(ADVERT_ID_1);

        // then
        assertThat(savedAds.getAdverts()).isEqualTo(Sets.newHashSet(ADVERT_ID_2, ADVERT_ID_3));
    }

    @Test
    public void shouldRemoveAdvertFromEmptySavedAds() {
        // given
        SavedAds savedAds = new SavedAds();

        // when
        savedAds.removeAdvert(ADVERT_ID_1);

        // then
        assertThat(savedAds.getAdverts()).isEqualTo(new HashSet<Long>());
    }
}
