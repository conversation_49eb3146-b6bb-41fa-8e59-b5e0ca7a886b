package com.gumtree.personalization.config;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.common.mongo.savedsearch.SavedSearch;
import com.gumtree.common.mongo.savedsearch.SavedSearchDBActions;
import com.gumtree.common.mongo.savedsearch.SavedSearchDBActionsTest;
import com.mongodb.DBCollection;
import com.mongodb.Mongo;
import com.mongodb.DB;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.authentication.UserCredentials;
import org.springframework.data.mongodb.config.AbstractMongoConfiguration;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import java.util.HashSet;
import java.util.Set;

@Configuration
@Import(EmbeddedMongoDbConfiguration.class)
@EnableMongoRepositories({"com.gumtree.personalization.repository" })
public class EmbeddedMongoRepositoryConfig extends AbstractMongoConfiguration {

    @Bean
    @Override
    public Mongo mongo() throws Exception {
        return new Mongo("localhost", EmbeddedMongoDbConfiguration.DB_PORT);
    }

    @Override
    public String getDatabaseName() {
        return "test-db";
    }

    @Override
    public UserCredentials getUserCredentials() {
        return UserCredentials.NO_CREDENTIALS;
    }

    @Bean
    @Override
    public MappingMongoConverter mappingMongoConverter() throws Exception {
        MappingMongoConverter converter = super.mappingMongoConverter();
        // removes '_class' property from resulting JSON document that is send to MongoDB
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        return converter;
    }

    @Override
    protected Set<Class<?>> getInitialEntitySet() throws ClassNotFoundException {
        Set<Class<?>> classes = new HashSet<Class<?>>();
        classes.add(SavedSearch.class);
        return classes;
    }

    @Bean
    public SavedSearchDBActions savedSearchDBActions(Mongo mongo) {
        DB db = mongo.getDB("test_db");
        final DBCollection savedSearchDBCollection =db.getCollection("test_saved_search");
        final MetricRegistry metricRegistry = Mockito.mock(MetricRegistry.class);
        final Counter counter = Mockito.mock(Counter.class);
        Mockito.when(metricRegistry.counter(SavedSearchDBActions.EXCEPTIONS_NAME)).thenReturn(counter);
        return new SavedSearchDBActions(savedSearchDBCollection,metricRegistry);
    }

}
