package com.gumtree.personalization.config;

import de.flapdoodle.embed.mongo.Command;
import de.flapdoodle.embed.mongo.MongodExecutable;
import de.flapdoodle.embed.mongo.MongodProcess;
import de.flapdoodle.embed.mongo.MongodStarter;
import de.flapdoodle.embed.mongo.config.DownloadConfigBuilder;
import de.flapdoodle.embed.mongo.config.ExtractedArtifactStoreBuilder;
import de.flapdoodle.embed.mongo.config.IMongodConfig;
import de.flapdoodle.embed.mongo.config.MongodConfigBuilder;
import de.flapdoodle.embed.mongo.config.Net;
import de.flapdoodle.embed.mongo.config.RuntimeConfigBuilder;
import de.flapdoodle.embed.mongo.distribution.Version;
import de.flapdoodle.embed.mongo.distribution.Versions;
import de.flapdoodle.embed.process.config.IRuntimeConfig;
import de.flapdoodle.embed.process.runtime.Network;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EmbeddedMongoDbConfiguration implements DisposableBean, InitializingBean {
    public static final int DB_PORT = 12345;
    private MongodExecutable mongodExe;
    private MongodProcess mongod;

    @Override
    public void afterPropertiesSet() throws Exception {
        startEmbeddedMongoDb();
    }

    @Override
    public void destroy() throws Exception {
        if(this.mongod != null) {
            this.mongod.stop();
        }
    }

    private void startEmbeddedMongoDb() throws Exception {
        Command command = Command.MongoD;

        IRuntimeConfig runtimeConfig = new RuntimeConfigBuilder()
                .defaults(command)
                .artifactStore(new ExtractedArtifactStoreBuilder()
                        .defaults(command)
                        .download(new DownloadConfigBuilder()
                                .defaultsForCommand(command)
                                .downloadPath("https://nexus.gum-ops-prod.gumtree.cloud/repository/mongodb-releases/")))
                .build();

        IMongodConfig mongodConfig = new MongodConfigBuilder()
                .version(Versions.withFeatures(Version.Main.V2_2))
                .net(new Net(DB_PORT, Network.localhostIsIPv6()))
                .build();


        MongodStarter runtime = MongodStarter.getInstance(runtimeConfig);
        mongodExe = runtime.prepare(mongodConfig);
        mongod = mongodExe.start();
    }
}
