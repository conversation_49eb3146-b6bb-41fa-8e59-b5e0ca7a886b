package com.gumtree.personalization.repository;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.gumtree.personalization.config.EmbeddedMongoRepositoryConfig;
import com.gumtree.personalization.domain.savedsearches.entity.SavedAds;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import java.util.List;

import static org.fest.assertions.api.Assertions.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(loader = AnnotationConfigContextLoader.class,
        classes = {EmbeddedMongoRepositoryConfig.class})
public class SavedAdsRepositoryTest {
    private static final Long USER_ID = 1L;

    @Autowired
    private SavedAdsRepository repository;

    @Before
    public void before() {
        repository.deleteAll();
    }

    @Test
    public void shouldSaveAdvert() {
        // given
        SavedAds savedAds = SavedAds.builder().advert(1L).userId(USER_ID).build();

        // when
        SavedAds result = repository.save(savedAds);

        // then
        assertThat(result.getId()).isNotNull();

        // and
        List<SavedAds> all = repository.findAll();
        assertThat(all).hasSize(1);
        assertThat(all.get(0).getUserId()).isEqualTo(USER_ID);
        assertThat(all.get(0).getAdverts()).isEqualTo(Sets.newHashSet(Lists.newArrayList(1L)));
    }
}
