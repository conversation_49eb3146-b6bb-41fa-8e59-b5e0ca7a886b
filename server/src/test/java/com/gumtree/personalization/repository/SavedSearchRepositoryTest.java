package com.gumtree.personalization.repository;

import com.google.common.collect.ImmutableList;
import com.gumtree.common.mongo.savedsearch.SavedSearch;
import com.gumtree.common.mongo.savedsearch.SavedSearchDBActions;
import com.gumtree.personalization.config.EmbeddedMongoRepositoryConfig;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import java.util.Date;
import java.util.List;

import static org.fest.assertions.api.Assertions.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(loader = AnnotationConfigContextLoader.class,
        classes = {EmbeddedMongoRepositoryConfig.class})
public class SavedSearchRepositoryTest {
    private static final Long USER_ID = 1L;
    public static final Sort CREATED_AT_SORT = new Sort(new Sort.Order(Sort.Direction.DESC, "createdAt"));
    private static final String sampleJson = "{\"location\":\"clapham\",\"categoryId\":70," +
            "\"price\":{\"maxValue\":140}}";
    @Autowired
    private SavedSearchDBActions repository;

    @Before
    public void before() throws SavedSearchDBActions.SavedSearchDBActionsException {
        repository.deleteAll();
    }

    @Test
    public void shouldSaveSavedSearch() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // given
        Date now = new Date();
        SavedSearch ss = builder().setUserId(USER_ID).setCreatedAt(now.getTime())
                .setEDialogId("eid").setIsEmailAlert(false).setUrl("url").setSearchJson(sampleJson).build();

        // when
        SavedSearch savedSS = repository.save(ss);

        // then
        assertThat(savedSS.getId()).isNotNull();
        List<SavedSearch> actualSaveSearches = repository.findByUserIdWithOrderCreatedAtDescending(USER_ID);
        assertThat(actualSaveSearches).hasSize(1);
        assertThat(actualSaveSearches.get(0).getCreatedAt()).isEqualTo(now.getTime());
    }

    @Test
    public void shouldDelete() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // given
        final ImmutableList<SavedSearch> ss = ImmutableList.of(
                builder().setUserId(USER_ID).setEDialogId("eid").setIsEmailAlert(false).setUrl("url").setSearchJson(sampleJson).build(),
                builder().setUserId(USER_ID).setEDialogId("eid").setIsEmailAlert(false).setUrl("url").setSearchJson(sampleJson).build());
        List<SavedSearch> savedSS = repository.save(ss);

        // when
        repository.delete(savedSS.get(0).getId());

        // then
        List<SavedSearch> actualSS = repository.findByUserIdWithOrderCreatedAtDescending(USER_ID);
        assertThat(actualSS).hasSize(1);
        assertThat(actualSS.get(0).getId()).isEqualTo(savedSS.get(1).getId());
    }

    @Test
    public void findByUserIdShouldReturnedSavedSearchesOrderedByCreationDateDesc()
            throws SavedSearchDBActions.SavedSearchDBActionsException {
        // given
        Date now = new Date();
        ImmutableList<SavedSearch> ss = ImmutableList.of(
                builder().setUserId(USER_ID).setSearchJson(sampleJson).setCreatedAt(DateUtils.addMinutes(now, 5).getTime())
                        .setUrl("now + 5").build(),
                builder().setUserId(USER_ID).setSearchJson(sampleJson).setCreatedAt(now.getTime()).setUrl("now").build(),
                builder().setUserId(USER_ID).setSearchJson(sampleJson).setCreatedAt(null).setUrl("created-at-not-set").build(),
                builder().setUserId(USER_ID).setSearchJson(sampleJson).setCreatedAt(DateUtils.addMinutes(now, 3).getTime())
                        .setUrl("now + 3").build(),
                builder().setUserId(USER_ID).setSearchJson(sampleJson).setCreatedAt(DateUtils.addMinutes(now, -2).getTime())
                        .setUrl("now - 1").build());
        repository.save(ss);

        // when
        List<SavedSearch> savedSearches = repository.findByUserIdWithOrderCreatedAtDescending(USER_ID);

        // then
        assertThat(savedSearches).hasSize(5);
        assertThat(savedSearches.get(0).getUrl()).isEqualTo("now + 5");
        assertThat(savedSearches.get(1).getUrl()).isEqualTo("now + 3");
        assertThat(savedSearches.get(2).getUrl()).isEqualTo("now");
        assertThat(savedSearches.get(3).getUrl()).isEqualTo("now - 1");
        assertThat(savedSearches.get(4).getUrl()).isEqualTo("created-at-not-set");
    }

    private SavedSearch.Builder builder() {
        return new SavedSearch.Builder().setEDialogId("eid").setIsEmailAlert(false);
    }
}
