package com.gumtree.seller.notifications.kafka;

import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.config.search.AdFavouriteFeeder;
import com.gumtree.seller.event.advert.FavouriteEvent;
import com.vibrancy.events.kafka.favouriteadverts.FavouriteAdverts;
import com.vibrancy.events.kafka.favouriteadverts.favouriteEvent;
import com.vibrancy.events.kafka.favouriteadverts.triggerBy;
import org.apache.camel.EndpointInject;
import org.apache.camel.Produce;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.test.junit4.CamelTestSupport;
import org.junit.Test;

import static com.gumtree.config.search.AdFavouriteFeeder.Trigger.ADD_FAVOURITE;

public class FavouriteNotificationRouteBuilderTest extends CamelTestSupport {
    private static final Clock CLOCK = new StoppedClock();
    private static final String FAVOURITE_DESTINATION_ENDPOINT = "mock:realtime-output";
    private static final String EMAIL = "<EMAIL>";
    private static final long USER_ID = 88L;
    private static final long ADVERT_ID = 99L;
    @EndpointInject(uri = FAVOURITE_DESTINATION_ENDPOINT)
    protected MockEndpoint favouriteDestinationEndpoint;
    @Produce(uri = FavouriteNotificationRouteBuilder.SOURCE_ENDPOINT)
    protected ProducerTemplate producer;

    @Override
    protected RouteBuilder createRouteBuilder() {
        return new FavouriteNotificationRouteBuilder(FAVOURITE_DESTINATION_ENDPOINT, CLOCK);
    }

    @Test
    public void shouldSendFavouriteEventToDestinationURI() throws InterruptedException {
        boolean status = true;
        FavouriteEvent favouriteEvent = new FavouriteEvent(ADVERT_ID, USER_ID, EMAIL, status);
        AdFavouriteFeeder.FeedEvent event = new AdFavouriteFeeder.FeedEvent(favouriteEvent, ADD_FAVOURITE, CLOCK.now());

        FavouriteAdverts favAds = new FavouriteAdverts(
                triggerBy.valueOf(event.getTriggerBy().name()),
                event.getTimestamp(),
                new favouriteEvent(
                        event.getFavouriteEvent().getAdvertId(),
                        event.getFavouriteEvent().isStatus(),
                        event.getFavouriteEvent().getUserId(),
                        event.getFavouriteEvent().getUserEmail())
        );

        //expect
        favouriteDestinationEndpoint.expectedBodiesReceived(favAds);
        favouriteDestinationEndpoint.expectedHeaderReceived("kafka.TIMESTAMP", CLOCK.now());

        // when
        producer.sendBody(event);
        // then
        favouriteDestinationEndpoint.assertIsSatisfied();
    }
}
