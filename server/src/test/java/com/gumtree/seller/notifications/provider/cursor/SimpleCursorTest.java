package com.gumtree.seller.notifications.provider.cursor;

import com.gumtree.seller.notifications.provider.SimpleCursor;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InOrder;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.jdbc.core.RowMapper;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class SimpleCursorTest {

    private Connection connection;

    private Statement statement;

    private ResultSet resultSet;

    private RowMapper<Map.Entry<Long, Long>> rowMapper;

    private SimpleCursor cursor;

    @Before
    public void init() throws Exception {
        connection = mock(Connection.class);
        statement = mock(Statement.class);
        resultSet = mock(ResultSet.class);
        rowMapper = mock(RowMapper.class);
        cursor = new SimpleCursor(connection, statement, resultSet);

        when(rowMapper.mapRow(eq(resultSet), anyInt())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                long row = Long.valueOf((Integer) invocationOnMock.getArguments()[1]);
                return new AbstractMap.SimpleEntry<Long, Long>(row, row);
            }
        });
    }

    @Test
    public void shouldCloseJDBCResourcesIfResultSetHasNoMoreResults() throws Exception {
        when(resultSet.next()).thenReturn(false);
        Map<Long, Long> map = cursor.next(10, rowMapper);
        assertThat(map.size(), equalTo(0));
        InOrder order = inOrder(connection, statement, resultSet);
        order.verify(resultSet).close();
        order.verify(statement).close();
        order.verify(connection).close();
    }

    @Test
    public void shouldReturnResultsEvenIfCountIsGreaterThanAvailableResultsInResultSet() throws Exception {
        when(resultSet.next()).thenReturn(true).thenReturn(false);
        Map<Long, Long> map = cursor.next(2, rowMapper);
        assertThat(map.size(), equalTo(1));
        assertThat(map.get(0L), equalTo(0L));
        verify(resultSet, never()).close();
        verify(statement, never()).close();
        verify(connection, never()).close();
    }

    @Test
    public void shouldReturnOnlySpecifiedNumberOfResultsEvenIfResultSetHasMore() throws Exception {
        when(resultSet.next()).thenReturn(true).thenReturn(true).thenReturn(true).thenReturn(false);
        Map<Long, Long> map = cursor.next(2, rowMapper);
        assertThat(map.size(), equalTo(2));
        assertThat(map.get(0L), equalTo(0L));
        assertThat(map.get(1L), equalTo(1L));
        verify(resultSet, never()).close();
        verify(statement, never()).close();
        verify(connection, never()).close();
    }

    @Test
    public void shouldExtractResultsCorrectlyOverMultipleCalls() throws Exception {
        when(resultSet.next()).thenReturn(true).thenReturn(true).thenReturn(true).thenReturn(false);
        Map<Long, Long> union = new HashMap<Long, Long>();
        union.putAll(cursor.next(1, rowMapper));
        union.putAll(cursor.next(1, rowMapper));
        union.putAll(cursor.next(1, rowMapper));
        union.putAll(cursor.next(1, rowMapper));
        assertThat(union.size(), equalTo(3));
        assertThat(union.get(0L), equalTo(0L));
        assertThat(union.get(1L), equalTo(1L));
        assertThat(union.get(2L), equalTo(2L));
        InOrder order = inOrder(connection, statement, resultSet);
        order.verify(resultSet).close();
        order.verify(statement).close();
        order.verify(connection).close();
    }

    @Test(expected = SQLException.class)
    public void shouldStillCloseStatementAndConnectionIfResultSetCloseFails() throws Exception {
        when(resultSet.next()).thenReturn(false);
        doThrow(new SQLException()).when(resultSet).close();
        cursor.next(10, rowMapper);
        InOrder order = inOrder(connection, statement, resultSet);
        order.verify(resultSet).close();
        order.verify(statement).close();
        order.verify(connection).close();
    }

    @Test(expected = SQLException.class)
    public void shouldStillCloseResultSetAndConnectionIfStatementCloseFails() throws Exception {
        when(resultSet.next()).thenReturn(false);
        doThrow(new SQLException()).when(statement).close();
        cursor.next(10, rowMapper);
        InOrder order = inOrder(connection, statement, resultSet);
        order.verify(resultSet).close();
        order.verify(statement).close();
        order.verify(connection).close();
    }

    @Test(expected = SQLException.class)
    public void shouldStillCloseResultSetAndStatementIfConnectionCloseFails() throws Exception {
        when(resultSet.next()).thenReturn(false);
        doThrow(new SQLException()).when(connection).close();
        cursor.next(10, rowMapper);
        InOrder order = inOrder(connection, statement, resultSet);
        order.verify(resultSet).close();
        order.verify(statement).close();
        order.verify(connection).close();
    }
}
