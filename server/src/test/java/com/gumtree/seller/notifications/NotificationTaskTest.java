package com.gumtree.seller.notifications;

import com.gumtree.common.util.throttle.Throttle;
import com.gumtree.sapi.spec.domain.FlatAd;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.notifications.advert.AdvertBuilderFactory;
import com.gumtree.seller.notifications.provider.AdvertProvider;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionOperations;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class NotificationTaskTest {

    private AdvertProvider advertProvider;
    private NotificationCallback notificationCallback;
    private Throttle throttle;
    private List<FlatAd> adverts;
    private TransactionOperations transactionTemplate;
    private AdvertBuilderFactory<FlatAd> advertBuilderFactory;

    @Test
    public void verfiyNotificationSentToCallback() throws Exception {
        NotificationTask task = createTask();
        FlatAd advert = mockAdvertWithStatus(AdvertStatus.AWAITING_SCREENING);
        task.call();
        ArgumentCaptor<Iterable> advertsCaptor = ArgumentCaptor.forClass(Iterable.class);
        verify(notificationCallback).onBatch(advertsCaptor.capture(), any(NotificationStatus.class));
        Iterable<FlatAd> adverts = advertsCaptor.getValue();
        for (FlatAd a : adverts) {
            assertThat(a.getId(), equalTo(advert.getId()));
        }
        verify(throttle, times(1)).regulate();
        verify(advertProvider).close();
    }

    private NotificationTask createTask() {
        notificationCallback = mock(NotificationCallback.class);
        throttle = mock(Throttle.class);
        advertProvider = mock(AdvertProvider.class);
        transactionTemplate = mock(TransactionOperations.class);
        advertBuilderFactory = mock(AdvertBuilderFactory.class);

        NotificationTask task = new NotificationTask(
                advertProvider,
                advertBuilderFactory,
                throttle,
                notificationCallback,
                transactionTemplate);

        adverts = mock(List.class);
        when(advertProvider.getNextPage(advertBuilderFactory)).thenReturn(adverts);
        when(transactionTemplate.execute(any(TransactionCallback.class))).then(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                TransactionCallback callback = (TransactionCallback) invocationOnMock.getArguments()[0];
                TransactionStatus status = mock(TransactionStatus.class);
                return callback.doInTransaction(status);
            }
        });

        return task;
    }

    private FlatAd createAdvert(AdvertStatus status) {
        FlatAd advert = new FlatAd();
        advert.setStatus(status.toString());
        return advert;
    }

    private FlatAd mockAdvertWithStatus(AdvertStatus status) {
        FlatAd advert = createAdvert(status);
        when(adverts.iterator()).thenReturn(Arrays.asList(advert).iterator());
        when(adverts.size()).thenReturn(1).thenReturn(0);

        return advert;
    }
}
