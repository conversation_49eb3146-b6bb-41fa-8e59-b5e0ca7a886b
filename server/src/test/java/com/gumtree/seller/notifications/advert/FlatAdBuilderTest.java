package com.gumtree.seller.notifications.advert;

import com.google.common.base.Optional;
import com.google.common.collect.Sets;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.converter.search.FlatAdAttributeExpander;
import com.gumtree.api.domain.advert.DeleteReason;
import com.gumtree.sapi.spec.domain.Centroid;
import com.gumtree.sapi.spec.domain.Feature;
import com.gumtree.sapi.spec.domain.FlatAd;
import com.gumtree.sapi.spec.domain.GeoPoint;
import com.gumtree.sapi.spec.domain.Location;
import com.gumtree.seller.category.TestCategory;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.domain.attribute.dictionary.EnumValue;
import com.gumtree.seller.domain.category.exception.CategoryNotFoundException;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.email.EmailUrlScheme;
import com.gumtree.seller.service.advert.AdvertLocationResolver;
import com.gumtree.seller.service.location.LocationModel;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.gumtree.sapi.spec.domain.Category.aCategory;
import static com.gumtree.sapi.spec.domain.ExpirableFeature.anExpirableFeature;
import static com.gumtree.sapi.spec.domain.Location.aLocation;
import static com.gumtree.sapi.spec.domain.UrlFeature.aUrlFeature;
import static com.gumtree.sapi.spec.domain.User.aUser;
import static com.gumtree.seller.test.TestUtils.createLocation;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasEntry;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

/**
 * TODO: Validate values when nothing is specified (defaults)
 */
@RunWith(MockitoJUnitRunner.class)
public class FlatAdBuilderTest {

    @Mock private AdvertLocationResolver locationResolver;
    @Mock private LocationModel locationModel;
    @Mock private EmailUrlScheme urlScheme;
    @Mock private CategoryModel categoryModel;
    @Mock private FlatAdAttributeExpander flatAdAttributeExpander;

    private FlatAdBuilder builder;

    @Before
    public void init() {
        builder = new FlatAdBuilder(locationResolver, locationModel, categoryModel, urlScheme, flatAdAttributeExpander);
    }

    @Test
    public void shouldTransformCategoryIdIntoRelevantCategoryDataModels() {
        Long categoryId = 1L;
        Category category = TestCategory.category(categoryId, "cat1", "Cat 1");
        List<Category> hierarchy = Arrays.asList(
                TestCategory.category(3L, "cat3", "Cat 3"),
                TestCategory.category(2L, "cat2", "Cat 2"),
                category
        );

        when(categoryModel.getCategory(categoryId)).thenReturn(Optional.of(category));
        when(categoryModel.getFullPath(categoryId)).thenReturn(hierarchy);

        FlatAd flatAd = builder.categoryId(categoryId).build();

        com.gumtree.sapi.spec.domain.Category primaryCategory = flatAd.getPrimaryCategory();
        assertThat(primaryCategory, equalTo(
                aCategory().withId(categoryId).withName("cat1").withDisplayName("Cat 1").asPrimary(true).build()));
        assertThat(flatAd.getCategories().size(), equalTo(3));

        assertThat(flatAd.getCategories(), containsInAnyOrder(
                aCategory().withId(categoryId).withName("cat1").withDisplayName("Cat 1").asPrimary(true).build(),
                aCategory().withId(2L).withName("cat2").withDisplayName("Cat 2").asPrimary(false).build(),
                aCategory().withId(3L).withName("cat3").withDisplayName("Cat 3").asPrimary(false).build()));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfCategoryIdIsNull() {
        builder.categoryId(null);
    }

    @Test(expected = CategoryNotFoundException.class)
    public void shouldThrowExceptionIfCategoryIdIsNotRecognised() {
        when(categoryModel.getCategory(1L)).thenReturn(Optional.<Category>absent());
        builder.categoryId(1L);
    }

    @Test
    public void shouldTransformLocationIdsIntoRelevantLocationDataModels() {
        Long primaryLocationId = new Long(2);
        when(locationResolver.getHierarchicalLocationIdsFromLeaf(1L))
                .thenReturn(Arrays.asList(1L, 2L, 3L));
        when(locationResolver.getHierarchicalLocationIdsFromLeaf(2L))
                .thenReturn(Arrays.asList(1L, 2L, 3L));
        when(locationResolver.getHierarchicalLocationIdsFromLeaf(3L))
                .thenReturn(Arrays.asList(1L, 2L, 3L, 4L));
        when(locationResolver.getSmallestLocation(Sets.newHashSet(1L, 2L, 3L, 4L)))
                .thenReturn(primaryLocationId);

        when(locationModel.getById(1L)).thenReturn(createLocation(1L, "loc1", "Loc 1"));
        when(locationModel.getById(2L)).thenReturn(createLocation(2L, "loc2", "Loc 2"));
        when(locationModel.getById(3L)).thenReturn(createLocation(3L, "loc3", "Loc 3"));
        when(locationModel.getById(4L)).thenReturn(createLocation(4L, "loc4", "Loc 4"));

        FlatAd flatAd = builder.locationIds(Arrays.asList(1L, 2L, 3L)).build();

        Location primaryLocation = flatAd.getPrimaryLocation();
        assertThat(primaryLocation, equalTo(
                aLocation().withId(2L).withName("loc2").withDisplayName("Loc 2").asPrimary(true).build()));
        assertThat(flatAd.getLocations().size(), equalTo(4));

        assertThat(flatAd.getLocations(), containsInAnyOrder(
                aLocation().withId(1L).withName("loc1").withDisplayName("Loc 1").asPrimary(false).build(),
                aLocation().withId(2L).withName("loc2").withDisplayName("Loc 2").asPrimary(true).build(),
                aLocation().withId(3L).withName("loc3").withDisplayName("Loc 3").asPrimary(false).build(),
                aLocation().withId(4L).withName("loc4").withDisplayName("Loc 4").asPrimary(false).build())
        );

        // TODO: We can remove these next fields once we're off Solr
        assertThat(flatAd.getLocationId().size(), equalTo(4));
        assertThat(flatAd.getLocationName().size(), equalTo(4));
        assertThat(flatAd.getLocationDisplayName().size(), equalTo(4));

        assertThat(flatAd.getLocationId(), containsInAnyOrder(2L, 3L, 1L, 4L));
        assertThat(flatAd.getLocationName(), containsInAnyOrder("loc2", "loc3", "loc1", "loc4"));
        assertThat(flatAd.getLocationDisplayName(), containsInAnyOrder("Loc 2", "Loc 3", "Loc 1", "Loc 4"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfAnyLocationIdIsNull() {
        builder.locationIds(Arrays.asList(1L, null, 3L));
    }

    @Test
    public void shouldSetIdCorrectly() {
        builder.id(1L);
        assertThat(builder.build().getId(), equalTo(1L));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfIdIsNull() {
        builder.id(null);
    }

    @Test
    public void shouldSetTitleCorrectly() {
        builder.title("My title");
        assertThat(builder.build().getTitle(), equalTo("My title"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfTitleIsNull() {
        builder.title(null);
    }

    @Test
    public void shouldSetDescriptionCorrectly() {
        builder.description("My description");
        assertThat(builder.build().getDescription(), equalTo("My description"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfDescriptionIsNull() {
        builder.description(null);
    }

    @Test
    public void shouldSetVersionCorrectly() {
        builder.version(1L);
        assertThat(builder.build().getVersion(), equalTo(1L));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfVersionIsNull() {
        builder.version(null);
    }

    @Test
    public void shouldSetStatusStringCorrectly() {
        builder.status("LIVE");
        assertThat(builder.build().getStatus(), equalTo("LIVE"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfStatusStringIsNull() {
        builder.status((String) null);
    }

    @Test
    public void shouldSetStatusCorrectly() {
        builder.status(AdvertStatus.LIVE);
        assertThat(builder.build().getStatus(), equalTo("LIVE"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfStatusIsNull() {
        builder.status((AdvertStatus) null);
    }

    @Test
    public void shouldSetLastModifiedDateCorrectly() {
        builder.lastModifiedDate(new Date(10000000000L));
        assertThat(builder.build().getLastModifiedDate(), equalTo(10000000000L));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfLastModifiedDateIsNull() {
        builder.lastModifiedDate((Date) null);
    }

    @Test
    public void shouldSetCreatedDateCorrectly() {
        builder.createdDate(new Date(10000000000L));
        assertThat(builder.build().getCreatedDate(), equalTo(10000000000L));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfCreatedDateIsNull() {
        builder.createdDate((Date) null);
    }

    @Test
    public void shouldSetPublishedDateCorrectly() {
        builder.publishedDate(new Date(10000000000L));
        assertThat(builder.build().getPublishedDate(), equalTo(10000000000L));
    }

    @Test
    public void shouldAcceptANullPublishedDate() {
        builder.publishedDate((Date) null);
        assertThat(builder.build().getPublishedDate(), nullValue());
    }

    @Test
    public void shouldSetDeletedDateCorrectly() {
        builder.deletedDate(new Date(10000000000L));
        assertThat(builder.build().getDeletedDate(), equalTo(10000000000L));
    }

    @Test
    public void shouldAcceptANullDeletedDate() {
        builder.deletedDate((Date) null);
        assertThat(builder.build().getDeletedDate(), nullValue());
    }

    @Test
    public void shouldSetExpiryDateCorrectly() {
        builder.expiryDate(new Date(10000000000L));
        assertThat(builder.build().getExpiryDate(), equalTo(10000000000L));
    }

    @Test
    public void shouldAcceptANullExpiryDate() {
        builder.expiryDate((Date) null);
        assertThat(builder.build().getExpiryDate(), nullValue());
    }

    @Test
    public void shouldSetLastModifiedDateTimeCorrectly() {
        builder.lastModifiedDate(new DateTime(10000000000L));
        assertThat(builder.build().getLastModifiedDate(), equalTo(10000000000L));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfLastModifiedDateTimeIsNull() {
        builder.lastModifiedDate((DateTime) null);
    }

    @Test
    public void shouldSetCreatedDateTimeCorrectly() {
        builder.createdDate(new DateTime(10000000000L));
        assertThat(builder.build().getCreatedDate(), equalTo(10000000000L));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfCreatedDateTimeIsNull() {
        builder.createdDate((DateTime) null);
    }

    @Test
    public void shouldSetPublishedDateTimeCorrectly() {
        builder.publishedDate(new DateTime(10000000000L));
        assertThat(builder.build().getPublishedDate(), equalTo(10000000000L));
    }

    @Test
    public void shouldAcceptANullPublishedDateTime() {
        builder.publishedDate((DateTime) null);
        assertThat(builder.build().getPublishedDate(), nullValue());
    }

    @Test
    public void shouldSetDeletedDateTimeCorrectly() {
        builder.deletedDate(new DateTime(10000000000L));
        assertThat(builder.build().getDeletedDate(), equalTo(10000000000L));
    }

    @Test
    public void shouldAcceptANullDeletedDateTime() {
        builder.deletedDate((DateTime) null);
        assertThat(builder.build().getDeletedDate(), nullValue());
    }

    @Test
    public void shouldSetExpiryDateTimeCorrectly() {
        builder.expiryDate(new DateTime(10000000000L));
        assertThat(builder.build().getExpiryDate(), equalTo(10000000000L));
    }

    @Test
    public void shouldAcceptANullExpiryDateTime() {
        builder.expiryDate((DateTime) null);
        assertThat(builder.build().getExpiryDate(), nullValue());
    }

    @Test
    public void shouldSetContactEmailCorrectly() {
        builder.contactEmail("<EMAIL>");
        assertThat(builder.build().getContactEmail(), equalTo("<EMAIL>"));
    }

    @Test
    public void shouldAcceptANullContactEmail() {
        builder.contactEmail(null);
        assertThat(builder.build().getContactEmail(), nullValue());
    }

    @Test
    public void shouldSetContactTelephoneCorrectly() {
        builder.contactTelephone("12345678");
        assertThat(builder.build().getContactTelephone(), equalTo("12345678"));
    }

    @Test
    public void shouldAcceptANullContactTelephone() {
        builder.contactTelephone(null);
        assertThat(builder.build().getContactTelephone(), nullValue());
    }

    @Test
    public void shouldSetContactNameCorrectly() {
        builder.contactName("Andrew");
        assertThat(builder.build().getContactName(), equalTo("Andrew"));
    }

    @Test
    public void shouldAcceptANullContactName() {
        builder.contactName(null);
        assertThat(builder.build().getContactName(), nullValue());
    }

    @Test
    public void shouldSetContactUrlCorrectly() {
        builder.contactUrl("http://www.test.com");
        assertThat(builder.build().getContactUrl(), equalTo("http://www.test.com"));
    }

    @Test
    public void shouldAcceptANullContactUrl() {
        builder.contactUrl(null);
        assertThat(builder.build().getContactUrl(), nullValue());
    }

    @Test
    public void shouldSetPostcodeCorrectly() {
        builder.postcode("TW9 1EH");
        assertThat(builder.build().getPostcode(), equalTo("TW9 1EH"));
    }

    @Test
    public void shouldAcceptANullPostcode() {
        builder.postcode(null);
        assertThat(builder.build().getPostcode(), nullValue());
    }

    @Test
    public void shouldSetOutcodeCorrectly() {
        builder.outcode("TW9");
        assertThat(builder.build().getOutcode(), equalTo("TW9"));
    }

    @Test
    public void shouldAcceptANullOutcode() {
        builder.outcode(null);
        assertThat(builder.build().getOutcode(), nullValue());
    }

    @Test
    public void shouldSetCentroidCorrectly() {
        builder.centroid(new BigDecimal("54.10"), new BigDecimal("-1.2456"));
        Centroid centroid = builder.build().getCentroid();
        assertThat(centroid.getLatitude(), equalTo(new BigDecimal("54.10")));
        assertThat(centroid.getLongitude(), equalTo(new BigDecimal("-1.2456")));
        GeoPoint pin = builder.build().getPin();
        assertThat(pin.getLatitude().get(), equalTo(new BigDecimal("54.10")));
        assertThat(pin.getLongitude().get(), equalTo(new BigDecimal("-1.2456")));
    }

    @Test
    public void shouldNotSetCentroidIfLatitudeIsNull() {
        builder.centroid(null, new BigDecimal("-1.2456"));
        assertThat(builder.build().getCentroid(), nullValue());
        assertThat(builder.build().getPin(), nullValue());
    }

    @Test
    public void shouldNotSetCentroidIfLongitudeIsNull() {
        builder.centroid(new BigDecimal("54.10"), null);
        assertThat(builder.build().getCentroid(), nullValue());
        assertThat(builder.build().getPin(), nullValue());
    }

    @Test
    public void shouldNotSetCentroidIfLongitudeIsNullAndLongitudeIsNull() {
        builder.centroid(null, null);
        assertThat(builder.build().getCentroid(), nullValue());
        assertThat(builder.build().getPin(), nullValue());
    }

    @Test
    public void shouldSetVisibleOnMapCorrectlyWhenTrue() {
        builder.visibleOnMap(true);
        assertThat(builder.build().isVisibleOnMap(), equalTo(true));
    }

    @Test
    public void shouldSetVisibleOnMapCorrectlyWhenFalse() {
        builder.visibleOnMap(false);
        assertThat(builder.build().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void shouldSetVisibleOnMapCorrectlyWhenNull() {
        builder.visibleOnMap(null);
        assertThat(builder.build().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void visibleOnMapShouldBeFalseIfNotDefined() {
        assertThat(builder.build().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void shouldAcceptMultipleAttributes() {
        builder.attribute(CategoryConstants.Attribute.VEHICLE_MAKE.getName(), EnumValue.AUDI.getId());
        builder.attribute(CategoryConstants.Attribute.PROPERTY_TYPE.getName(), EnumValue.HOUSE.getId());
        Map<String, Object> attributes = builder.build().getAttribute();
        assertThat(attributes,
                hasEntry(equalTo(CategoryConstants.Attribute.VEHICLE_MAKE.getName()), equalTo((Object) EnumValue.AUDI.getId())));
        assertThat(attributes,
                hasEntry(equalTo(CategoryConstants.Attribute.PROPERTY_TYPE.getName()), equalTo((Object) EnumValue.HOUSE.getId())));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfAttributeNameIsNull() {
        builder.attribute(null, "value");
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfAttributeValueIsNull() {
        builder.attribute(CategoryConstants.Attribute.PROPERTY_TYPE.getName(), null);
    }

    @Test
    public void shouldFormatPriceCorrectlyFormatOneZero() {
        // given
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.PRICE.getName()))
                .thenReturn(Optional.of(AttributeType.CURRENCY));

        // when
        builder.attribute(CategoryConstants.Attribute.PRICE.getName(), "1200.0");

        // then
        Map<String, Object> attributes = builder.build().getAttribute();
        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes,
                hasEntry(equalTo(CategoryConstants.Attribute.PRICE.getName()), equalTo((Object) 1200L)));
    }

    @Test
    public void shouldFormatPriceCorrectlyFormatTwoZero() {
        // given
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.PRICE.getName()))
                .thenReturn(Optional.of(AttributeType.CURRENCY));

        // when
        builder.attribute(CategoryConstants.Attribute.PRICE.getName(), "1200.00");

        // then
        Map<String, Object> attributes = builder.build().getAttribute();
        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes,
                hasEntry(equalTo(CategoryConstants.Attribute.PRICE.getName()), equalTo((Object) 1200L)));
    }

    @Test
    public void shouldFormatPriceCorrectlyFormatAnyNumber() {
        // given
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.PRICE.getName()))
                .thenReturn(Optional.of(AttributeType.CURRENCY));

        // when
        builder.attribute(CategoryConstants.Attribute.PRICE.getName(), "1200.2");

        // then
        Map<String, Object> attributes = builder.build().getAttribute();
        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes,
                hasEntry(equalTo(CategoryConstants.Attribute.PRICE.getName()), equalTo((Object) 1200L)));
    }

    @Test
    public void shouldAcceptABunchOfImages() {
        builder.image("http://images.com/1", true);
        builder.image("http://images.com/2", false);
        builder.image("http://images.com/3", false);
        builder.image("http://images.com/4", false);
        FlatAd flatAd = builder.build();
        assertThat(flatAd.getPrimaryImageUrl(), equalTo("http://images.com/1"));
        assertThat(flatAd.getAdditionalImageUrls(), contains(
                "http://images.com/2",
                "http://images.com/3",
                "http://images.com/4"));
    }

    @Test(expected = IllegalStateException.class)
    public void shouldThrowExceptionIfAdditionalImagesAreSetWithoutAPrimary() {
        builder.image("http://images.com/2", false);
        builder.image("http://images.com/3", false);
        builder.image("http://images.com/4", false);
        FlatAd flatAd = builder.build();
    }

    @Test
    public void shouldReorganiseImagesCorrectlyIfPrimaryIsChanged() {
        builder.image("http://images.com/1", true);
        builder.image("http://images.com/2", false);
        builder.image("http://images.com/3", true);
        FlatAd flatAd = builder.build();
        assertThat(flatAd.getPrimaryImageUrl(), equalTo("http://images.com/3"));
        assertThat(flatAd.getAdditionalImageUrls(), contains(
                "http://images.com/1",
                "http://images.com/2"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfImageUrlIsNull() {
        builder.image(null, true);
    }

    @Test
    public void shouldSetYouTubeLinkCorrectly() {
        builder.youtubeUrl("http://www.youtube.com/test");
        assertThat(builder.build().getYoutubeUrl(), equalTo("http://www.youtube.com/test"));
    }

    @Test
    public void shouldAcceptANullYouTubeLink() {
        builder.youtubeUrl(null);
        assertThat(builder.build().getYoutubeUrl(), nullValue());
    }

    @Test
    public void shouldSetPaidForCorrectlyWhenTrue() {
        builder.paidFor(true);
        assertThat(builder.build().isPaidFor(), equalTo(true));
    }

    @Test
    public void shouldSetPaidForCorrectlyWhenFalse() {
        builder.paidFor(false);
        assertThat(builder.build().isPaidFor(), equalTo(false));
    }

    @Test
    public void shouldSetPaidForCorrectlyWhenNull() {
        builder.paidFor(null);
        assertThat(builder.build().isPaidFor(), equalTo(false));
    }

    @Test
    public void paidForShouldBeFalseIfNotDefined() {
        assertThat(builder.build().isPaidFor(), equalTo(false));
    }

    @Test
    public void shouldSetCreatedByUserCorrectly() {
        builder.createdBy(5L, "<EMAIL>", "Bob", "Bobbinson", "1235", "12346", new DateTime(1000000000));
        assertThat(builder.build().getCreatedBy(), equalTo(aUser()
                .withId(5L)
                .createdOn(new DateTime(1000000000))
                .withEmailAddress("<EMAIL>")
                .withForename("Bob")
                .withSurname("Bobbinson")
                .withContactTelephone("1235")
                .withVerifiedPhoneNumber("12346").build()
        ));
    }

    @Test
    public void shouldSetLastModifiedByUserCorrectly() {
        builder.lastModifiedBy(5L, "<EMAIL>", "Bob", "Bobbinson", "1235", "12346", new DateTime(1000000000));
        assertThat(builder.build().getLastModifiedBy(), equalTo(aUser()
                .withId(5L)
                .createdOn(new DateTime(1000000000))
                .withEmailAddress("<EMAIL>")
                .withForename("Bob")
                .withSurname("Bobbinson")
                .withContactTelephone("1235")
                .withVerifiedPhoneNumber("12346").build()
        ));
    }

    @Test
    public void shouldSetBumpUpCountCorrectly() {
        builder.bumpUpCount(10);
        assertThat(builder.build().getBumpUpCount(), equalTo(10));
    }

    @Test
    public void shouldSetBumpUpCountCorrectlyWhenNull() {
        builder.bumpUpCount(null);
        assertThat(builder.build().getBumpUpCount(), equalTo(0));
    }

    @Test
    public void bumpUpCountShouldBe0IfNotDefined() {
        assertThat(builder.build().getBumpUpCount(), equalTo(0));
    }

    @Test
    public void shouldSetIpAddressCorrectly() {
        builder.ipAddress("***********");
        assertThat(builder.build().getIpAddress(), equalTo("***********"));
    }

    @Test
    public void shouldAcceptANullIpAddress() {
        builder.ipAddress(null);
        assertThat(builder.build().getIpAddress(), nullValue());
    }

    @Test
    public void shouldSetRemoteHostCorrectly() {
        builder.remoteHost("localhost.com");
        assertThat(builder.build().getRemoteHost(), equalTo("localhost.com"));
    }

    @Test
    public void shouldAcceptANullRemoteHost() {
        builder.remoteHost(null);
        assertThat(builder.build().getRemoteHost(), nullValue());
    }

    @Test
    public void shouldSetCookieCorrectly() {
        builder.cookie("342342353463456tydfgdfgfd");
        assertThat(builder.build().getCookie(), equalTo("342342353463456tydfgdfgfd"));
    }

    @Test
    public void shouldAcceptANullCookie() {
        builder.cookie(null);
        assertThat(builder.build().getCookie(), nullValue());
    }

    @Test
    public void shouldSetExternalReferenceCorrectly() {
        builder.externalReference("342342353463456tydfgdfgfd");
        assertThat(builder.build().getExternalReference(), equalTo("342342353463456tydfgdfgfd"));
    }

    @Test
    public void shouldAcceptANullExternalReference() {
        builder.cookie(null);
        assertThat(builder.build().getExternalReference(), nullValue());
    }

    @Test
    public void setsPublicWebsiteUrlCorrectly() {
        Long categoryId = 10L;
        Category category = TestCategory.category(categoryId, "cat10", "Cat 10");
        List<Category> hierarchy = Arrays.asList(
                TestCategory.category(1L, "cat1", "Cat 1"),
                TestCategory.category(5L, "cat5", "Cat 5"),
                category);

        when(categoryModel.getCategory(categoryId)).thenReturn(Optional.of(category));
        when(categoryModel.getFullPath(categoryId)).thenReturn(hierarchy);
        when(urlScheme.urlForAdvert(1L, "my title", categoryId)).thenReturn("http://www.gumtree.com/cars/mytitle/1");

        builder.id(1L).title("my title").categoryId(categoryId);

        assertThat(builder.build().getPublicWebsiteUrl(), equalTo("http://www.gumtree.com/cars/mytitle/1"));
    }

    @Test
    public void shouldSetFeaturesCorrectly() {
        builder.expirableFeature(ProductName.FEATURE_14_DAY, new DateTime(100000000000L));
        builder.expirableFeature(ProductName.FEATURE_7_DAY, new DateTime(100000000001L));
        builder.urlFeature("http://www.features.com/url1");
        builder.urlFeature("http://www.features.com/url2");
        Set<Feature> features = builder.build().getFeatures();
        assertThat(features, containsInAnyOrder(
                (Feature) anExpirableFeature()
                        .forProduct(ProductName.FEATURE_14_DAY.name())
                        .withExpiryDate(new DateTime(100000000000L)).build(),
                (Feature) anExpirableFeature()
                        .forProduct(ProductName.FEATURE_7_DAY.name())
                        .withExpiryDate(new DateTime(100000000001L)).build(),
                (Feature) aUrlFeature()
                        .forProduct(ProductName.WEBSITE_URL.name())
                        .withUrl("http://www.features.com/url1").build(),
                (Feature) aUrlFeature()
                        .forProduct(ProductName.WEBSITE_URL.name())
                        .withUrl("http://www.features.com/url2").build()
        ));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfExpirableFeatureProductIsNull() {
        builder.expirableFeature(null, new DateTime(100000000000L));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfExpirableFeatureExpiryDateIsNull() {
        builder.expirableFeature(ProductName.FEATURE_14_DAY, null);
    }

    @Test
    public void shouldAcceptANullUrlFeatureUrl() {
        builder.urlFeature(null);
        assertThat(builder.build().getFeatures(), nullValue());
    }

    @Test
    public void shouldAcceptANullThreatmetrixSessionId() {
        builder.threatmetrixSessionId(null);
        assertThat(builder.build().getThreatmetrixSessionId(), nullValue());
    }

    @Test
    public void shouldAcceptAThreatmetrixSessionId() {
        String threatmetrixSessionId = "abc123";
        builder.threatmetrixSessionId(threatmetrixSessionId);
        assertThat(builder.build().getThreatmetrixSessionId(), equalTo(threatmetrixSessionId));
    }

    @Test
    public void shouldSetDeleteReason() {
        builder.deleteReason(DeleteReason.GT_SUCCESS_YES);
        assertEquals("GT_SUCCESS_YES", builder.build().getDeleteReason());
    }

    @Test
    public void shouldSetDeleteReasonNullIfNull() {
        builder.deleteReason(null);
        assertNull(builder.build().getDeleteReason());
    }
}
