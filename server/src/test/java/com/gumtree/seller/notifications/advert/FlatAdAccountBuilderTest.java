package com.gumtree.seller.notifications.advert;

import com.gumtree.api.converter.search.FlatAdAttributeExpander;
import com.gumtree.sapi.spec.domain.Account;
import com.gumtree.seller.domain.account.type.AccountReplyType;
import com.gumtree.seller.email.EmailUrlScheme;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class FlatAdAccountBuilderTest {
    @Mock
    private EmailUrlScheme urlScheme;
    @Mock
    private FlatAdAttributeExpander flatAdAttributeExpander;

    private FlatAdBuilder builder;

    private static final Long TEST_ID = 10L;
    private static final String TEST_PUBLIC_ID = "6aa7808933f96f44f";
    private static final Long TEST_DATE_MS = 1000000000L;
    private static final String TEST_LOGO = "http://website.com/image.jpg";
    private static final String TEST_VAT_NUMBER = "la di da";

    @Before
    public void init() {
        builder = new FlatAdBuilder(null, null, null, urlScheme, flatAdAttributeExpander);
    }

    private Account.Builder expectedAccount() {
        return Account.anAccount()
                .withId(TEST_ID)
                .publicId(TEST_PUBLIC_ID)
                .postingSince(new DateTime(TEST_DATE_MS))
                .isPro(false);
    }

    private Account.Builder expectedProAccount() {
        return expectedAccount().isPro(true);
    }

    @Test
    public void shouldSetAccountIdCorrectly() {
        builder.account(TEST_ID);
        assertEquals(builder.build().getAccountId(), TEST_ID);
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionIfAccountIdIsNull() {
        builder.account(null);
    }

    @Test
    public void shouldSetProAccountCorrectlyWithDate() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, new Date(TEST_DATE_MS), true, null, null, null);
        assertEquals(builder.build().getAccount(), expectedProAccount().build());
    }

    @Test
    public void shouldSetNonProAccountCorrectlyWithDate() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, new Date(TEST_DATE_MS), false, null, null, null);
        assertEquals(builder.build().getAccount(), expectedAccount().build());
    }

    @Test
    public void shouldSetProAccountCorrectlyWithDateTime() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, new DateTime(TEST_DATE_MS), true, null, null, null);
        assertEquals(builder.build().getAccount(), expectedProAccount().build());
    }

    @Test
    public void shouldSetNonProAccountCorrectlyWithDateTime() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, new DateTime(TEST_DATE_MS), false, null, null, null);
        assertEquals(builder.build().getAccount(), expectedAccount().build());
    }

    @Test
    public void shouldSupportNullPostingSinceDateForAccount() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, (Date) null, true, null, null, null);
        assertEquals(builder.build().getAccount(), expectedProAccount()
                .postingSince(null)
                .build()
        );
    }

    @Test
    public void shouldSupportNullPostingSinceDateTimeForAccount() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, (DateTime) null, true, null, null, null);
        assertEquals(builder.build().getAccount(), expectedProAccount()
                .postingSince(null)
                .build()
        );
    }

    @Test
    public void shouldSetProAccountCorrectlyWithLogoUrl() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, (Date) null, true, null, TEST_LOGO, null);
        assertEquals(builder.build().getAccount(), expectedProAccount()
                .postingSince(null)
                .logoUrl(TEST_LOGO)
                .build()
        );
    }

    @Test
    public void shouldSetNonProAccountCorrectlyWithLogoUrl() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, (Date) null, false, null, TEST_LOGO, null);
        assertEquals(builder.build().getAccount(),  expectedAccount()
                .postingSince(null)
                .logoUrl(TEST_LOGO)
                .build()
        );
    }

    @Test
    public void shouldSetAccountWithVatNumber() {
        builder.account(TEST_ID, TEST_PUBLIC_ID ,new Date(TEST_DATE_MS), true, TEST_VAT_NUMBER, null, null);
        assertEquals(builder.build().getAccount(), expectedProAccount()
                .vatNumber(TEST_VAT_NUMBER)
                .build()
        );
    }

    @Test
    public void shouldSetAccountWithReplyType() {
        AccountReplyType trifecta = AccountReplyType.TRIFECTA;
        builder.account(TEST_ID, TEST_PUBLIC_ID, new Date(TEST_DATE_MS), true, null, null, trifecta);
        assertEquals(builder.build().getAccount(), expectedProAccount()
                .replyType(trifecta.name())
                .build()
        );
    }

    @Test
    public void shoudSetAccountWithPublicId() {
        builder.account(TEST_ID, TEST_PUBLIC_ID, (Date) null, false, null, null, null);
        assertEquals(builder.build().getAccount(),  expectedAccount()
                .postingSince(null)
                .publicId(TEST_PUBLIC_ID)
                .build()
        );
    }
}
