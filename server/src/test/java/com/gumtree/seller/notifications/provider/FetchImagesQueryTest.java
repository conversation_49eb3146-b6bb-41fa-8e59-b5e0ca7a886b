package com.gumtree.seller.notifications.provider;

import com.google.common.collect.Lists;
import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.test.DataSetLocation;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcOperations;

import java.util.List;
import java.util.Map;

import static org.fest.assertions.api.Assertions.assertThat;


@DataSetLocation(value = "classpath:/com/gumtree/seller/notification/fetch-images-dataset.xml", dataSourceName = "sellerDatasource")
public class FetchImagesQueryTest extends BaseRepositoryTest {

    @Autowired
    private JdbcOperations jdbcOperations;

    @Test
    public void shouldFetchAllAdvertsAndPopulateEpsOrCFUrl() {
        // given
        List<Long> advertIds = Lists.newArrayList(1L, 2L, 3L, 4L);

        // when
        Map<Long, ImageRow> result = new FetchImagesQuery().advertIds(advertIds).executeQuery(jdbcOperations);

        // then
        // image table only
        assertThat(result).hasSize(4);
        assertThat(result.get(1L).getMainImage().getUrl()).isEqualTo("https://i.ebayimg.com/00/s/1/$_86.PNG");
        assertThat(result.get(1L).getMainImage().getPosition()).isEqualTo(0);
        assertThat(result.get(1L).getAdditionalImages()).isEmpty();

        // both image & media contain urls
        assertThat(result.get(2L).getMainImage().getUrl()).isEqualTo("https://imagedelivery.net/abc/2/1");
        assertThat(result.get(2L).getMainImage().getPosition()).isEqualTo(0);
        assertThat(result.get(2L).getAdditionalImages()).isEmpty();

        // image only
        assertThat(result.get(3L).getMainImage().getUrl()).isEqualTo("https://i.ebayimg.com/00/s/3/$_86.PNG");
        assertThat(result.get(3L).getMainImage().getPosition()).isEqualTo(0);
        assertThat(result.get(3L).getAdditionalImages()).isEmpty();

        // media only
        assertThat(result.get(4L).getMainImage().getUrl()).isEqualTo("https://imagedelivery.net/abc/4/1");
        assertThat(result.get(4L).getMainImage().getPosition()).isEqualTo(0);

        List<OrderableImage> additionalImages = Lists.newArrayList(result.get(4L).getAdditionalImages());
        assertThat(additionalImages).hasSize(1);
        OrderableImage additional = additionalImages.get(0);
        assertThat(additional.getUrl()).isEqualTo("https://i.ebayimg.com/00/s/5/$_86.PNG");
        assertThat(additional.getPosition()).isEqualTo(1);
    }
}
