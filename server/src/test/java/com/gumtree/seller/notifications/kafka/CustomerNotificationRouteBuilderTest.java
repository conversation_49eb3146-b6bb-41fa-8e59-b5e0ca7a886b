package com.gumtree.seller.notifications.kafka;

import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.notification.CustomerNotification;
import com.gumtree.notification.EntryProperty;
import org.apache.camel.EndpointInject;
import org.apache.camel.Produce;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.test.junit4.CamelTestSupport;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;


public class CustomerNotificationRouteBuilderTest extends CamelTestSupport {
    private static final Clock CLOCK = new StoppedClock();
    private static final String NOTIFICATION_DESTINATION_ENDPOINT = "mock:realtime-output";

    @EndpointInject(uri = NOTIFICATION_DESTINATION_ENDPOINT)
    protected MockEndpoint customerDestinationEndpoint;

    @Produce(uri = CustomerNotificationRouteBuilder.SOURCE_ENDPOINT)
    protected ProducerTemplate producer;

    @Override
    protected RouteBuilder createRouteBuilder() {
        return new CustomerNotificationRouteBuilder(NOTIFICATION_DESTINATION_ENDPOINT);
    }

    @Test
    public void shouldSendFavouriteEventToDestinationURI() throws InterruptedException {
        CustomerNotification customerNotification = new CustomerNotification();
        customerNotification.setUserId(123L);
        customerNotification.setTriggerEventName("test");
        customerNotification.setTriggerTime(CLOCK.now());
        List<EntryProperty> entryProperties = new ArrayList<>();
        entryProperties.add(new EntryProperty("firstName", "cat"));

        customerNotification.setEntryProperties(entryProperties);

        //expect
        customerDestinationEndpoint.expectedBodiesReceived(customerNotification);

        // when
        producer.sendBody(customerNotification);
        // then
        customerDestinationEndpoint.assertIsSatisfied();
    }
}
