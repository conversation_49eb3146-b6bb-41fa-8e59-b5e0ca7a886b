package com.gumtree.seller.notifications.kafka;

import com.google.common.collect.Lists;
import com.gumtree.common.util.time.Clock;
import com.gumtree.config.search.AdSearchIndexFeeder;
import com.gumtree.sapi.spec.domain.FlatAd;
import com.gumtree.seller.notifications.NotificationStatus;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class KafkaAdvertNotificationCallbackTest {
    private KafkaAdvertNotificationCallback callback;

    private AdSearchIndexFeeder indexFeeder;
    private NotificationStatus notificationStatus;
    private Clock clock;

    @Before
    public void beforeEach() {
        indexFeeder = mock(AdSearchIndexFeeder.class);
        notificationStatus = mock(NotificationStatus.class);
        clock = mock(Clock.class);

        callback = new KafkaAdvertNotificationCallback(indexFeeder, AdSearchIndexFeeder.Trigger.REINDEX, clock);
        when(clock.now()).thenReturn(System.currentTimeMillis());
    }

    @Test
    public void shouldCallKafkaFeeder() {
        ArrayList<FlatAd> adverts = Lists.newArrayList(FlatAd.aFlatAd().withId(88L).build(), FlatAd.aFlatAd().withId(1998L).build());

        // when
        callback.onBatch(adverts, notificationStatus);

        // then
        verify(indexFeeder).send(new AdSearchIndexFeeder.FeedEvent(adverts.get(0), AdSearchIndexFeeder.Trigger.REINDEX, clock.now()));
        verify(indexFeeder).send(new AdSearchIndexFeeder.FeedEvent(adverts.get(1), AdSearchIndexFeeder.Trigger.REINDEX, clock.now()));

        // and
        verify(notificationStatus).incrementCompletedCount(2);
        verify(notificationStatus).setLastAdvertId(1998L);
    }

}