package com.gumtree.seller.notifications.provider;

import org.junit.Test;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Created with IntelliJ IDEA. User: da<PERSON><PERSON> Date: 20/08/2013 Time: 12:11 To change this template use File |
 * Settings | File Templates.
 */
public class FetchLocationsQueryTest {

    @Test
    public void shouldUsePostcodeLongAndLatIfAvailable() throws SQLException {
        //given
        FetchLocationsQuery.LocationsMap locationsMap = new FetchLocationsQuery.LocationsMap();
        ResultSet resultSet = mock(ResultSet.class);
        when(resultSet.getLong(1)).thenReturn(100000000L);
        when(resultSet.getBigDecimal(4)).thenReturn(new BigDecimal(50.1));
        when(resultSet.getBigDecimal(5)).thenReturn(new BigDecimal(-1.01));

        //when
        locationsMap.processRow(resultSet);

        //then
        assertThat(locationsMap.get(100000000L).getLatitude()).isEqualTo(new BigDecimal(50.1));
        assertThat(locationsMap.get(100000000L).getLongitude()).isEqualTo(new BigDecimal(-1.01));
    }

    @Test
    public void shouldUseLocationLongAndLatIfPostcodeNotAvailable() throws SQLException {
        //given
        FetchLocationsQuery.LocationsMap locationsMap = new FetchLocationsQuery.LocationsMap();
        ResultSet resultSet = mock(ResultSet.class);
        when(resultSet.getLong(1)).thenReturn(100000001L);
        when(resultSet.getBigDecimal(9)).thenReturn(new BigDecimal(50.2));
        when(resultSet.getBigDecimal(10)).thenReturn(new BigDecimal(-1.02));

        //when
        locationsMap.processRow(resultSet);

        //then
        assertThat(locationsMap.get(100000001L).getLatitude()).isEqualTo(new BigDecimal(50.2));
        assertThat(locationsMap.get(100000001L).getLongitude()).isEqualTo(new BigDecimal(-1.02));
    }
}
