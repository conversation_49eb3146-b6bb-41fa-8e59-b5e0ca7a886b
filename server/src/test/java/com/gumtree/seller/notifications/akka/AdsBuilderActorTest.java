package com.gumtree.seller.notifications.akka;

import akka.actor.*;

import akka.testkit.TestKit;
import akka.testkit.TestProbe;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.converter.search.FlatAdAttributeExpander;
import com.gumtree.sapi.spec.domain.FlatAd;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.email.EmailUrlScheme;
import com.gumtree.seller.notifications.advert.AdvertBuilder;
import com.gumtree.seller.notifications.advert.FlatAdBuilder;
import com.gumtree.seller.notifications.provider.FeatureRow;
import com.gumtree.seller.notifications.provider.ImageRow;
import com.gumtree.seller.notifications.provider.LocationRow;
import com.gumtree.seller.service.advert.AdvertLocationResolver;
import com.gumtree.seller.service.location.LocationModel;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AdsBuilderActorTest extends TestKit {

    static ActorSystem system;

    private ActorRef testActorRef;
    private TestProbe testProbe;

    @Mock
    private ActorRef dbWorkerActorsMock;

    @Mock
    private DbQueryResultFactory<Serializable> dbResultFactoryMock;
    @Mock
    private AdvertLocationResolver advertLocationResolverMock;
    @Mock
    private LocationModel locationModelMock;
    @Mock
    private CategoryModel categoryModelMock;
    @Mock
    private EmailUrlScheme emailUrlSchemeMock;
    @Mock
    private FlatAdAttributeExpander flatAdAttributeExpanderMock;

    public AdsBuilderActorTest() {
        super(system);
    }


    @BeforeClass
    public static void setupClass() {
        system = ActorSystem.create("testSystem");
    }

    @Before
    public void setUp() {
        testProbe = new TestProbe(system);
        Props props = new Props((UntypedActorFactory) () -> new AdsBuilderActor(dbWorkerActorsMock, dbResultFactoryMock));
        testActorRef = system.actorOf(props);
    }

    @AfterClass
    public static void tearDownClass() {
        if (system != null) {
            system.shutdown();
            system = null;
        }
    }


    @Test
    public void onReceive_allChildActorsCompletedAndAdvertsHaveImages_returnAdverts() {
        //Given
        Set<Long> advertIds = new HashSet<>(Arrays.asList(1L, 2L));
        Map<Long, AdvertBuilder<FlatAd>> adsBuilders = createAdsBuilder(advertIds);
        AdsBuildersMessage adsBuildersMessage = createAdsBuildersMessage(adsBuilders);
        AdsDetailsMessage adsDetailsMessage = createAdsDetailsMessage(adsBuilders);
        AdsAttributesMessage adsAttributesMessage = createAdsAttributesMessage(advertIds);
        AdsImagesMessage adsImagesMessage = createAdsImagesMessage(advertIds);
        AdsLocationsMessage adsLocationsMessage = createAdsLocationsMessage(advertIds);
        AdsFeaturesMessage adsFeaturesMessage = createAdsFeaturesMessage(advertIds);

        //When
        sendMessagesToActor(adsBuildersMessage, adsDetailsMessage, adsAttributesMessage, adsImagesMessage, adsLocationsMessage, adsFeaturesMessage);

        //Then
        AdvertsMessage advertsMessage = testProbe.expectMsgClass(AdvertsMessage.class);
        assertThat(advertsMessage.getAdverts().size(), equalTo(2));
    }

    @Test
    public void onReceive_allChildActorsCompletedAndOneAdvertDoesNotHaveMainImageButHasAdditionalImages_advertIsNotPresent() {
        //Given
        Set<Long> advertIds = new HashSet<>(Arrays.asList(1L, 2L));
        Map<Long, AdvertBuilder<FlatAd>> adsBuilders = createAdsBuilder(advertIds);

        AdsBuildersMessage adsBuildersMessage = createAdsBuildersMessage(adsBuilders);
        AdsDetailsMessage adsDetailsMessage = createAdsDetailsMessage(adsBuilders);
        AdsAttributesMessage adsAttributesMessage = createAdsAttributesMessage(advertIds);
        AdsImagesMessage adsImagesMessage = createIncompleteAdsImagesMessage();
        AdsLocationsMessage adsLocationsMessage = createAdsLocationsMessage(advertIds);
        AdsFeaturesMessage adsFeaturesMessage = createAdsFeaturesMessage(advertIds);

        //When
        sendMessagesToActor(adsBuildersMessage, adsDetailsMessage, adsAttributesMessage, adsImagesMessage, adsLocationsMessage, adsFeaturesMessage);

        //Then
        AdvertsMessage advertsMessage = testProbe.expectMsgClass(AdvertsMessage.class);
        assertThat(advertsMessage.getAdverts().size(), equalTo(1));
    }

    @Test
    public void onReceive_allChildActorsCompletedAndOneAdvertHasMainImageButDoesNotHaveAdditionalImages_returnAdverts() {
        //Given
        Set<Long> advertIds = new HashSet<>(Arrays.asList(1L, 2L));
        Map<Long, AdvertBuilder<FlatAd>> adsBuilders = createAdsBuilder(advertIds);

        AdsBuildersMessage adsBuildersMessage = createAdsBuildersMessage(adsBuilders);
        AdsDetailsMessage adsDetailsMessage = createAdsDetailsMessage(adsBuilders);
        AdsAttributesMessage adsAttributesMessage = createAdsAttributesMessage(advertIds);
        AdsImagesMessage adsImagesMessage = createPartialAdsImagesMessage();
        AdsLocationsMessage adsLocationsMessage = createAdsLocationsMessage(advertIds);
        AdsFeaturesMessage adsFeaturesMessage = createAdsFeaturesMessage(advertIds);

        //When
        sendMessagesToActor(adsBuildersMessage, adsDetailsMessage, adsAttributesMessage, adsImagesMessage, adsLocationsMessage, adsFeaturesMessage);

        //Then
        AdvertsMessage advertsMessage = testProbe.expectMsgClass(AdvertsMessage.class);
        assertThat(advertsMessage.getAdverts().size(), equalTo(2));
    }

    @Test
    public void onReceive_allChildActorsCompletedAndOneAdvertDoesNotHaveImages_returnAdverts() {
        //Given
        Set<Long> advertIds = new HashSet<>(Arrays.asList(1L, 2L, 3L));
        Map<Long, AdvertBuilder<FlatAd>> adsBuilders = createAdsBuilder(advertIds);

        AdsBuildersMessage adsBuildersMessage = createAdsBuildersMessage(adsBuilders);
        AdsDetailsMessage adsDetailsMessage = createAdsDetailsMessage(adsBuilders);
        AdsAttributesMessage adsAttributesMessage = createAdsAttributesMessage(advertIds);
        AdsImagesMessage adsImagesMessage = createEmptyAdsImagesMessage();
        AdsLocationsMessage adsLocationsMessage = createAdsLocationsMessage(advertIds);
        AdsFeaturesMessage adsFeaturesMessage = createAdsFeaturesMessage(advertIds);

        //When
        sendMessagesToActor(adsBuildersMessage, adsDetailsMessage, adsAttributesMessage, adsImagesMessage, adsLocationsMessage, adsFeaturesMessage);

        //Then
        AdvertsMessage advertsMessage = testProbe.expectMsgClass(AdvertsMessage.class);
        assertThat(advertsMessage.getAdverts().size(), equalTo(3));
    }

    private void sendMessagesToActor(Object... messages) {
        for (Object message : messages) {
            testActorRef.tell(message, testProbe.ref());
        }
    }

    private Map<Long, AdvertBuilder<FlatAd>> createAdsBuilder(Set<Long> advertIds) {
        AdvertBuilder<FlatAd> flatAdAdvertBuilder = createFlatAdvertBuilder();
        Map<Long, AdvertBuilder<FlatAd>> adsBuilders = new HashMap<>();
        for (Long advertId : advertIds) {
            adsBuilders.put(advertId, flatAdAdvertBuilder);
        }
        return adsBuilders;
    }

    private AdvertBuilder<FlatAd> createFlatAdvertBuilder() {
        when(advertLocationResolverMock.getHierarchicalLocationIdsFromLeaf(anyLong())).thenReturn(new ArrayList<>());
        return new FlatAdBuilder(advertLocationResolverMock, locationModelMock, categoryModelMock, emailUrlSchemeMock, flatAdAttributeExpanderMock);
    }

    private AdsBuildersMessage createAdsBuildersMessage(Map<Long, AdvertBuilder<FlatAd>> adsBuilders) {
        return new AdsBuildersMessage(adsBuilders);
    }

    private AdsFeaturesMessage createAdsFeaturesMessage(Set<Long> advertIds) {
        Map<Long, Set<FeatureRow>> adsFeatures = new HashMap<>();
        for (Long advertId : advertIds) {
            FeatureRow featureRow1 = new FeatureRow();
            featureRow1.setProductName(ProductName.INSERTION);
            FeatureRow featureRow2 = new FeatureRow();
            featureRow2.setProductName(ProductName.FEATURE_7_DAY);
            Set<FeatureRow> featureRows = new HashSet<>();
            featureRows.add(featureRow1);
            featureRows.add(featureRow2);

            adsFeatures.put(advertId, featureRows);
        }
        return new AdsFeaturesMessage(adsFeatures, 2L);
    }

    private AdsLocationsMessage createAdsLocationsMessage(Set<Long> advertIds) {
        Map<Long, LocationRow> adsLocations = new HashMap<>();
        for (Long advertId : advertIds) {
            LocationRow locationRow = new LocationRow();
            locationRow.addLocationId(1L);
            locationRow.setLatitude(new BigDecimal(1L));
            locationRow.setLongitude(new BigDecimal(2L));
            locationRow.setOutcode("n4");
            adsLocations.put(advertId, locationRow);
        }
        return new AdsLocationsMessage(adsLocations, 2L);
    }

    private AdsAttributesMessage createAdsAttributesMessage(Set<Long> advertIds) {
        Map<Long, Map<String, String>> adAttributes = new HashMap<>();
        for (Long advertId : advertIds) {
            Map<String, String> attributes = new HashMap<>();
            attributes.put("attr1", "valueAttr1");
            attributes.put("attr2", "valueAttr2");
            attributes.put("attr3", "valueAttr3");
            adAttributes.put(advertId, attributes);
        }
        return new AdsAttributesMessage(adAttributes, 2L);
    }

    private AdsDetailsMessage createAdsDetailsMessage(Map<Long, AdvertBuilder<FlatAd>> adsBuilders) {
        return new AdsDetailsMessage(adsBuilders, 2L);
    }

    private AdsImagesMessage createAdsImagesMessage(Set<Long> advertIds) {
        Map<Long, ImageRow> adImages = new HashMap<>();
        for (Long advertId : advertIds) {
            ImageRow imageRow1 = new ImageRow();
            imageRow1.addImage("http://mainimage1", 1, true);
            imageRow1.addImage("http://addmage1", 2, false);
            imageRow1.addImage("http://addmage2", 3, false);
            imageRow1.addImage("http://addmage3", 4, false);
            adImages.put(advertId, imageRow1);
        }
        return new AdsImagesMessage(adImages, 2L);
    }

    private AdsImagesMessage createIncompleteAdsImagesMessage() {
        Map<Long, ImageRow> adImages = new HashMap<>();
        ImageRow imageRow1 = new ImageRow();
        imageRow1.addImage("http://addmage1", 2, false);
        imageRow1.addImage("http://addmage2", 3, false);
        imageRow1.addImage("http://addmage3", 4, false);
        ImageRow imageRow2 = new ImageRow();
        imageRow1.addImage("http://mainimage2", 1, true);
        imageRow2.addImage("http://addmage1", 2, false);
        imageRow2.addImage("http://addmage2", 3, false);
        imageRow2.addImage("http://addmage3", 4, false);
        adImages.put(1L, imageRow1);
        adImages.put(2L, imageRow2);
        return new AdsImagesMessage(adImages, 2L);
    }

    private AdsImagesMessage createPartialAdsImagesMessage() {
        Map<Long, ImageRow> adImages = new HashMap<>();
        ImageRow imageRow1 = new ImageRow();
        imageRow1.addImage("http://mainimage1", 1, true);
        imageRow1.addImage("http://addmage1", 2, false);
        imageRow1.addImage("http://addmage2", 3, false);
        imageRow1.addImage("http://addmage3", 4, false);
        ImageRow imageRow2 = new ImageRow();
        imageRow2.addImage("http://mainimage2", 1, true);
        adImages.put(1L, imageRow1);
        adImages.put(2L, imageRow2);
        return new AdsImagesMessage(adImages, 2L);
    }

    private AdsImagesMessage createEmptyAdsImagesMessage() {
        Map<Long, ImageRow> adImages = new HashMap<>();
        ImageRow imageRow1 = new ImageRow();
        imageRow1.addImage("http://mainimage1", 1, true);
        imageRow1.addImage("http://addmage1", 2, false);
        imageRow1.addImage("http://addmage2", 3, false);
        imageRow1.addImage("http://addmage3", 4, false);
        adImages.put(2L, imageRow1);
        adImages.put(3L, new ImageRow());
        return new AdsImagesMessage(adImages, 2L);
    }
}
