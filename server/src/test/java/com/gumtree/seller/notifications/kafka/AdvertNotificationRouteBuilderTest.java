package com.gumtree.seller.notifications.kafka;

import com.gumtree.api.converter.advert.AdvertToAdLifecycleConverter;
import com.gumtree.seller.service.advert.impl.AdvertLocationResolverFactory;
import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.config.search.AdSearchIndexFeeder;
import com.gumtree.sapi.spec.domain.FlatAd;
import com.gumtree.sapi.spec.domain.User;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import io.micrometer.common.util.StringUtils;
import org.apache.camel.EndpointInject;
import org.apache.camel.Produce;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.test.junit4.CamelTestSupport;
import org.joda.time.DateTime;
import org.json.JSONObject;
import org.junit.Test;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.regex.Pattern;

import static com.gumtree.config.search.AdSearchIndexFeeder.Trigger.AD_CHANGE;
import static com.gumtree.config.search.AdSearchIndexFeeder.Trigger.REINDEX;
import static com.gumtree.config.search.AdSearchIndexFeeder.Trigger.DELETE;
import static com.gumtree.config.search.AdSearchIndexFeeder.Trigger.FULLAD_REINDEX;
import static com.gumtree.sapi.spec.domain.FlatAd.aFlatAd;
import static org.hamcrest.Matchers.is;

public class AdvertNotificationRouteBuilderTest extends CamelTestSupport {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdvertNotificationRouteBuilderTest.class);

    private static final String REALTIME_DESTINATION = "mock:realtime-output";
    private static final String REINDEX_DESTINATION = "mock:reindex-output";
    private static final String FULLAD_REINDEX_DESTINATION = "mock:fullad-reindex-output";
    private static final String INTERNAL_REALTIME_DESTINATION = "mock:internal-realtime-output";

    private static final Clock CLOCK = new StoppedClock();

    @Mock
    private AdvertLocationResolverFactory advertLocationResolverFactory;

    @EndpointInject(uri = REALTIME_DESTINATION)
    protected MockEndpoint realTimeResultEndpoint;

    @EndpointInject(uri = REINDEX_DESTINATION)
    protected MockEndpoint reindexResultEndpoint;

    @EndpointInject(uri = INTERNAL_REALTIME_DESTINATION)
    protected MockEndpoint internalRealTimeResultEndpoint;

    @EndpointInject(uri = FULLAD_REINDEX_DESTINATION)
    protected MockEndpoint fullAdReindexDestinationUri;

    @Produce(uri = AdvertNotificationRouteBuilder.SOURCE_ENDPOINT)
    protected ProducerTemplate producer;

    @Override
    protected RouteBuilder createRouteBuilder() {
        return new AdvertNotificationRouteBuilder(REALTIME_DESTINATION, REINDEX_DESTINATION, FULLAD_REINDEX_DESTINATION,
                INTERNAL_REALTIME_DESTINATION, new AdvertToAdLifecycleConverter(advertLocationResolverFactory), CLOCK);
    }

    @Test
    public void shouldSendReindexEventToReindexDestinationURI() throws InterruptedException {
        AdSearchIndexFeeder.FeedEvent event = new AdSearchIndexFeeder.FeedEvent(aFlatAd().withId(88L).build(), REINDEX, CLOCK.now());

        // expect
        reindexResultEndpoint.expectedBodiesReceived("{\"advert\":{\"id\":88,\"visible_on_map\":false,\"paid_for\":false,\"bump_up_count\":0,\"cs_reviewed\":false},\"trigger_by\":\"REINDEX\",\"timestamp\":" + CLOCK.now() + "}");
        reindexResultEndpoint.expectedHeaderReceived("kafka.KEY", 88L);
        reindexResultEndpoint.expectedHeaderReceived("kafka.TIMESTAMP", CLOCK.now());

        // and
        realTimeResultEndpoint.expectedBodiesReceived(Collections.emptyList());

        // when
        producer.sendBody(event);

        // then
        reindexResultEndpoint.assertIsSatisfied();

        // and
        realTimeResultEndpoint.assertIsSatisfied();
    }

    @Test
    public void shouldSendChangeEventToChangeDestinationURI() throws InterruptedException {
        AdSearchIndexFeeder.FeedEvent event = new AdSearchIndexFeeder.FeedEvent(aFlatAd().withId(88L).build(), AD_CHANGE, CLOCK.now());

        // expect
        realTimeResultEndpoint.expectedBodiesReceived("{\"advert\":{\"id\":88,\"visible_on_map\":false,\"paid_for\":false,\"bump_up_count\":0,\"cs_reviewed\":false},\"trigger_by\":\"AD_CHANGE\",\"timestamp\":" + CLOCK.now() + "}");
        realTimeResultEndpoint.expectedHeaderReceived("kafka.KEY", 88L);
        realTimeResultEndpoint.expectedHeaderReceived("kafka.TIMESTAMP", CLOCK.now());

        // and
        reindexResultEndpoint.expectedBodiesReceived(Collections.emptyList());

        // when
        producer.sendBody(event);

        // then
        realTimeResultEndpoint.assertIsSatisfied();

        // and
        reindexResultEndpoint.assertIsSatisfied();
    }

    @Test
    public void shouldSendAdEventToRealTimeAndInternalKafkaDestinationURI() throws InterruptedException {

        String expectedUserId = "53458489";
        User.Builder userBuilder = User.aUser().withId(Long.valueOf(expectedUserId));
        FlatAd ad = FlatAd.aFlatAd().
                withId(1387197038L).
                createdBy(userBuilder).
                lastModifiedOn(new DateTime(1603735816880L)).
                withStatus(AdvertStatus.CREATED.name()).
                build();

        AdSearchIndexFeeder.FeedEvent event = new AdSearchIndexFeeder.FeedEvent(ad, AD_CHANGE, CLOCK.now());

        // expect

        realTimeResultEndpoint.expectedHeaderReceived("kafka.KEY", 1387197038L);
        realTimeResultEndpoint.expectedHeaderReceived("kafka.TIMESTAMP", CLOCK.now());
        realTimeResultEndpoint.expectedBodiesReceived("{\"advert\":{\"id\":1387197038,\"last_modified_date\":1603735816880,\"status\":\"CREATED\",\"visible_on_map\":false,\"paid_for\":false,\"created_by\":{\"id\":53458489,\"created_date\":0},\"bump_up_count\":0,\"cs_reviewed\":false},\"trigger_by\":\"AD_CHANGE\",\"timestamp\":" + CLOCK.now() + "}");


        internalRealTimeResultEndpoint.expectedHeaderReceived("kafka.KEY", 1387197038L);
        internalRealTimeResultEndpoint.expectedHeaderReceived("kafka.TIMESTAMP", CLOCK.now());
        internalRealTimeResultEndpoint.expectedMessageCount(1);
        internalRealTimeResultEndpoint.expectedMessagesMatches(exchange -> {

            try {
                String body = exchange.getIn().getBody(String.class);

                JSONObject json = new JSONObject(body);

                assertMatchesPattern("[a-f,0-9,-]+", json.getString("eventId"));
                assertThat(json.getLong("eventDateTime"), is(1603735816880L));
                assertThat(json.isNull("requestMetadataRegistryVersion"), is(true));
                assertThat(json.isNull("requestMetadata"), is(true));
                assertThat(json.getJSONObject("ad").getInt("adId"), is(1387197038));
                assertThat(json.getJSONObject("ad").isNull("versionId"), is(true));
                assertThat(json.getJSONObject("ad").isNull("adTitle"), is(true));
                assertThat(json.getJSONObject("ad").isNull("adDescription"), is(true));
                assertThat(json.getJSONObject("ad").getString("adType"), is("forsale"));
                assertThat(json.getJSONObject("ad").isNull("createdDate"), is(true));
                assertThat(json.getJSONObject("ad").isNull("publishedDate"), is(true));
                assertThat(json.getJSONObject("ad").getLong("lastModifiedDate"), is(1603735816880L));
                assertThat(json.getJSONObject("ad").isNull("deletedDate"), is(true));
                assertThat(json.getJSONObject("ad").isNull("archivedDate"), is(true));
                assertThat(json.getJSONObject("ad").isNull("expiryDate"), is(true));
                assertThat(json.getJSONObject("ad").getString("userId"), is("53458489"));
                assertThat(json.getJSONObject("ad").isNull("userType"), is(true));
                assertThat(json.getJSONObject("ad").isNull("proxyId"), is(true));
                assertThat(json.getJSONObject("ad").isNull("proxyName"), is(true));
                assertThat(json.getJSONObject("ad").getString("status"), is("CREATED"));
                assertThat(json.getJSONObject("ad").getJSONArray("location").length(), is(0));
                assertThat(json.getJSONObject("ad").isNull("category"), is(true));
                assertThat(json.getJSONObject("ad").isNull("askingPrice"), is(false));
                assertThat(json.getJSONObject("ad").getJSONArray("askingPrice").getJSONObject(0).get("grossAmount"), is(0.0));
                assertThat(json.getJSONObject("ad").getJSONArray("askingPrice").getJSONObject(0).get("currency"), is("GBP"));
                assertThat(json.getJSONObject("ad").isNull("adUrl"), is(true));
                assertThat(json.getJSONObject("ad").getJSONArray("externalAdUrl").length(), is(0));
                assertThat(json.getJSONObject("ad").getJSONArray("imageURL").length(), is(0));
                assertThat(json.getJSONObject("ad").isNull("videoURL"), is(true));
                assertThat(json.getJSONObject("ad").isNull("reasonCode"), is(true));
                assertThat(json.getJSONObject("ad").isNull("adAttribute"), is(true));

            } catch (Exception e) {
                LOGGER.error("Exception while parsing exchange body", e);
                return false;
            }

            return true;
        });


        // when
        producer.sendBody(event);

        // then
        realTimeResultEndpoint.assertIsSatisfied();
        internalRealTimeResultEndpoint.assertIsSatisfied();
    }

    @Test
    public void shouldSendDeletedAdEventToRealTimeAndInternalKafkaDestinationURIWithNewVersion() throws InterruptedException {


        String expectedUserId = "53458489";
        User.Builder userBuilder = User.aUser().withId(Long.valueOf(expectedUserId));
        int newVersion = 3;
        FlatAd ad = FlatAd.aFlatAd().
                withId(1387197038L).
                atVersion(newVersion).
                createdBy(userBuilder).
                lastModifiedOn(new DateTime(1603735816880L)).
                withStatus(AdvertStatus.DELETED_GDPR.name()).
                build();

        AdSearchIndexFeeder.FeedEvent event = new AdSearchIndexFeeder.FeedEvent(ad, DELETE, CLOCK.now());

        // expect

        realTimeResultEndpoint.expectedHeaderReceived("kafka.KEY", 1387197038L);
        realTimeResultEndpoint.expectedHeaderReceived("kafka.TIMESTAMP", CLOCK.now());
        realTimeResultEndpoint.expectedBodiesReceived(String.format("{\"advert\":" +
                        "{\"id\":1387197038," +
                        "\"version\":%d,\"last_modified_date\":1603735816880," +
                        "\"status\":\"DELETED_GDPR\"," +
                        "\"visible_on_map\":false," +
                        "\"paid_for\":false," +
                        "\"created_by\":{\"id\":53458489,\"created_date\":0}," +
                        "\"bump_up_count\":0" +
                        ",\"cs_reviewed\":false}," +
                        "\"trigger_by\":\"DELETE\"," +
                        "\"timestamp\":%s}",
                newVersion, CLOCK.now()));

        internalRealTimeResultEndpoint.expectedHeaderReceived("kafka.KEY", 1387197038L);
        internalRealTimeResultEndpoint.expectedHeaderReceived("kafka.TIMESTAMP", CLOCK.now());
        internalRealTimeResultEndpoint.expectedMessageCount(1);
        internalRealTimeResultEndpoint.expectedMessagesMatches(exchange -> {

            try {
                String body = exchange.getIn().getBody(String.class);
                Object header = exchange.getIn().getHeader("event-trigger");

                JSONObject json = new JSONObject(body);

                assertThat(header, is(AdSearchIndexFeeder.Trigger.AD_CHANGE));
                assertMatchesPattern("[a-f,0-9,-]+", json.getString("eventId"));
                assertThat(json.getLong("eventDateTime"), is(1603735816880L));
                assertThat(json.isNull("requestMetadataRegistryVersion"), is(true));
                assertThat(json.isNull("requestMetadata"), is(true));
                assertThat(json.getJSONObject("ad").getInt("adId"), is(1387197038));
                assertThat(json.getJSONObject("ad").getInt("versionId"), is(newVersion));
                assertThat(json.getJSONObject("ad").isNull("adTitle"), is(true));
                assertThat(json.getJSONObject("ad").getString("status"), is("DELETED_GDPR"));
            } catch (Exception e) {
                LOGGER.error("Exception while parsing exchange body", e);
                return false;
            }

            return true;
        });


        // when
        producer.sendBody(event);

        // then
        realTimeResultEndpoint.assertIsSatisfied();
        internalRealTimeResultEndpoint.assertIsSatisfied();
    }

    @Test
    public void shouldSendReindexOnlyToFullIndex() throws InterruptedException {


        String expectedUserId = "53458489";
        User.Builder userBuilder = User.aUser().withId(Long.valueOf(expectedUserId));
        int newVersion = 3;
        FlatAd ad = FlatAd.aFlatAd().
                withId(1387197038L).
                atVersion(newVersion).
                createdBy(userBuilder).
                lastModifiedOn(new DateTime(1603735816880L)).
                withStatus(AdvertStatus.DELETED_CS.name()).
                build();

        AdSearchIndexFeeder.FeedEvent event = new AdSearchIndexFeeder.FeedEvent(ad, FULLAD_REINDEX, CLOCK.now());

        // expect

        fullAdReindexDestinationUri.expectedHeaderReceived("kafka.KEY", 1387197038L);
        fullAdReindexDestinationUri.expectedHeaderReceived("kafka.TIMESTAMP", CLOCK.now());
        fullAdReindexDestinationUri.expectedBodiesReceived(String.format("{\"advert\":" +
                        "{\"id\":1387197038," +
                        "\"version\":%d," +
                        "\"last_modified_date\":1603735816880," +
                        "\"status\":\"DELETED_CS\"," +
                        "\"visible_on_map\":false," +
                        "\"paid_for\":false," +
                        "\"created_by\":{\"id\":53458489,\"created_date\":0}," +
                        "\"bump_up_count\":0,\"cs_reviewed\":false}," +
                        "\"trigger_by\":\"FULLAD_REINDEX\"," +
                        "\"timestamp\":%s}",
                newVersion, CLOCK.now()));

        internalRealTimeResultEndpoint.expectedMessageCount(0);
        realTimeResultEndpoint.expectedMessageCount(0);


        // when
        producer.sendBody(event);

        // then
        fullAdReindexDestinationUri.assertIsSatisfied();
        realTimeResultEndpoint.assertIsSatisfied();
        internalRealTimeResultEndpoint.assertIsSatisfied();
    }

    public static void assertMatchesPattern(String regex, String actual) {
        assertTrue("Expected string to match pattern: " + regex, StringUtils.isNotEmpty(actual) && Pattern.matches(regex, actual));
    }
}
