package com.gumtree.seller.notifications.provider.cursor;

import com.gumtree.seller.notifications.provider.PostgresCursorFactory;
import com.gumtree.seller.notifications.provider.SQLCursor;
import com.gumtree.seller.notifications.provider.SimpleCursor;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InOrder;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import static org.hamcrest.CoreMatchers.instanceOf;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class PostgresCursorFactoryTest {

    private DataSource datasource;

    private Connection connection;

    private Statement statement;

    private ResultSet resultSet;

    private PostgresCursorFactory factory;

    @Before
    public void init() throws Exception {
        datasource = mock(DataSource.class);
        connection = mock(Connection.class);
        statement = mock(Statement.class);
        resultSet = mock(ResultSet.class);
        factory = new PostgresCursorFactory(datasource);

        when(datasource.getConnection()).thenReturn(connection);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);
    }

    @Test
    public void factoryShouldCreateCursorCorrectlyForPostgres() throws Exception {
        SQLCursor sqlCursor = factory.executeQuery("select * from test", 1000);
        assertThat(sqlCursor, instanceOf(SimpleCursor.class));
        InOrder order = inOrder(connection, statement);
        order.verify(connection).setAutoCommit(false);
        order.verify(statement).setFetchSize(1000);
        order.verify(statement).executeQuery("select * from test");
    }
}
