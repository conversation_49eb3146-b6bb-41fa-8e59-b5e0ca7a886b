package com.gumtree.seller.repository.user;

import com.gumtree.integration.repository.BaseRepositoryTest;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.test.DataSetLocation;
import org.fest.assertions.api.Assertions;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@DataSetLocation(value = "classpath:/com/gumtree/seller/datasets/user-deactivation-test-dataset.xml", dataSourceName="sellerDatasource")
public class UserDeactivationRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private UserDeactivationRepository repository;

    @PersistenceContext
    private EntityManager entityManager;

    @Test
    @Transactional
    public void shouldDeleteByUserId() {
        // given
        Assertions.assertThat(repository.findAll()).hasSize(2);

        User usrRef = entityManager.getReference(User.class, 1L);

        // when
        repository.deleteByUser(usrRef);

        // then
        Assertions.assertThat(repository.findAll()).hasSize(1);

    }

}