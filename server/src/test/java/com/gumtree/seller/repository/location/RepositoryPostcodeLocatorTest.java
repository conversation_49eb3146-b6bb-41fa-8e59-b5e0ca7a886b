package com.gumtree.seller.repository.location;

import com.gumtree.seller.test.TestUtils;
import org.hamcrest.CoreMatchers;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;

public class RepositoryPostcodeLocatorTest {

    @Test
    public void returnsNullWhenPostcodeNotRecognisedInRepository() {
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(
                mock(PostcodeRepository.class), mock(OutcodeRepository.class));
        assertThat(locator.findPostcode("EC2V 1JR"), CoreMatchers.nullValue());
    }

    @Test
    public void returnsPostcodeWhenRecognisedPostcodeWithSpaceRequested() {
        PostcodeRepository repository = TestUtils.mockPostcodeRepository("EC2V1JR");
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(repository, mock(OutcodeRepository.class));
        assertThat(locator.findPostcode("EC2V 1JR").getPostcode(), equalTo("EC2V1JR"));
    }

    @Test
    public void returnsPostcodeWhenRecognisedPostcodeNoSpaceRequested() {
        PostcodeRepository repository = TestUtils.mockPostcodeRepository("EC2V1JR");
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(repository, mock(OutcodeRepository.class));
        assertThat(locator.findPostcode("EC2V1JR").getPostcode(), equalTo("EC2V1JR"));
    }

    @Test
    public void returnsPostcodeWhenRecognisedPostcodeInLowercaseRequested() {
        PostcodeRepository repository = TestUtils.mockPostcodeRepository("EC2V1JR");
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(repository, mock(OutcodeRepository.class));
        assertThat(locator.findPostcode("eC2V 1jR").getPostcode(), equalTo("EC2V1JR"));
    }

    @Test
    public void returnsNullWhenOutcodeNotRecognisedInRepository() {
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(
                mock(PostcodeRepository.class), mock(OutcodeRepository.class));
        assertThat(locator.findOutcode("EC2V 1JR"), CoreMatchers.nullValue());
    }

    @Test
    public void returnsNullWhenInvalidOutcodeIsSpecified() {
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(
                mock(PostcodeRepository.class), mock(OutcodeRepository.class));
        assertThat(locator.findOutcode("EC"), CoreMatchers.nullValue());
    }

    @Test
    public void returnsOutcodeWhenPostcodeWithSpaceForRecognisedOutcodeRequested() {
        OutcodeRepository repository = TestUtils.mockOutcodeRepository("EC2V");
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(mock(PostcodeRepository.class), repository);
        assertThat(locator.findOutcode("EC2V 1JR").getOutcode(), equalTo("EC2V"));
    }

    @Test
    public void returnsOutcodeWhenOnlyOutcodeRequested() {
        OutcodeRepository repository = TestUtils.mockOutcodeRepository("EC2V");
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(mock(PostcodeRepository.class), repository);
        assertThat(locator.findOutcode("EC2V").getOutcode(), equalTo("EC2V"));
    }

    @Test
    public void returnsOutcodeWhenPostcodeWithNoSpaceForRecognisedOutcodeRequested() {
        OutcodeRepository repository = TestUtils.mockOutcodeRepository("EC2V");
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(mock(PostcodeRepository.class), repository);
        assertThat(locator.findOutcode("EC2V1JR").getOutcode(), equalTo("EC2V"));
    }

    @Test
    public void returnsOutcodeWhenPostcodeInLowercaseForRecognisedOutcodeRequested() {
        OutcodeRepository repository = TestUtils.mockOutcodeRepository("EC2V");
        RepositoryPostcodeLocator locator = new RepositoryPostcodeLocator(mock(PostcodeRepository.class), repository);
        assertThat(locator.findOutcode("eC2V 1jR").getOutcode(), equalTo("EC2V"));
    }
}
