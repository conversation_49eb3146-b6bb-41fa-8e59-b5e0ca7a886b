package com.gumtree.seller.security;

import com.gumtree.common.util.security.exception.UserAccountNotActiveException;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.user.entity.ApiKey;
import com.gumtree.seller.domain.user.entity.Role;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.seller.service.security.SecurityService;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.subject.PrincipalCollection;
import org.junit.Test;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;

import static com.gumtree.seller.test.TestUtils.createRolePermission;
import static com.gumtree.seller.test.TestUtils.createUserPermission;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class PlatformRealmTest {

    @Test
    public void platformRealmShouldSupportApiKeyAuthenticationToken() {
        PlatformRealm realm = new PlatformRealm();
        assertThat(realm.supports(new ApiKeyAuthenticationToken(null)), equalTo(true));
    }

    @Test
    public void realmNameIsAsExpected() {
        PlatformRealm realm = new PlatformRealm();
        ReflectionTestUtils.setField(realm, "securityService", mock(SecurityService.class));
        assertThat(realm.getName(), equalTo("Gumtree_Seller_Backend_Realm"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void verifyGetAuthenticationInfoDoesNotAcceptInvalidTokenType() {
        PlatformRealm realm = new PlatformRealm();
        realm.doGetAuthenticationInfo(mock(UsernamePasswordToken.class));
    }

    @Test(expected = UnknownAccountException.class)
    public void verifyUnknownUserThrowsExceptionWhenGettingAuthenticationInfo() {
        ApiKeyAuthenticationToken token = new ApiKeyAuthenticationToken(null);
        PlatformRealm realm = new PlatformRealm();
        ReflectionTestUtils.setField(realm, "securityService", mock(SecurityService.class));
        realm.doGetAuthenticationInfo(token);
    }

    @Test
    public void verifyAuthenticationInfoForRecognisedUser() {
        ApiKey apiKey = new ApiKey();
        User user = new User();
        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setEmail("testUser");
        ReflectionTestUtils.setField(user, "username", emailAddress);
        ReflectionTestUtils.setField(user, "password", "myPassword");
        ReflectionTestUtils.setField(user, "salt", "mySalt");
        ReflectionTestUtils.setField(apiKey, "user", user);
        PlatformRealm realm = new PlatformRealm();
        ApiKeyAuthenticationToken token = new ApiKeyAuthenticationToken(apiKey);
        SimpleAuthenticationInfo authenticationInfo = (SimpleAuthenticationInfo) realm.doGetAuthenticationInfo(token);
        assertThat((String) authenticationInfo.getPrincipals().getPrimaryPrincipal(), equalTo("testUser"));
        assertThat(new ArrayList<String>(authenticationInfo.getPrincipals().getRealmNames()).get(0), equalTo("Gumtree_Seller_Backend_Realm"));
        assertThat((String) authenticationInfo.getCredentials(), equalTo("myPassword"));
        assertThat(authenticationInfo.getCredentialsSalt().toBase64(), equalTo("mySaAAA="));
    }

    @Test(expected = UserAccountNotActiveException.class)
    public void verifyUserAccountNotActiveException() {
        PlatformRealm realm = new PlatformRealm() {
            @Override
            protected Object getAvailablePrincipal(PrincipalCollection principals) {
                return "testUser";
            }
        };
        User user = new User();
        user.setStatus(UserStatus.DEACTIVATED);
        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setEmail("testUser");
        ReflectionTestUtils.setField(user, "username", emailAddress);
        ReflectionTestUtils.setField(user, "password", "myPassword");
        ReflectionTestUtils.setField(user, "salt", "mySalt");
        SecurityService repository = mock(SecurityService.class);
        when(repository.getUser("testUser")).thenReturn(user);
        when(repository.getUserWithRolesAndPermissions(anyString())).thenReturn(user);
        ReflectionTestUtils.setField(realm, "securityService", repository);

        PrincipalCollection collection = mock(PrincipalCollection.class);
        when(collection.getPrimaryPrincipal()).thenReturn(user);
        realm.doGetAuthorizationInfo(collection);
    }

    @Test(expected = AuthorizationException.class)
    public void verifyGetAuthorizationInfoThrowsExceptionForNullPrincipals() {
        PlatformRealm realm = new PlatformRealm();
        realm.doGetAuthorizationInfo(null);
    }

    @Test(expected = AuthorizationException.class)
    public void verifyGetAuthorizationInfoThrowsExceptionForEmptyPrincipals() {
        PlatformRealm realm = new PlatformRealm();
        realm.doGetAuthorizationInfo(mock(PrincipalCollection.class));
    }

    @Test(expected = AuthorizationException.class)
    public void verifyGetAuthorizationInfoThrowsExceptionForUnknownUsername() {
        PlatformRealm realm = new PlatformRealm() {
            @Override
            protected Object getAvailablePrincipal(PrincipalCollection principals) {
                return "testUser";
            }
        };

        ReflectionTestUtils.setField(realm, "securityService", mock(SecurityService.class));
        realm.doGetAuthorizationInfo(mock(PrincipalCollection.class));
    }

    @Test(expected = AuthorizationException.class)
    public void verifyDataAccessExceptionDuringRepositoryAccessCausesAuthorizationException() {
        PlatformRealm realm = new PlatformRealm() {
            @Override
            protected Object getAvailablePrincipal(PrincipalCollection principals) {
                return "testUser";
            }
        };
        SecurityService repository = mock(SecurityService.class);
        when(repository.getUserWithRolesAndPermissions(anyString())).thenThrow(new DataAccessResourceFailureException(""));
        ReflectionTestUtils.setField(realm, "securityService", repository);
        realm.doGetAuthorizationInfo(mock(PrincipalCollection.class));
    }

    @Test
    public void verifyAuthorizationInfoForRecognisedUser() {
        User user = new User();
        user.setStatus(UserStatus.ACTIVE);
        ReflectionTestUtils.setField(user, "permissions",
                Arrays.asList(
                        createUserPermission(1L, "d1", "a1", "1"),
                        createUserPermission(2L, "d1", "a2", "1"),
                        createUserPermission(3L, "d1", "a3", "1")));

        Role role1 = new Role();
        ReflectionTestUtils.setField(role1, "name", Role.RoleName.API_USER);
        ReflectionTestUtils.setField(role1, "permissions",
                Arrays.asList(
                        createRolePermission(1L, "d2", "a1", "*"),
                        createRolePermission(2L, "d2", "a2", "*"),
                        createRolePermission(3L, "d2", "a3", "*")));

        Role role2 = new Role();
        ReflectionTestUtils.setField(role2, "name", Role.RoleName.SUPER_USER);
        ReflectionTestUtils.setField(role2, "permissions",
                Arrays.asList(
                        createRolePermission(4L, "d2", "a1", "*"),
                        createRolePermission(5L, "d3", "a1", "*"),
                        createRolePermission(6L, "d1", "a3", "1")));

        ReflectionTestUtils.setField(user, "roles", Arrays.asList(role1, role2));

        PlatformRealm realm = new PlatformRealm() {
            @Override
            protected Object getAvailablePrincipal(PrincipalCollection principals) {
                return "testUser";
            }
        };

        SecurityService repository = mock(SecurityService.class);
        when(repository.getUserWithRolesAndPermissions("testUser")).thenReturn(user);
        when(repository.getUser(anyString())).thenReturn(user);
        ReflectionTestUtils.setField(realm, "securityService", repository);

        AuthorizationInfo authorizationInfo = realm.doGetAuthorizationInfo(mock(PrincipalCollection.class));
        Collection<String> roles = authorizationInfo.getRoles();
        Collection<String> permissions = authorizationInfo.getStringPermissions();
        assertThat(roles.size(), equalTo(2));
        assertThat(roles.contains(Role.RoleName.API_USER.toString()), equalTo(true));
        assertThat(roles.contains(Role.RoleName.SUPER_USER.toString()), equalTo(true));
        assertThat(permissions.size(), equalTo(7));
        assertThat(permissions.contains("d1:a1:1"), equalTo(true));
        assertThat(permissions.contains("d1:a2:1"), equalTo(true));
        assertThat(permissions.contains("d1:a3:1"), equalTo(true));
        assertThat(permissions.contains("d2:a1:*"), equalTo(true));
        assertThat(permissions.contains("d2:a2:*"), equalTo(true));
        assertThat(permissions.contains("d2:a3:*"), equalTo(true));
        assertThat(permissions.contains("d3:a1:*"), equalTo(true));
    }

    @Test
    public void authorizationCacheIsClearedAsExpected() {
        final InvocationCounter counter = new InvocationCounter();

        PlatformRealm realm = new PlatformRealm() {
            @Override
            protected void clearCachedAuthorizationInfo(PrincipalCollection principals) {
                assertThat((String) principals.getPrimaryPrincipal(), equalTo("<EMAIL>"));
                assertThat(new ArrayList<String>(principals.getRealmNames()).get(0), equalTo("Gumtree_Seller_Backend_Realm"));
                // Check realm name
                counter.invoked();
            }
        };

        realm.securityInfoChanged("<EMAIL>");
        assertThat(counter.getInvocationCount(), equalTo(1));
    }

    private class InvocationCounter {
        private int count = 0;

        public void invoked() {
            count++;
        }

        public int getInvocationCount() {
            return count;
        }
    }
}
