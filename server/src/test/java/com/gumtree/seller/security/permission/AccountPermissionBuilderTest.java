package com.gumtree.seller.security.permission;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 */
public class AccountPermissionBuilderTest {

    @Test
    public void whenNoActionOrTargetDefinedActionIsWildcardedAndTargetIsNotSpecified() {
        String permissionString = new AccountPermissionBuilder().build();
        assertEquals("account:*:*", permissionString);
    }

    @Test
    public void whenActionDefinedAndNoTargetDefinedActionIsSpecifiedAndTargetIsNotSpecified() {
        String permissionString = new AccountPermissionBuilder().withAction(AccountPermissionBuilder.Action.MANAGE_ADS).build();
        assertEquals("account:manageads:*", permissionString);
    }

    @Test
    public void whenNoActionDefinedAndTargetDefinedActionIsWildcardedAndTargetIsSpecified() {
        String permissionString = new AccountPermissionBuilder().withAccountId(1L).build();
        assertEquals("account:*:1", permissionString);
    }

    @Test
    public void whenActionAndTargetDefinedBothAreSpecified() {
        String permissionString = new AccountPermissionBuilder().withAccountId(1L)
                .withAction(AccountPermissionBuilder.Action.MANAGE_ADS)
                .build();
        assertEquals("account:manageads:1", permissionString);
    }
}
