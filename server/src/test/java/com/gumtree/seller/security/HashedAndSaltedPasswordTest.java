package com.gumtree.seller.security;

import org.apache.shiro.crypto.RandomNumberGenerator;
import org.apache.shiro.util.SimpleByteSource;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class HashedAndSaltedPasswordTest {

    @Test(expected = IllegalArgumentException.class)
    public void verifyThrowsExceptionForNullPassword() {
        new HashedAndSaltedPassword(null, mock(RandomNumberGenerator.class));
    }

    @Test(expected = IllegalArgumentException.class)
    public void verifyThrowsExceptionForEmptyPassword() {
        new HashedAndSaltedPassword("", mock(RandomNumberGenerator.class));
    }

    @Test
    public void passwordIsHashedWithGeneratedSalt() {
        HashedAndSaltedPassword password = buildPassword("myPassword");
        assertThat(password.getPassword(), equalTo("vbwms4RaqyLW/L2tPmNVkQ/rY0z7zeOqRSv73eF1D9k="));
        assertThat(password.getSalt(), equalTo("dGVzdHNhbHQ="));
    }

    @Test
    public void comparePasswordReturnsTrueWhenPasswordMatches() {
        String myPassword = "myPassword";
        String encPassword = "vbwms4RaqyLW/L2tPmNVkQ/rY0z7zeOqRSv73eF1D9k=";
        String encSalt = "dGVzdHNhbHQ=";
        assertThat(HashedAndSaltedPassword.comparePassword(myPassword, encPassword, encSalt ), equalTo(true));
    }

    @Test
    public void comparePasswordReturnsFalseWhenPasswordDoesntMatch() {
        String encPassword = "vbwms4RaqyLW/L2tPmNVkQ/rY0z7zeOqRSv73eF1D9k=";
        String encSalt = "dGVzdHNhbHQ=";
        assertThat(HashedAndSaltedPassword.comparePassword("notMyPassword", encPassword, encSalt ), equalTo(false));
    }

    @Test
    public void comparePasswordUsingCreatedPasswordMatches() {
        String myPassword = "super secret";
        HashedAndSaltedPassword password = buildPassword(myPassword);
        assertThat(HashedAndSaltedPassword.comparePassword(myPassword, password.getPassword(), password.getSalt()), equalTo(true));
    }

    private HashedAndSaltedPassword buildPassword(String plainTextPassword) {
        RandomNumberGenerator generator = mock(RandomNumberGenerator.class);
        SimpleByteSource byteSource = new SimpleByteSource("testsalt");
        when(generator.nextBytes()).thenReturn(byteSource);
        return new HashedAndSaltedPassword(plainTextPassword, generator);
    }
}
