package com.gumtree.seller.service.savedsearches;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.domain.savedsearches.ApiSavedSearch;
import com.gumtree.api.domain.savedsearches.SearchBean;
import com.gumtree.common.mongo.savedsearch.SavedSearch;
import com.gumtree.common.mongo.savedsearch.SavedSearchDBActions;
import com.gumtree.common.util.emailalert.CategoryNameMapper;
import com.gumtree.personalization.domain.EdialogUser;
import com.gumtree.personalization.domain.EmailAlertExtraAttributes;
import com.gumtree.personalization.domain.savedsearches.SavedSearchRequest;
import com.gumtree.personalization.domain.savedsearches.exception.SavedSearchNotFoundException;
import com.gumtree.personalization.repository.EdialogUserRepository;
import com.gumtree.personalization.repository.EmailAlertsExtraAttributesRepository;
import com.gumtree.seller.category.TestCategory;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.exception.UserNotFoundException;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.test.TestUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.shiro.authz.UnauthorizedException;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.core.convert.ConversionService;

import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verifyNoMoreInteractions;

/**
 * Unit tests for the {@link SavedSearchServiceImpl} class.
 */
public class SavedSearchServiceImplTest {

    private static final Answer<SavedSearch> SAVE_ANSWER = new Answer<SavedSearch>() {
        @Override
        public SavedSearch answer(InvocationOnMock invocation) throws Throwable {
            final Object o = invocation.getArguments()[0];
            return (SavedSearch) o;
        }
    };
    private SavedSearchSerializer mockSavedSearchSerializer;
    private SavedSearchDBActions mockSavedSearchRepostory;
    private UserRepository mockUserRepository;
    private EdialogUserRepository mockEdialogUserRepository;
    private EmailAlertsExtraAttributesRepository mockEmailAlertsExtraAttributesRepository;
    private ConversionService conversionService;
    private CategoryModel categoryModel;

    private SavedSearchServiceImpl savedSearchService;

    private static final Long TEST_USER_ID = 500L;
    private static final String TEST_EDIALOG_ID = DigestUtils.md5Hex(TEST_USER_ID + (new DateTime()).toString("yyyyMMdd"));
    private static final String TEST_JSON = "{testjson:1}";
    private static final Long TEST_CREATED_AT = new Date().getTime();

    @Before
    public void setup() {
        mockSavedSearchSerializer = mock(SavedSearchSerializer.class);
        mockSavedSearchRepostory = mock(SavedSearchDBActions.class);
        mockUserRepository = mock(UserRepository.class);
        mockEdialogUserRepository = mock(EdialogUserRepository.class);
        mockEmailAlertsExtraAttributesRepository = mock(EmailAlertsExtraAttributesRepository.class);
        categoryModel = mock(CategoryModel.class);
        conversionService = mock(ConversionService.class);
        savedSearchService = new SavedSearchServiceImpl(mockSavedSearchSerializer, mockSavedSearchRepostory,
                mockUserRepository, mockEdialogUserRepository, mockEmailAlertsExtraAttributesRepository, categoryModel,
                conversionService);
    }

    @Test
    public void storeSavedSearchForKnownUser() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // Given
        SavedSearchRequest savedSearchRequest = createSavedSearchRequest(true, false);

        when(mockSavedSearchRepostory.save(any(SavedSearch.class))).thenAnswer(SAVE_ANSWER);
        // When
        SavedSearch savedSearch = savedSearchService.storeSavedSearch(savedSearchRequest);

        // Then
        verify(mockSavedSearchRepostory).save(savedSearch);
        assertEquals(TEST_USER_ID, savedSearch.getUserId());
        assertEquals(false, savedSearch.getIsEmailAlert());
        assertEquals(TEST_JSON, savedSearch.getSearchJson());
    }

    @Test
    public void storeEmailAlertForKnownUser() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // Given
        SavedSearchRequest savedSearchRequest = createSavedSearchRequest(true, true);
        //ArgumentCaptor<EdialogUser> edialogUserArgumentCaptor = ArgumentCaptor.forClass(EdialogUser.class);
        List<Category> categoryList = Lists.newArrayList(TestCategory.withSeoName(1L, "pets"), TestCategory.withSeoName(2L, "pets"));

        when(categoryModel.getL1CategoryFor(savedSearchRequest.getSearch().getCategoryId())).thenReturn(Optional.of(categoryList.get(1)));
        when(categoryModel.getFullPath(2L)).thenReturn(categoryList);


        // When
        when(mockSavedSearchRepostory.save(any(SavedSearch.class))).thenAnswer(SAVE_ANSWER);
        SavedSearch savedSearch = savedSearchService.storeSavedSearch(savedSearchRequest);

        // Then
        verify(mockSavedSearchRepostory).save(savedSearch);
        //verify(mockEdialogUserRepository).save(edialogUserArgumentCaptor.capture());
        //assertEquals(TEST_EDIALOG_ID, edialogUserArgumentCaptor.getValue().getEdialogId());
        assertEquals(TEST_USER_ID, savedSearch.getUserId());
        assertEquals(true, savedSearch.getIsEmailAlert());
        assertNotNull(savedSearch.geteDialogId());
        assertEquals(TEST_JSON, savedSearch.getSearchJson());
    }

    @Test
    public void storeEmailAlertForKnownUserAlreadySubscribed() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // Given
        SavedSearchRequest savedSearchRequest = createSavedSearchRequest(true, true);
        List<Category> categoryList = Lists.newArrayList(TestCategory.withSeoName(1L, "pets"), TestCategory.withSeoName(2L, "pets"));

        when(categoryModel.getL1CategoryFor(savedSearchRequest.getSearch().getCategoryId())).thenReturn(Optional.of(categoryList.get(1)));
        when(categoryModel.getFullPath(2L)).thenReturn(categoryList);
        when(mockEdialogUserRepository.findByUserId(TEST_USER_ID)).thenReturn(mock(EdialogUser.class));

        // When
        when(mockSavedSearchRepostory.save(any(SavedSearch.class))).thenAnswer(SAVE_ANSWER);
        SavedSearch savedSearch = savedSearchService.storeSavedSearch(savedSearchRequest);

        // Then
        verify(mockSavedSearchRepostory).save(savedSearch);
        assertEquals(TEST_USER_ID, savedSearch.getUserId());
        assertEquals(true, savedSearch.getIsEmailAlert());
        assertNotNull(savedSearch.geteDialogId());
        assertEquals(TEST_JSON, savedSearch.getSearchJson());
    }

    @Test(expected = UserNotFoundException.class)
    public void storeSavedSearchForUnknownUser() {
        // Given
        SavedSearchRequest savedSearchRequest = createSavedSearchRequest(false, true);

        // When
        SavedSearch savedSearch = savedSearchService.storeSavedSearch(savedSearchRequest);
    }


    @Test
    public void deleteSavedSearchOwnedByUser() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // Given
        final SavedSearch mockSavedSearch = new SavedSearch.Builder()
                .setSearchJson(TEST_JSON)
                .setEDialogId(TEST_EDIALOG_ID)
                .setUserId(TEST_USER_ID)
                .setCreatedAt(TEST_CREATED_AT)
                .setIsEmailAlert(true)
                .build();
        List<SavedSearch> savedSearchList = Lists.newArrayList(mockSavedSearch);
        EdialogUser mockEdialogUser = mock(EdialogUser.class);
        when(mockSavedSearchRepostory.findOne("1")).thenReturn(Optional.of(mockSavedSearch));
        when(mockEdialogUserRepository.findByUserId(TEST_USER_ID)).thenReturn(mockEdialogUser);
        when(mockEdialogUserRepository.findByUserId(TEST_USER_ID)).thenReturn(mockEdialogUser);

        // When
        savedSearchService.deleteSavedSearch(TEST_USER_ID, "1");

        // Then
        verify(mockSavedSearchRepostory).delete(mockSavedSearch);
        //verify(mockEdialogUserRepository).delete(mockEdialogUser);
    }

    @Test
    public void testEnableSavedSearch() throws SavedSearchDBActions.SavedSearchDBActionsException {

        // given

        boolean searchAlertStatus = true;

        final SavedSearch mockSavedSearch = getSavedSearch(searchAlertStatus);

        ApiSavedSearch apiSavedSearch = mock(ApiSavedSearch.class);

        User user = TestUtils.createUser(1L);

        Category category = new Category();
        category.setId(1211L);
        category.setParentId(2313L);
        category.setName("For Sale");
        CategoryNameMapper sale = CategoryNameMapper.SALE;
        category.setSeoName(sale.getDbName());
        String forSaleBreadcrumb = "All >> For Sale";

        SearchBean searchBean = new SearchBean();
        searchBean.setCategoryId(category.getId());

        EmailAlertExtraAttributes ea = getExtraAttribute(mockSavedSearch, user, sale, forSaleBreadcrumb);

        when(mockUserRepository.findOne(TEST_USER_ID)).thenReturn(user);
        when(mockSavedSearchRepostory.findOne(mockSavedSearch.getId())).thenReturn(Optional.of(mockSavedSearch));
        when(mockUserRepository.getOne(user.getId())).thenReturn(user);
        when(categoryModel.getL1CategoryFor(category.getId())).thenReturn(Optional.of(category));
        when(categoryModel.getFullBreadcrumb(category.getId())).thenReturn(forSaleBreadcrumb);
        when(conversionService.convert(mockSavedSearch, ApiSavedSearch.class)).thenReturn(apiSavedSearch);
        when(apiSavedSearch.getSearch()).thenReturn(searchBean);

        // when
        savedSearchService.changeSearchAlertStatus(mockSavedSearch.getId(), searchAlertStatus);

        // then
        verify(mockSavedSearchRepostory, times(1)).save(mockSavedSearch);
        verify(mockEmailAlertsExtraAttributesRepository, times(1)).save(any(EmailAlertExtraAttributes.class));

    }

    @Test
    public void testChangeStatusSavedSearchNotFound() throws SavedSearchDBActions.SavedSearchDBActionsException {

        // given
        boolean searchAlertStatus = true;
        String id = "doesn't exist";

        when(mockSavedSearchRepostory.findOne(id)).thenReturn(Optional.absent());

        // when
        savedSearchService.changeSearchAlertStatus(id, searchAlertStatus);

        // then
        verify(mockSavedSearchRepostory, times(1)).findOne(id);

        verifyNoMoreInteractions(mockEmailAlertsExtraAttributesRepository);
    }

    @Test
    public void testDisableSavedSearch() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // given
        boolean searchAlertStatus = false;

        final SavedSearch mockSavedSearch = getSavedSearch(searchAlertStatus);

        final EmailAlertExtraAttributes extraAttribute = getExtraAttribute(mockSavedSearch, TestUtils.createUser(1L),
                CategoryNameMapper.SALE, "");

        when(mockSavedSearchRepostory.findOne(mockSavedSearch.getId())).thenReturn(Optional.of(mockSavedSearch));
        when(mockEmailAlertsExtraAttributesRepository.findBySavedSearchId(mockSavedSearch.getId())).thenReturn(extraAttribute);
        // when
        savedSearchService.changeSearchAlertStatus(mockSavedSearch.getId(), searchAlertStatus);

        // then
        verify(mockSavedSearchRepostory, times(1)).save(mockSavedSearch);
        verify(mockEmailAlertsExtraAttributesRepository, times(1)).delete(extraAttribute);
    }

    @Test
    public void testDisableSavedSearchNoExtraAttribs() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // given
        boolean searchAlertStatus = false;

        final SavedSearch mockSavedSearch = getSavedSearch(searchAlertStatus);

        final EmailAlertExtraAttributes extraAttribute = getExtraAttribute(mockSavedSearch, TestUtils.createUser(1L),
                CategoryNameMapper.SALE, "");

        when(mockSavedSearchRepostory.findOne(mockSavedSearch.getId())).thenReturn(Optional.of(mockSavedSearch));
        when(mockEmailAlertsExtraAttributesRepository.findBySavedSearchId(mockSavedSearch.getId())).thenReturn(null);
        // when
        savedSearchService.changeSearchAlertStatus(mockSavedSearch.getId(), searchAlertStatus);

        // then
        verify(mockSavedSearchRepostory, times(1)).save(mockSavedSearch);
        verify(mockEmailAlertsExtraAttributesRepository, times(0)).delete(extraAttribute);
    }

    private SavedSearch getSavedSearch(boolean searchAlertStatus) {
        return new SavedSearch.Builder()
                    .setSearchJson(TEST_JSON)
                    .setEDialogId(TEST_EDIALOG_ID)
                    .setUserId(TEST_USER_ID)
                    .setCreatedAt(TEST_CREATED_AT)
                    .setIsEmailAlert(searchAlertStatus)
                    .setId("saved-search-id")
                    .build();
    }

    private EmailAlertExtraAttributes getExtraAttribute(SavedSearch mockSavedSearch, User user, CategoryNameMapper sale, String forSaleBreadcrumb) {
        return new EmailAlertExtraAttributes.Builder()
                .withSavedSearchId(mockSavedSearch.getId())
                .alertType(sale.getEDialogName())
                .category(forSaleBreadcrumb)
                .userFirstName(user.getFirstName())
                .userLastName(user.getLastName())
                .userEmail(user.getEmailAddress().getEmail())
                .build();
    }

    @Test(expected = SavedSearchNotFoundException.class)
    public void deleteNonExistentSavedSearchThrowsException() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // Given
        when(mockSavedSearchRepostory.findOne("1")).thenReturn(Optional.<SavedSearch>absent());

        // When
        savedSearchService.deleteSavedSearch(TEST_USER_ID, "1");

        // Then
        verify(mockSavedSearchRepostory, never()).delete(any(SavedSearch.class));
    }

    @Test(expected = UnauthorizedException.class)
    public void deleteSavedSearchNotOwnedByEmailAddress() throws SavedSearchDBActions.SavedSearchDBActionsException {
        // Given
        SavedSearch mockSavedSearch = new SavedSearch.Builder()
                .setSearchJson(TEST_JSON)
                .setEDialogId(TEST_EDIALOG_ID)
                .setCreatedAt(TEST_CREATED_AT)
                .setIsEmailAlert(true)
                .setUserId(999999L)
                .build();
        when(mockSavedSearchRepostory.findOne("1")).thenReturn(Optional.of(mockSavedSearch));

        // When
        savedSearchService.deleteSavedSearch(TEST_USER_ID, "1");

        // Then
        verify(mockSavedSearchRepostory, never()).delete(any(SavedSearch.class));
    }

    private SavedSearchRequest createSavedSearchRequest(Boolean isKnownEmailAddress, Boolean getIsEmailAlert) {
        if (isKnownEmailAddress) {
            User mockUser = mock(User.class);
            when(mockUser.getId()).thenReturn(TEST_USER_ID);
            when(mockUser.getFirstName()).thenReturn("testName");
            when(mockUser.getLastName()).thenReturn("testLastName");
            EmailAddress emailAddress = mock(EmailAddress.class);
            when(mockUser.getEmailAddress()).thenReturn(emailAddress);
            when(emailAddress.getEmail()).thenReturn("<EMAIL>");

            when(mockUserRepository.findOne(TEST_USER_ID)).thenReturn(mockUser);
        }
        SearchBean searchBean = mock(SearchBean.class);
        when(searchBean.getCategoryId()).thenReturn(2L);
        when(mockSavedSearchSerializer.toJson(searchBean)).thenReturn(TEST_JSON);

        SavedSearchRequest savedSearchRequest = new SavedSearchRequest();
        savedSearchRequest.setEmailAlert(getIsEmailAlert);
        savedSearchRequest.setSearch(searchBean);
        savedSearchRequest.setUserId(TEST_USER_ID);

        return savedSearchRequest;
    }

}
