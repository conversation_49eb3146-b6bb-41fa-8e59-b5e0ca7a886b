package com.gumtree.seller.service.location.legacy.impl;

import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.repository.location.LocationRepository;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 *
 */
public class LegacyLocationHierarchyImplTest {

    private static LocationRepository locationRepository;

    @Before
    public void startUp() {
        locationRepository = mock(LocationRepository.class);
        when(locationRepository.findAll()).thenReturn(createLocationList());
    }


    @Test
    public void shouldReturnTrueWhenLocationExist() {
        LegacyLocationHierarchyImpl legacyLocationHierarchy = new LegacyLocationHierarchyImpl(locationRepository);
        assertThat(legacyLocationHierarchy.exists(2L), equalTo(true));
    }


    @Test
    public void shouldReturnFalseWhenTheLocationNotExist() {
        LegacyLocationHierarchyImpl legacyLocationHierarchy = new LegacyLocationHierarchyImpl(locationRepository);
        assertThat(legacyLocationHierarchy.exists(123L), equalTo(false));
    }

    @Test
    public void shouldReturnCorrectRoot() {
        LegacyLocationHierarchyImpl legacyLocationHierarchy = new LegacyLocationHierarchyImpl(locationRepository);
        assertThat(legacyLocationHierarchy.getRoot().getId(), equalTo(1L));
    }

    @Test(expected = IllegalStateException.class)
    public void shouldThrowExceptonWhenMoreThanOneRoot() {
        new LegacyLocationHierarchyImpl(twoRootRepository());
    }


    @Test
    public void shouldReturnCorrectChildrenGivenParentId() {
        LegacyLocationHierarchyImpl legacyLocationHierarchy = new LegacyLocationHierarchyImpl(locationRepository);
        assertChildren(legacyLocationHierarchy, 1L, Arrays.asList(2L, 3L));
        assertChildren(legacyLocationHierarchy, 2L, Arrays.asList(4L, 5L));
        assertChildren(legacyLocationHierarchy, 3L, Arrays.asList(6L));
        assertChildren(legacyLocationHierarchy, 4L, Arrays.asList(7L, 8L));

    }


    @Test
    public void shouldReturnTrueIfCategoryIsALeaf() {
        LegacyLocationHierarchyImpl legacyLocationHierarchy = new LegacyLocationHierarchyImpl(locationRepository);
        assertThat(legacyLocationHierarchy.isLeaf(5L), equalTo(true));
        assertThat(legacyLocationHierarchy.isLeaf(6L), equalTo(true));
        assertThat(legacyLocationHierarchy.isLeaf(7L), equalTo(true));
        assertThat(legacyLocationHierarchy.isLeaf(8L), equalTo(true));
    }

    @Test
    public void shouldReturnFalseIfCategoryIsNotALeaf() {
        LegacyLocationHierarchyImpl legacyLocationHierarchy = new LegacyLocationHierarchyImpl(locationRepository);
        assertThat(legacyLocationHierarchy.isLeaf(1L), equalTo(false));
        assertThat(legacyLocationHierarchy.isLeaf(2L), equalTo(false));
        assertThat(legacyLocationHierarchy.isLeaf(3L), equalTo(false));
        assertThat(legacyLocationHierarchy.isLeaf(4L), equalTo(false));
    }



    private void assertChildren(LegacyLocationHierarchyImpl legacyLocationHierarchy, Long parentId, List<Long> childrenIds) {
        List<Location> locations = legacyLocationHierarchy.getChildren(parentId);
        assertThat(locations.size(), equalTo(childrenIds.size()));

        for (Long childId : childrenIds) {
            Location childLocation = new Location();
            ReflectionTestUtils.setField(childLocation, "id", childId);
            assertThat(locations.contains(childLocation), equalTo(true));
        }
    }

    private LocationRepository twoRootRepository() {
        LocationRepository twoRootRepository = mock(LocationRepository.class);
        Location child = createLocation(3L, "Child", null);
        Location root1 = createLocation(1L, "Root1", Arrays.asList(child));
        Location root2 = createLocation(2L, "Root2", null);
        ReflectionTestUtils.setField(child, "zoomOuts", Arrays.asList(root1));
        when(twoRootRepository.findAll()).thenReturn(Arrays.asList(root1, root2, child));
        return twoRootRepository;
    }


    /**
     * Build a location hierarchy
     *
     * root
     * root-parent1
     * root-parent1-child11
     * root-parent1-child11-child111
     * root-parent1-child11-child112
     * root-parent1-child12
     * root-parent2
     * root-parent2-child21
     *
     */
    private static List<Location> createLocationList() {
        List<Location> locations = new ArrayList<Location>();
        Location child21 = createLocation(6L, "Luton", null );
        Location child12 = createLocation(5L, "West London", null);
        Location child111 = createLocation(7L, "Angel", null);
        Location child112 = createLocation(8L, "Camden", null);
        Location child11 = createLocation(4L, "Central London", Arrays.asList(child111, child112));
        Location parent2 = createLocation(3L, "Bedfordshire", Arrays.asList(child21));
        Location parent1 = createLocation(2L, "London", Arrays.asList(child11, child12));
        Location root = createLocation(1L, "United Kingdom", Arrays.asList(parent1, parent2));
        ReflectionTestUtils.setField(parent1, "zoomOuts", Arrays.asList(root));
        ReflectionTestUtils.setField(parent2, "zoomOuts", Arrays.asList(root));
        ReflectionTestUtils.setField(child11, "zoomOuts", Arrays.asList(parent1));
        ReflectionTestUtils.setField(child12, "zoomOuts", Arrays.asList(parent1));
        ReflectionTestUtils.setField(child21, "zoomOuts", Arrays.asList(parent2));
        ReflectionTestUtils.setField(child111, "zoomOuts", Arrays.asList(child11));
        ReflectionTestUtils.setField(child112, "zoomOuts", Arrays.asList(child11));
        locations.add(root);
        locations.add(parent1);
        locations.add(parent2);
        locations.add(child11);
        locations.add(child12);
        locations.add(child21);
        locations.add(child111);
        locations.add(child112);
        return locations;
    }

    private static Location createLocation(Long id, String name, List<Location> zoomIns) {
        Location location = new Location();
        ReflectionTestUtils.setField(location, "id", id);
        ReflectionTestUtils.setField(location, "name", name);
        ReflectionTestUtils.setField(location, "zoomIns", zoomIns);
        return location;
    }


}
