package com.gumtree.seller.service.orderItem;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.domain.order.entity.Order;
import com.gumtree.seller.domain.order.entity.OrderItem;
import com.gumtree.seller.domain.order.status.OrderItemStatus;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.seller.service.order.common.ApplicatorSelector;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import static java.util.Collections.singletonList;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class OrderItemServiceImplTest {

    @Mock
    private ApplicatorSelector applicatorSelector;

    private OrderItemService actionOrderItemService;

    @Before
    public void setup() {
        actionOrderItemService = new OrderItemServiceImpl();
        ReflectionTestUtils.setField(actionOrderItemService, "applicatorSelector", applicatorSelector);
    }

    @Test
    public void ignoreUnpaidOrderItems() {
        OrderItemStatus[] status = new OrderItemStatus[]{OrderItemStatus.CANCELLED, OrderItemStatus.REFUNDED, OrderItemStatus.UNPAID};

        for(OrderItemStatus s: status) {
            OrderItem orderItem = new OrderItem();
            orderItem.setStatus(s);
            actionOrderItemService.actionOrderItems(ImmutableMap.of(orderItem, Collections.emptyList()));
            assertEquals(orderItem.getStatus(), s);
        }
    }

    @Test
    public void actionPaidOrderItems() {
        // Given
        OrderItem orderItem = new OrderItem();
        Product product = new Product();
        product.setName(ProductName.BUMP_UP);
        orderItem.setProduct(product);
        orderItem.setPriceIncVat(10L);

        Advert advert = new Advert();
        advert.setStatus(AdvertStatus.LIVE);
        orderItem.setAdvert(advert);

        Order order = new Order();
        User user = new User();
        user.setStatus(UserStatus.ACTIVE);
        order.setCreatedBy(user);
        orderItem.setOrder(order);

        orderItem.setStatus(OrderItemStatus.PAID);
        when(applicatorSelector.applicatorsForProducts(any())).thenReturn(ImmutableList.of());
        Map<OrderItem, Collection<Product>> orderItemProducts =
                ImmutableMap.of(orderItem, singletonList(orderItem.getProduct()));

        // When
        actionOrderItemService.actionOrderItems(orderItemProducts);

        // Then
        assertEquals(orderItem.getStatus(), OrderItemStatus.ACTIONED);

        assertThat(advert.getPaidFor(), is(true));
    }

    @Test
    public void isOrderItemActionableShouldReturnFalseForActionedItems() {
        OrderItem orderItem = new OrderItem();
        orderItem.setStatus(OrderItemStatus.ACTIONED);
        assertFalse(actionOrderItemService.isOrderItemActionable(orderItem));
    }

    @Test
    public void isOrderItemActionableShouldReturnFalseForNotApplicableAdvertState() {
        OrderItem orderItem = new OrderItem();
        orderItem.setStatus(OrderItemStatus.PAID);

        Product product = new Product();
        product.setName(ProductName.BUMP_UP);
        orderItem.setProduct(product);

        Advert advert = new Advert();
        advert.setStatus(AdvertStatus.DRAFT);
        orderItem.setAdvert(advert);

        assertFalse(actionOrderItemService.isOrderItemActionable(orderItem));
    }

    @Test
    public void isOrderItemActionableShouldReturnFalseForNonActiceUsers() {
        OrderItem orderItem = new OrderItem();
        orderItem.setStatus(OrderItemStatus.PAID);

        Product product = new Product();
        product.setName(ProductName.BUMP_UP);
        orderItem.setProduct(product);

        Advert advert = new Advert();
        advert.setStatus(AdvertStatus.LIVE);
        orderItem.setAdvert(advert);

        Order order = new Order();
        User user = new User();
        user.setStatus(UserStatus.DEACTIVATED);
        order.setCreatedBy(user);
        orderItem.setOrder(order);

        assertFalse(actionOrderItemService.isOrderItemActionable(orderItem));
    }

    @Test
    public void isOrderItemActionable() {
        OrderItem orderItem = new OrderItem();
        orderItem.setStatus(OrderItemStatus.PAID);

        Product product = new Product();
        product.setName(ProductName.BUMP_UP);
        orderItem.setProduct(product);

        Advert advert = new Advert();
        advert.setStatus(AdvertStatus.LIVE);
        orderItem.setAdvert(advert);

        Order order = new Order();
        User user = new User();
        user.setStatus(UserStatus.ACTIVE);
        order.setCreatedBy(user);
        orderItem.setOrder(order);

        assertTrue(actionOrderItemService.isOrderItemActionable(orderItem));
    }

}