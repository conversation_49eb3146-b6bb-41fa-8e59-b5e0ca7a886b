package com.gumtree.seller.service.marketing.impl;



import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.gender.Gender;
import com.gumtree.seller.domain.location.entity.Postcode;
import com.gumtree.seller.domain.location.exception.PostcodeNotFoundException;
import com.gumtree.seller.domain.marketing.MarketingPreferenceServiceBean;
import com.gumtree.seller.domain.marketing.entity.UserMarketingPreference;
import com.gumtree.seller.domain.marketing.entity.UserMarketingPreferenceCategory;
import com.gumtree.seller.domain.marketing.exception.MarketingPreferenceNotFoundException;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.repository.email.EmailAddressRepository;
import com.gumtree.seller.repository.location.PostcodeRepository;
import com.gumtree.seller.repository.marketing.UserMarketingPreferenceCategoryRepository;
import com.gumtree.seller.repository.marketing.UserMarketingPreferenceRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.service.marketing.UserMarketingPreferenceService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.hamcrest.core.IsNull.nullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


/**
 * Author: bpadhiar
 */
public class UserMarketingPreferenceServiceImplTest {

    private UserMarketingPreferenceRepository userMarketingPreferenceRepository;
    private UserMarketingPreferenceCategoryRepository userMarketingPreferenceCategoryRepository;
    private PostcodeRepository postcodeRepository;
    private EmailAddressRepository emailAddressRepository;
    private UserMarketingPreferenceService service;
    private UserRepository userRepository;

    @Before
    public void setUp() {
        userMarketingPreferenceRepository = mock(UserMarketingPreferenceRepository.class);
        emailAddressRepository = mock(EmailAddressRepository.class);
        userRepository = mock(UserRepository.class);
        userMarketingPreferenceCategoryRepository = mock(UserMarketingPreferenceCategoryRepository.class);
        postcodeRepository = mock(PostcodeRepository.class);
        service = new UserMarketingPreferenceServiceImpl();
        ReflectionTestUtils.setField(service, "repository", userMarketingPreferenceRepository);
        ReflectionTestUtils.setField(service, "postcodeRepository", postcodeRepository);
        ReflectionTestUtils.setField(service, "categoryRepository", userMarketingPreferenceCategoryRepository);
        ReflectionTestUtils.setField(service, "emailRepository", emailAddressRepository);
        ReflectionTestUtils.setField(service, "userRepository", userRepository);

        initialiseTestConditions();

    }

    private void initialiseTestConditions() {
        Postcode postcode = mock(Postcode.class);
        when(postcode.getPostcode()).thenReturn("TW91EH");
        when(postcode.getId()).thenReturn(100L);
        when(postcodeRepository.findOne(100L)).thenReturn(postcode);
        when(postcodeRepository.findByPostcode("TW91EH")).thenReturn(postcode);
        when(postcodeRepository.findByPostcode("INVAL1D")).thenReturn(null);

    }

    @Test(expected = MarketingPreferenceNotFoundException.class)
    public void unknownUserEmailThrowsPreferenceNotFoundException() {

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        service.loadUserMarketingPreference("<EMAIL>");
        verify(userMarketingPreferenceRepository).findByUsername("<EMAIL>");
    }

    @Test
    public void returnsPreferenceWithKnownPostcode() {
        UserMarketingPreference userMarketingPreference = getFullMarketingPreference();
        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(userMarketingPreference);
        MarketingPreferenceServiceBean marketingPreferenceServiceBean = service.loadUserMarketingPreference("<EMAIL>");
        verify(userMarketingPreferenceRepository).findByUsername("<EMAIL>");

        assertThat(marketingPreferenceServiceBean.getId().get(), equalTo(1L));
        assertThat(marketingPreferenceServiceBean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(marketingPreferenceServiceBean.getLocationRadius().get().doubleValue(), equalTo(1.5));
        assertThat(marketingPreferenceServiceBean.getPostcode().get(), equalTo("TW91EH"));
        assertThat(marketingPreferenceServiceBean.getOptInGumtree().get(), equalTo(true));
        assertThat(marketingPreferenceServiceBean.getOptInEbay().get(), equalTo(true));
        assertThat(marketingPreferenceServiceBean.getOptInThirdParty().get(), equalTo(true));
        assertThat(marketingPreferenceServiceBean.getUnregisteredName().get(), equalTo("Gumtree Tester"));

        List<Long> preferredCategoryIds = marketingPreferenceServiceBean.getPreferredCategoryIds().get();
        Long categoryId = preferredCategoryIds.get(0);

        assertThat(categoryId, equalTo(123L));
    }

    @Test
    public void returnsPreferenceWithNoKnownPostcode() {
        UserMarketingPreference userMarketingPreference = getFullMarketingPreference();
        userMarketingPreference.setPostcodeLocationId(null);
        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(userMarketingPreference);
        MarketingPreferenceServiceBean marketingPreferenceServiceBean = service.loadUserMarketingPreference("<EMAIL>");

        assertThat(marketingPreferenceServiceBean.getId().get(), equalTo(1L));
        assertThat(marketingPreferenceServiceBean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(marketingPreferenceServiceBean.getPostcode().orNull(), nullValue());

    }

    @Test
    public void returnsPreferenceWithNoGenderForUnregisteredUser() {
        UserMarketingPreference userMarketingPreference = getFullMarketingPreference();
        userMarketingPreference.setPostcodeLocationId(null);
        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(userMarketingPreference);
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(null);

        MarketingPreferenceServiceBean marketingPreferenceServiceBean = service.loadUserMarketingPreference("<EMAIL>");

        assertThat(marketingPreferenceServiceBean.getId().get(), equalTo(1L));
        assertThat(marketingPreferenceServiceBean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(marketingPreferenceServiceBean.getGender().orNull(), nullValue());

    }

    @Test
    public void returnsPreferenceWithNoGenderForRegisteredUserWithGender() {
        User user = new User();
        UserMarketingPreference userMarketingPreference = getFullMarketingPreference();
        userMarketingPreference.setPostcodeLocationId(null);
        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(userMarketingPreference);
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(user);

        MarketingPreferenceServiceBean marketingPreferenceServiceBean = service.loadUserMarketingPreference("<EMAIL>");

        assertThat(marketingPreferenceServiceBean.getId().get(), equalTo(1L));
        assertThat(marketingPreferenceServiceBean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(marketingPreferenceServiceBean.getGender().orNull(), nullValue());

    }

    @Test
    public void returnsPreferenceWithNoGenderForRegisteredUserWithUnknownGender() {
        User user = new User();
        Gender gender = new Gender();
        user.setGender(gender);
        gender.setGender("Weirdo");
        UserMarketingPreference userMarketingPreference = getFullMarketingPreference();
        userMarketingPreference.setPostcodeLocationId(null);
        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(userMarketingPreference);
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(user);

        MarketingPreferenceServiceBean marketingPreferenceServiceBean = service.loadUserMarketingPreference("<EMAIL>");

        assertThat(marketingPreferenceServiceBean.getId().get(), equalTo(1L));
        assertThat(marketingPreferenceServiceBean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(marketingPreferenceServiceBean.getGender().orNull(), nullValue());

    }

    @Test
    public void returnsPreferenceWithGenderForRegisteredUserWithValidGender() {
        User user = new User();
        Gender gender = new Gender();
        user.setGender(gender);
        gender.setGender("male");
        UserMarketingPreference userMarketingPreference = getFullMarketingPreference();
        userMarketingPreference.setPostcodeLocationId(null);
        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(userMarketingPreference);
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(user);

        MarketingPreferenceServiceBean marketingPreferenceServiceBean = service.loadUserMarketingPreference("<EMAIL>");

        assertThat(marketingPreferenceServiceBean.getId().get(), equalTo(1L));
        assertThat(marketingPreferenceServiceBean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(marketingPreferenceServiceBean.getGender().orNull(), equalTo(MarketingPreferenceServiceBean.GenderPreference.MALE));

    }

    @Test(expected = PostcodeNotFoundException.class)
    public void canCreateNewUserPreferenceWithInvalidPostcodeThrowsException() {
        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        when(emailAddressRepository.findByEmail("<EMAIL>")).thenReturn(null);

        MarketingPreferenceServiceBean bean = createDummyMarketingPreferenceBean();
        bean.setPostcode("INVAL1D");
        service.saveOrUpdateUserMarketingPreference(bean);

        verify(postcodeRepository).findByPostcode("INVAL1D");
        verifyZeroInteractions(userMarketingPreferenceRepository);

    }

    @Test
    public void canCreateNewUserPreferenceWithNoSuppliedPostcode() {

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        when(emailAddressRepository.findByEmail("<EMAIL>")).thenReturn(null);

        MarketingPreferenceServiceBean bean = createDummyMarketingPreferenceBean();
        bean.setPostcode(null);
        service.saveOrUpdateUserMarketingPreference(bean);

        verifyZeroInteractions(postcodeRepository);

        ArgumentCaptor<UserMarketingPreference> prefArgCaptor = ArgumentCaptor.forClass(UserMarketingPreference.class);
        verify(userMarketingPreferenceRepository).saveAndFlush(prefArgCaptor.capture());
        UserMarketingPreference preferenceToSave = prefArgCaptor.getValue();

        assertThat(preferenceToSave.getPostcodeLocationId(), nullValue());

    }


    @Test
    public void canCreateNewUserPreferenceWithNewEmailAndRecognisedPostcode() {

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        when(emailAddressRepository.findByEmail("<EMAIL>")).thenReturn(null);

        MarketingPreferenceServiceBean bean = createDummyMarketingPreferenceBean();
        service.saveOrUpdateUserMarketingPreference(bean);

        verify(postcodeRepository).findByPostcode("TW91EH");

        ArgumentCaptor<UserMarketingPreference> prefArgCaptor = ArgumentCaptor.forClass(UserMarketingPreference.class);
        verify(userMarketingPreferenceRepository).saveAndFlush(prefArgCaptor.capture());
        UserMarketingPreference preferenceToSave = prefArgCaptor.getValue();

        assertThat(preferenceToSave.getEmailAddress().getId(), is(nullValue()));
        assertThat(preferenceToSave.getEmailAddress().getEmail(), equalTo("<EMAIL>"));
        assertThat(preferenceToSave.isOptInGumtree(), equalTo(true));
        assertThat(preferenceToSave.isOptInEbay(), equalTo(false));
        assertThat(preferenceToSave.isOptInThirdParty(), equalTo(false));
        assertThat(preferenceToSave.getUserMarketingPreferenceCategories().size(), equalTo(3));
        assertThat(preferenceToSave.getPostcodeLocationId(), equalTo(100L));
        assertThat(preferenceToSave.getPostcodeLocationRadius().doubleValue(), equalTo(10.5));

    }

    @Test
    public void canCreateNewUserPreferenceWithoutGender() {

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        when(emailAddressRepository.findByEmail("<EMAIL>")).thenReturn(null);

        MarketingPreferenceServiceBean bean = createDummyMarketingPreferenceBean();
        bean.setGender(null);
        service.saveOrUpdateUserMarketingPreference(bean);

        verifyZeroInteractions(userRepository);
        verify(userMarketingPreferenceRepository).saveAndFlush(any(UserMarketingPreference.class));

    }

    @Test
    public void canCreateNewUserPreferenceForExistingUserWithGenderSavingGenderIfDifferent() {

        User user = new User();
        Gender gender = new Gender();
        user.setGender(gender);
        gender.setGender("male");

        when(userRepository.findByUsername("<EMAIL>")).thenReturn(user);

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        when(emailAddressRepository.findByEmail("<EMAIL>")).thenReturn(null);

        MarketingPreferenceServiceBean bean = createDummyMarketingPreferenceBean();
        bean.setGender(MarketingPreferenceServiceBean.GenderPreference.FEMALE);
        service.saveOrUpdateUserMarketingPreference(bean);

        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userRepository).saveAndFlush(userArgumentCaptor.capture());
        verify(userMarketingPreferenceRepository).saveAndFlush(any(UserMarketingPreference.class));

        User userCaptured = userArgumentCaptor.getValue();
        assertThat(userCaptured.getGender().getGender(), equalTo("FEMALE"));

    }

    @Test
    public void canCreateNewUserPreferenceForExistingUserWithoutGenderNotSavingGender() {
        //If gender and current gender are the same, then dont bother persisting again

        User user = new User();
        Gender gender = new Gender();
        user.setGender(gender);
        gender.setGender("male");

        when(userRepository.findByUsername("<EMAIL>")).thenReturn(user);

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        when(emailAddressRepository.findByEmail("<EMAIL>")).thenReturn(null);

        MarketingPreferenceServiceBean bean = createDummyMarketingPreferenceBean();
        bean.setGender(MarketingPreferenceServiceBean.GenderPreference.MALE);
        service.saveOrUpdateUserMarketingPreference(bean);

        verify(userMarketingPreferenceRepository).saveAndFlush(any(UserMarketingPreference.class));
        verify(userRepository).findByUsername("<EMAIL>");
        verifyNoMoreInteractions(userRepository);
    }

    @Test
    public void canCreateNewUserPreferenceForExistingUserWithoutGenderSavingGender() {
        //If gender and current gender are the same, then dont bother persisting again

        User user = new User();
        Gender gender = new Gender();
        user.setGender(gender);
        gender.setGender("female");

        when(userRepository.findByUsername("<EMAIL>")).thenReturn(user);

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        when(emailAddressRepository.findByEmail("<EMAIL>")).thenReturn(null);

        MarketingPreferenceServiceBean bean = createDummyMarketingPreferenceBean();
        bean.setGender(MarketingPreferenceServiceBean.GenderPreference.MALE);
        service.saveOrUpdateUserMarketingPreference(bean);

        verify(userMarketingPreferenceRepository).saveAndFlush(any(UserMarketingPreference.class));
        verify(userRepository).saveAndFlush(any(User.class));
    }

    @Test
    public void canCreateNewUserPreferenceWithExistingUserEmail() {
        // A user exists, but they do not yet have any associated marketing preference.
        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setId(123L);
        emailAddress.setEmail("<EMAIL>");

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>")).thenReturn(null);
        when(emailAddressRepository.findByEmail("<EMAIL>")).thenReturn(emailAddress);

        MarketingPreferenceServiceBean command = new MarketingPreferenceServiceBean("<EMAIL>");
        command.setOptInGumtree(true);
        command.setOptInEbay(false);
        command.setOptInThirdParty(true);

        service.saveOrUpdateUserMarketingPreference(command);

        ArgumentCaptor<UserMarketingPreference> prefArgCaptor = ArgumentCaptor.forClass(UserMarketingPreference.class);
        verify(userMarketingPreferenceRepository).saveAndFlush(prefArgCaptor.capture());

        UserMarketingPreference preferenceToSave = prefArgCaptor.getValue();

        assertThat(preferenceToSave.getEmailAddress().getId(), equalTo(123L));
        assertThat(preferenceToSave.getEmailAddress().getEmail(), equalTo("<EMAIL>"));
        assertThat(preferenceToSave.isOptInGumtree(), equalTo(true));
        assertThat(preferenceToSave.isOptInEbay(), equalTo(false));
        assertThat(preferenceToSave.isOptInThirdParty(), equalTo(true));

    }

    @Test
    public void canUpdateExistingUserMarketingPreferenceForAllFields() {

        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setId(123L);
        emailAddress.setEmail("<EMAIL>");

        UserMarketingPreference existingSavedPreference = new UserMarketingPreference();
        existingSavedPreference.setId(100L);
        existingSavedPreference.setEmailAddress(emailAddress);
        existingSavedPreference.setOptInGumtree(true);
        existingSavedPreference.setOptInEbay(false);
        existingSavedPreference.setOptInThirdParty(true);


        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>"))
                .thenReturn(existingSavedPreference);

        verifyZeroInteractions(emailAddressRepository);

        MarketingPreferenceServiceBean command = new MarketingPreferenceServiceBean("<EMAIL>");
        command.setOptInGumtree(false);
        command.setOptInEbay(true);
        command.setOptInThirdParty(false);

        service.saveOrUpdateUserMarketingPreference(command);

        ArgumentCaptor<UserMarketingPreference> prefArgCaptor = ArgumentCaptor.forClass(UserMarketingPreference.class);
        verify(userMarketingPreferenceRepository).saveAndFlush(prefArgCaptor.capture());

        UserMarketingPreference preferenceToSave = prefArgCaptor.getValue();

        assertThat(preferenceToSave.getEmailAddress().getId(), equalTo(123L));
        assertThat(preferenceToSave.getEmailAddress().getEmail(), equalTo("<EMAIL>"));
        assertThat(preferenceToSave.isOptInGumtree(), equalTo(false));
        assertThat(preferenceToSave.isOptInEbay(), equalTo(true));
        assertThat(preferenceToSave.isOptInThirdParty(), equalTo(false));

    }

    @Test
    public void updatingPreferenceDoesNotChangeNullSuppliedFields() {

        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setId(123L);
        emailAddress.setEmail("<EMAIL>");

        UserMarketingPreference existingSavedPreference = new UserMarketingPreference();
        existingSavedPreference.setId(100L);
        existingSavedPreference.setEmailAddress(emailAddress);
        existingSavedPreference.setOptInGumtree(true);
        existingSavedPreference.setOptInEbay(false);
        existingSavedPreference.setOptInThirdParty(false);


        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>"))
                .thenReturn(existingSavedPreference);

        verifyZeroInteractions(emailAddressRepository);

        MarketingPreferenceServiceBean command = new MarketingPreferenceServiceBean("<EMAIL>");
        command.setOptInGumtree(false);


        service.saveOrUpdateUserMarketingPreference(command);

        ArgumentCaptor<UserMarketingPreference> prefArgCaptor = ArgumentCaptor.forClass(UserMarketingPreference.class);
        verify(userMarketingPreferenceRepository).saveAndFlush(prefArgCaptor.capture());

        UserMarketingPreference preferenceToSave = prefArgCaptor.getValue();

        assertThat(preferenceToSave.getEmailAddress().getId(), equalTo(123L));
        assertThat(preferenceToSave.getEmailAddress().getEmail(), equalTo("<EMAIL>"));
        assertThat(preferenceToSave.isOptInGumtree(), equalTo(false));
        assertThat(preferenceToSave.isOptInEbay(), equalTo(false));
        assertThat(preferenceToSave.isOptInThirdParty(), equalTo(false));

    }

    @Test
    public void updatingPreferenceCategoriesRemovesOldCategories() {
        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setId(123L);
        emailAddress.setEmail("<EMAIL>");

        UserMarketingPreference existingSavedPreference = new UserMarketingPreference();
        existingSavedPreference.setId(100L);
        existingSavedPreference.setEmailAddress(emailAddress);
        existingSavedPreference.setOptInGumtree(true);
        existingSavedPreference.setOptInEbay(false);
        existingSavedPreference.setOptInThirdParty(false);

        UserMarketingPreferenceCategory category = new UserMarketingPreferenceCategory();
        category.setCategoryId(123L);

        UserMarketingPreferenceCategory category2 = new UserMarketingPreferenceCategory();
        category2.setCategoryId(456L);

        List<UserMarketingPreferenceCategory> categories = new ArrayList();
        categories.add(category);
        categories.add(category2);
        existingSavedPreference.setUserMarketingPreferenceCategories(categories);

        when(userMarketingPreferenceRepository.findByUsername("<EMAIL>"))
                .thenReturn(existingSavedPreference);

        MarketingPreferenceServiceBean command = new MarketingPreferenceServiceBean("<EMAIL>");

        List<Long> categoryIds = new ArrayList();
        categoryIds.add(789L);
        command.setPreferredCategoryIds(categoryIds);

        service.saveOrUpdateUserMarketingPreference(command);

        ArgumentCaptor<UserMarketingPreference> prefArgCaptor = ArgumentCaptor.forClass(UserMarketingPreference.class);
        verify(userMarketingPreferenceRepository).saveAndFlush(prefArgCaptor.capture());

        UserMarketingPreference preferenceToSave = prefArgCaptor.getValue();

        assertThat(preferenceToSave.getEmailAddress().getId(), equalTo(123L));
        assertThat(preferenceToSave.getEmailAddress().getEmail(), equalTo("<EMAIL>"));
        assertThat(preferenceToSave.getUserMarketingPreferenceCategories().size(), equalTo(1));
        assertThat(preferenceToSave.getUserMarketingPreferenceCategories().get(0).getCategoryId(), equalTo(789L));
    }

    private UserMarketingPreference getFullMarketingPreference() {
        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setId(1L);
        emailAddress.setEmail("<EMAIL>");
        emailAddress.setOptInMarketing(true);

        UserMarketingPreference userMarketingPreference = new UserMarketingPreference();
        userMarketingPreference.setId(1L);
        userMarketingPreference.setEmailAddress(emailAddress);
        userMarketingPreference.setOptInGumtree(true);
        userMarketingPreference.setOptInEbay(true);
        userMarketingPreference.setOptInThirdParty(true);
        userMarketingPreference.setPostcodeLocationId(100L);
        userMarketingPreference.setPostcodeLocationRadius(new BigDecimal(1.5));
        userMarketingPreference.setUnregisteredName("Gumtree Tester");

        UserMarketingPreferenceCategory categoryPreference = new UserMarketingPreferenceCategory();
        categoryPreference.setCategoryId(123L);
        categoryPreference.setId(1L);
        categoryPreference.setUserMarketingPreference(userMarketingPreference);

        List<UserMarketingPreferenceCategory> categories = new ArrayList();
        categories.add(categoryPreference);
        userMarketingPreference.setUserMarketingPreferenceCategories(categories);

        return userMarketingPreference;
    }

    private MarketingPreferenceServiceBean createDummyMarketingPreferenceBean() {
        List<Long> categoryIds = new ArrayList();
        categoryIds.add(123L);
        categoryIds.add(456L);
        categoryIds.add(789L);

        MarketingPreferenceServiceBean bean = new MarketingPreferenceServiceBean("<EMAIL>");
        bean.setGender(MarketingPreferenceServiceBean.GenderPreference.MALE);
        bean.setLocationRadius(10.5);
        bean.setOptInGumtree(true);
        bean.setOptInEbay(false);
        bean.setOptInThirdParty(false);
        bean.setPostcode("TW91EH");
        bean.setPreferredCategoryIds(categoryIds);
        bean.setUnregisteredName(null);
        return bean;
    }




}