package com.gumtree.seller.service.location.impl;

import com.google.common.collect.Lists;
import com.gumtree.seller.domain.location.LocationCreationRequest;
import com.gumtree.seller.domain.location.LocationUpdateRequest;
import com.gumtree.seller.domain.location.PostcodeUpdateRequest;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.location.entity.Postcode;
import com.gumtree.seller.domain.location.exception.LocationNotFoundException;
import com.gumtree.seller.domain.location.exception.MissingLocationDataException;
import com.gumtree.seller.domain.location.exception.PostcodeNotFoundException;
import com.gumtree.seller.repository.location.LocationRepository;
import com.gumtree.seller.repository.location.PostcodeRepository;
import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeDiagnosingMatcher;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.google.common.collect.Lists.newArrayList;
import static com.gumtree.seller.domain.location.LocationUpdateRequest.newRequest;
import static java.util.Arrays.deepToString;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class LocationServiceImplTest {
    public static final int CHILD_ID = 5;
    public static final int PARENT_ID = 6;
    public static final long CHILD_ID_L = 5L;
    public static final long PARENT_ID_L = 6L;
    public static final long NOT_EXISTING_LOCATION_ID = 1456L;
    private LocationServiceImpl service = null;
    private LocationRepository locationRepository = null;
    private PostcodeRepository postcodeRepository = null;
    private Location child;
    private Location parent;
    private Location someLocation;

    @Before
    public void setUp() {
        service = new LocationServiceImpl();
        locationRepository = mock(LocationRepository.class);
        postcodeRepository = mock(PostcodeRepository.class);
        ReflectionTestUtils.setField(service, "locationRepository", locationRepository);
        ReflectionTestUtils.setField(service, "postcodeRepository", postcodeRepository);
        service.init();
        child = locationWIthId(CHILD_ID);
        parent = locationWIthId(PARENT_ID);
        someLocation = locationWIthId(9);
    }

    @Test(expected = LocationNotFoundException.class)
    public void getByIdThrowsExceptionWhenLocationNotRecognised() {
        when(locationRepository.findOne(anyLong())).thenReturn(null);
        service.getById(1L);
        verify(locationRepository).findOne(1L);
    }

    @Test
    public void getByIdReturnsLocationWhenLocationRecognised() {
        Location location = locationWIthId(1);

        assertThat(service.getById(1L), equalTo(location));
        verify(locationRepository).findOne(1L);
    }

    @Test
    public void getLevelReturnsCorrectLevel() {
        Location root = new Location();
        Location leaf = new Location();
        ReflectionTestUtils.setField(leaf, "id", 1L);
        leaf.getZoomOuts().add(root);
        when(locationRepository.findOne(1L)).thenReturn(leaf);

        assertThat(service.getLevelForLocation(1L), equalTo(2));
    }

    @Test
    public void getLevelReturnsCorrectLevelForRoot() {
        locationWIthId(2);

        assertThat(service.getLevelForLocation(2L), equalTo(1));
    }

    @Test
    public void getLevelReturnsCorrectLevelForBigTree() {
        Location root = new Location();
        Location middle = new Location();
        Location leaf = new Location();
        ReflectionTestUtils.setField(leaf, "id", 3L);
        leaf.getZoomOuts().add(middle);
        middle.getZoomOuts().add(root);
        when(locationRepository.findOne(3L)).thenReturn(leaf);

        assertThat(service.getLevelForLocation(3L), equalTo(3));
    }

    @Test(expected = LocationNotFoundException.class)
    public void whenUpdatingLocation_IfMainLocationCannotBeFound_ExceptionShouldBeThrown() {
        service.updateLocation(newRequest(NOT_EXISTING_LOCATION_ID));
    }

    @Test(expected = LocationNotFoundException.class)
    public void whenAddingZoomingOut_IfNewZoomLocationCannotBeFound_ExceptionShouldBeThrown() {
        locationWIthId(CHILD_ID);

        service.updateLocation(update(CHILD_ID_L).addZoomOuts(NOT_EXISTING_LOCATION_ID));
    }

    @Test(expected = LocationNotFoundException.class)
    public void whenAddingZoomingIn_IfNewZoomLocationCannotBeFound_ExceptionShouldBeThrown() {
        locationWIthId(CHILD_ID);

        service.updateLocation(update(CHILD_ID_L).addZoomIns(NOT_EXISTING_LOCATION_ID));
    }

    @Test(expected = LocationNotFoundException.class)
    public void whenRemovingZoomingOut_IfRemovedZoomLocationCannotBeFoundInRepo_ExceptionShouldBeThrown() {
        locationWIthId(CHILD_ID);

        service.updateLocation(update(CHILD_ID_L).removeZoomOuts(NOT_EXISTING_LOCATION_ID));
    }

    @Test(expected = LocationNotFoundException.class)
    public void whenRemovingZoomingIn_IfRemovedZoomLocationCannotBeFoundInRepo_ExceptionShouldBeThrown() {
        locationWIthId(CHILD_ID);

        service.updateLocation(update(CHILD_ID_L).removeZoomIns(NOT_EXISTING_LOCATION_ID));
    }

    @Test
    public void whenAddingZoomingOut_IfZoomingOutAlreadyExists_ZoomingStateShouldNotBeChanged() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(CHILD_ID_L).addZoomOuts(PARENT_ID_L));

        assertThat(child, hasZoomingOut(parent));
        assertThat(parent, hasZoomingIn(child));
    }

    @Test
    public void whenAddingZoomingOut_IfZoomingOutAlreadyExists_RepositoryShouldNotBeUpdated() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(CHILD_ID_L).addZoomOuts(PARENT_ID_L));

        assertThat(locationRepository, didntSave(child, parent));
    }

    @Test
    public void whenAddingZoomingIn_IfZoomingOutAlreadyExists_ZoomingStateShouldNotBeChanged() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(PARENT_ID_L).addZoomIns(CHILD_ID_L));

        assertThat(child, hasZoomingOut(parent));
        assertThat(parent, hasZoomingIn(child));
    }

    @Test
    public void whenAddingZoomingIn_IfZoomingInAlreadyExists_RepositoryShouldNotBeUpdated() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(PARENT_ID_L).addZoomIns(CHILD_ID_L));

        assertThat(locationRepository, didntSave(child, parent));
    }

    @Test
    public void whenAddingZoomingIn_IfZoomingInAlreadyExistsInOneDirection_OnlyReversedZoomingOutShouldBeAdded() {
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(PARENT_ID_L).addZoomIns(CHILD_ID_L));

        assertThat(child, hasZoomingOut(parent));
        assertThat(parent, hasZoomingIn(child));
        assertThat(locationRepository, didntSave(parent));
        assertThat(locationRepository, saved(child));
    }

    @Test
    public void whenAddingZoomingOut_IfZoomingOutAlreadyExistsInOneDirection_OnlyReversedZoomingInShouldBeAdded() {
        initializeZoomingOutBetween(child, parent);

        service.updateLocation(update(CHILD_ID_L).addZoomOuts(PARENT_ID_L));

        assertThat(child, hasZoomingOut(parent));
        assertThat(parent, hasZoomingIn(child));
        assertThat(locationRepository, didntSave(child));
        assertThat(locationRepository, saved(parent));
    }

    @Test
    public void whenAddingZoomingIn_IfReverseZoomingOutAlreadyExistsInOneDirection_OnlyContainingLocationShouldBeUpdated() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingOutBetween(child, someLocation);

        service.updateLocation(update(PARENT_ID_L).addZoomIns(CHILD_ID_L));

        assertThat(child, hasZoomingOut(parent, someLocation));
        assertThat(parent, hasZoomingIn(child));
        assertThat(locationRepository, didntSave(child));
        assertThat(locationRepository, saved(parent));
    }

    @Test
    public void whenAddingZoomingOut_IfReverseZoomingInAlreadyExistsInOneDirection_OnlyContainedLocationShouldBeUpdated() {
        initializeZoomingInBetween(parent, child);
        initializeZoomingInBetween(parent, someLocation);

        service.updateLocation(update(CHILD_ID_L).addZoomOuts(PARENT_ID_L));

        assertThat(child, hasZoomingOut(parent));
        assertThat(parent, hasZoomingIn(child, someLocation));
        assertThat(locationRepository, didntSave(parent));
        assertThat(locationRepository, saved(child));
    }

    @Test
    public void whenAddingZoomingIn_IfZoomingDoesntExist_FullUpdateShouldBePerformed() {
        initializeZoomingOutBetween(child, someLocation);
        initializeZoomingInBetween(parent, someLocation);

        service.updateLocation(update(PARENT_ID_L).addZoomIns(CHILD_ID_L));

        assertThat(child, hasZoomingOut(parent, someLocation));
        assertThat(parent, hasZoomingIn(child, someLocation));
        assertThat(locationRepository, saved(parent, child));
    }

    @Test
    public void whenAddingZoomingOut_IfZoomingDoesntExist_FullUpdateShouldBePerformed() {
        initializeZoomingOutBetween(child, someLocation);

        service.updateLocation(update(CHILD_ID_L).addZoomOuts(PARENT_ID_L));

        assertThat(child, hasZoomingOut(parent, someLocation));
        assertThat(parent, hasZoomingIn(child));
        assertThat(locationRepository, saved(parent, child));
    }

    @Test
    public void whenAddingZooming_ExistingDataShouldNotBeBroken() {
        initializeZoomingInBetween(child, someLocation);
        initializeZoomingOutBetween(parent, someLocation);

        service.updateLocation(update(PARENT_ID_L).addZoomIns(CHILD_ID_L));

        assertThat(child, hasZoomingOut(parent));
        assertThat(child, hasZoomingIn(someLocation));
        assertThat(parent, hasZoomingOut(someLocation));
        assertThat(parent, hasZoomingIn(child));
    }

    @Test
    public void zoomingUpdateShouldNotAllowForCreatingDirectCycle() {
        initializeZoomingInBetween(parent, child);
        initializeZoomingOutBetween(child, parent);

        service.updateLocation(update(PARENT_ID_L).addZoomOuts(CHILD_ID_L));

        verify(locationRepository, times(0)).save(any(Location.class));
        assertThat(parent.getZoomOuts(), is(empty()));
        assertThat(child.getZoomIns(), is(empty()));
        assertThat(child, hasZoomingOut(parent));
        assertThat(parent, hasZoomingIn(child));
    }

    @Test
    public void whenZoomingUpdateWouldCreateDirectCycle_NoRepositoryUpdateShouldBeRun() {
        initializeZoomingInBetween(parent, child);
        initializeZoomingOutBetween(child, parent);
        initializeZoomingOutBetween(parent, someLocation);

        service.updateLocation(update(PARENT_ID_L).addZoomOuts(CHILD_ID_L));

        assertThat(parent, hasZoomingOut(someLocation));
        assertThat(child.getZoomIns(), is(empty()));
        assertThat(locationRepository, didntSave(child, parent));
    }

    @Test
    public void whenRemovingZooming_IfZoomingDoesntExist_RepositoryShouldNotBeUpdated() {
        locationWIthId(CHILD_ID);
        locationWIthId(PARENT_ID);

        service.updateLocation(update(CHILD_ID_L).removeZoomOuts(PARENT_ID_L));

        assertThat(parent.getZoomOuts(), is(empty()));
        assertThat(child.getZoomIns(), is(empty()));
        assertThat(locationRepository, didntSave(child, parent));
    }

    @Test
    public void removingZoomingOutShouldRemoveExistingZooming() {
        initializeZoomingInBetween(parent, someLocation);
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(CHILD_ID_L).removeZoomOuts(PARENT_ID_L));

        assertThat(child, hasZoomingOut(noLocations()));
        assertThat(parent, hasZoomingIn(someLocation));
        assertThat(locationRepository, saved(child, parent));
    }

    @Test
    public void removingZoomingInShouldRemoveExistingZooming() {
        initializeZoomingInBetween(parent, someLocation);
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(PARENT_ID_L).removeZoomIns(CHILD_ID_L));

        assertThat(child, hasZoomingOut(noLocations()));
        assertThat(parent, hasZoomingIn(someLocation));
        assertThat(locationRepository, saved(child, parent));
    }

    @Test
    public void whenRemovingZoomingOut_IfZoomingIsOneDirectionZooming_ExistingZoomingShouldBeRemoved() {
        initializeZoomingInBetween(parent, someLocation);
        initializeZoomingOutBetween(child, parent);

        service.updateLocation(update(CHILD_ID_L).removeZoomOuts(PARENT_ID_L));

        assertThat(child, hasZoomingOut(noLocations()));
        assertThat(locationRepository, saved(child));
        assertThat(locationRepository, didntSave(parent));
    }

    @Test
    public void whenRemovingZoomingIn_IfZoomingIsOneDirectionZooming_ExistingZoomingShouldBeRemoved() {
        initializeZoomingInBetween(parent, someLocation);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(PARENT_ID_L).removeZoomIns(CHILD_ID_L));

        assertThat(parent, hasZoomingIn(someLocation));
        assertThat(locationRepository, saved(parent));
        assertThat(locationRepository, didntSave(child));
    }

    @Test
    public void whenRemovingZoomingOut_IfReversedZoomingOutExists_ZoomingStateShouldNotBeChanged() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(PARENT_ID_L).removeZoomOuts(CHILD_ID_L));

        assertThat(parent, hasZoomingIn(child));
        assertThat(child, hasZoomingOut(parent));
        assertThat(locationRepository, didntSave(child, parent));
    }

    @Test
    public void whenRemovingZoomingIn_IfReversedZoomingInExists_ZoomingStateShouldNotBeChanged() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);

        service.updateLocation(update(CHILD_ID_L).removeZoomIns(PARENT_ID_L));

        assertThat(parent, hasZoomingIn(child));
        assertThat(child, hasZoomingOut(parent));
        assertThat(locationRepository, didntSave(child, parent));
    }

    @Test(expected = LocationNotFoundException.class)
    public void whenRemovingZooming_IfMainLocationIdIsNull_ExceptionShouldBeThrown() {
        service.updateLocation(update(null).removeZoomIns(PARENT_ID_L));
    }

    @Test
    public void whenRemovingZooming_IfUpdateLocationIdIsNull_UpdateShouldBeIgnored() {
        service.updateLocation(newRequest(PARENT_ID_L).setZoomInsToRemove(null));

        assertThat(locationRepository, didntSave(parent));
    }

    @Test(expected = LocationNotFoundException.class)
    public void whenAddingZooming_IfMainLocationIdIsNull_ExceptionShouldBeThrown() {
        service.updateLocation(update(null).removeZoomIns(PARENT_ID_L));
    }

    @Test
    public void whenAddingZooming_IfUpdateLocationIdIsNull_UpdateShouldBeIgnored() {
        service.updateLocation(newRequest(CHILD_ID_L).setZoomOutsToAdd(null));

        assertThat(locationRepository, didntSave(child));
    }

    @Test
    public void whenAddingZoomingIn_IfUpdateLocationIsDuplicated_OnlyOneLocationShouldBeAdded() {
        service.updateLocation(update(CHILD_ID_L).addZoomIns(PARENT_ID_L, PARENT_ID_L));

        assertThat(child, hasZoomingOut(noLocations()));
        assertThat(parent, hasZoomingIn(noLocations()));
        assertThat(locationRepository, saved(child, parent));
    }

    @Test
    public void whenAddingZoomingOut_IfUpdateLocationIsDuplicated_OnlyOneLocationShouldBeAdded() {
        service.updateLocation(update(PARENT_ID_L).addZoomOuts(CHILD_ID_L, CHILD_ID_L));

        assertThat(child, hasZoomingOut(noLocations()));
        assertThat(parent, hasZoomingIn(noLocations()));
        assertThat(locationRepository, saved(child, parent));
    }

    @Test
    public void whenRemovingZoomingIn_IfUpdateLocationIsDuplicated_RemovingShouldRemoveTheLocation() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);
        service.updateLocation(update(PARENT_ID_L).removeZoomIns(CHILD_ID_L, CHILD_ID_L));

        assertThat(child, hasZoomingOut(noLocations()));
        assertThat(parent, hasZoomingIn(noLocations()));
        assertThat(locationRepository, saved(child, parent));
    }

    @Test
    public void whenRemovingZoomingOut_IfUpdateLocationIsDuplicated_RemovingShouldRemoveTheLocation() {
        initializeZoomingOutBetween(child, parent);
        initializeZoomingInBetween(parent, child);
        service.updateLocation(update(CHILD_ID_L).removeZoomOuts(PARENT_ID_L, PARENT_ID_L));

        assertThat(child, hasZoomingOut(noLocations()));
        assertThat(parent, hasZoomingIn(noLocations()));
        assertThat(locationRepository, saved(child, parent));
    }

    @Test(expected = MissingLocationDataException.class)
    public void whenCreatingLocation_IfNameWasNotSpecified_ExceptionShouldBeThrown() {
        service.createLocation(new LocationCreationRequest(1L).setDisplayName("My Locacion"));
    }

    @Test(expected = MissingLocationDataException.class)
    public void whenCreatingLocation_IfDisplayNameWasNotSpecified_ExceptionShouldBeThrown() {
        service.createLocation(new LocationCreationRequest(1L).setName("my-location"));
    }

    @Test
    public void whenCreatingLocation_IfIsLandingIsNotSet_FalseValueShouldBeUsed() {
        LocationCreationRequest request = new LocationCreationRequest(1L).setName("x").setDisplayName("y");

        service.createLocation(request);

        assertThat(savedLocation().isLanding(), is(false));
    }

    @Test
    public void whenCreatingLocation_NewLocationShouldHaveProvidedDisplayNameSet() {
        LocationCreationRequest request = new LocationCreationRequest(1L).setName("x").setDisplayName("Aberdeen City");

        service.createLocation(request);

        assertThat(savedLocation().getDisplayName(), is("Aberdeen City"));
    }

    @Test
    public void whenCreatingLocation_NewLocationShouldHaveProvidedNameSet() {
        LocationCreationRequest request = new LocationCreationRequest(1L).setName("aberdeen").setDisplayName("x");

        service.createLocation(request);

        assertThat(savedLocation().getName(), is("aberdeen"));
    }

    @Test
    public void whenCreatingLocation_NewLocationShouldHaveProvidedLandingValueSet() {
        LocationCreationRequest request = newLocationCreationRequest().setLanding(true);

        service.createLocation(request);

        assertThat(savedLocation().isLanding(), is(true));
    }

    @Test
    public void whenCreatingLocation_NewLocationShouldHaveProvidedCentroidLatitudeSet() {
        LocationCreationRequest request = newLocationCreationRequest().setCentroidLatitude(new BigDecimal(55.6));

        service.createLocation(request);

        assertThat(savedLocation().getLatitude(), is(new BigDecimal(55.6)));
    }

    @Test
    public void whenCreatingLocation_NewLocationShouldHaveProvidedCentroidLongitudeSet() {
        LocationCreationRequest request = newLocationCreationRequest().setCentroidLongitude(new BigDecimal(-2));

        service.createLocation(request);

        assertThat(savedLocation().getLongitude(), is(new BigDecimal(-2)));
    }

    @Test(expected = PostcodeNotFoundException.class)
    public void postcodeUpdateShouldThrowExceptionWhenPostcodeCannotBeFound() {
        PostcodeUpdateRequest updateRequest = new PostcodeUpdateRequest(34L);
        service.updatePostcode(updateRequest);
    }

    @Test
    public void postcodeUpdateShouldNotBePerformedIfRequestContainsCurrentData() {
        final Long locationId = 3L;
        final Long postcodeId = 9L;
        double lat = 100.02;
        double lon = 102.3;
        PostcodeUpdateRequest updateRequest =
                createPostcodeUpdateRequestAddingLocations(postcodeId, null, null, locationId);
        mockPostcodeInRepo(postcodeId, "NW102A", lat, lon, locationWIthId(locationId));

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(0)).save(any(Postcode.class));
    }

    @Test
    public void postcodeLatitudeUpdateShouldBePerformedIfLatitudeIsSetInTheRequest() {
        final Long locationId = 3L;
        final Long postcodeId = 9L;
        double newLat = 100.02;
        double oldLat = 101;
        double lon = 102.3;
        PostcodeUpdateRequest updateRequest =
                createPostcodeUpdateRequestAddingLocations(postcodeId, newLat, lon, locationId);
        mockPostcodeInRepo(postcodeId, "NW102A", oldLat, lon, locationWIthId(locationId));
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        BigDecimal updatedPostcodeLatitude = ((Postcode) postcodeCaptor.getValue()).getLatitude();
        assertThat(updatedPostcodeLatitude, equalTo(new BigDecimal(newLat)));
    }

    @Test
    public void postcodeLongitudeUpdateShouldBePerformedIfLongitudeIsSetInTheRequest() {
        final Long locationId = 3L;
        final Long postcodeId = 9L;
        double lat = 100.02;
        double oldLon = 739;
        double newLon = 102.3;
        PostcodeUpdateRequest updateRequest = createPostcodeUpdateRequestAddingLocations(postcodeId, lat, newLon, locationId);
        mockPostcodeInRepo(postcodeId, "NW102A", lat, oldLon, locationWIthId(locationId));
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        BigDecimal updatedPostcodeLongitude = ((Postcode) postcodeCaptor.getValue()).getLongitude();
        assertThat(updatedPostcodeLongitude, equalTo(new BigDecimal(newLon)));
    }

    @Test
    public void postcodeLocationsAfterUpdateShouldNotContainDuplicatedLocations() {
        final Long[] newLocationsIds = {3L, 4L};
        final Location[] existingLocations = {locationWIthId(3), locationWIthId(2)};
        final Long postcodeId = 9L;
        double lat = 100.02;
        double lon = 102.3;
        Location newLocation = locationWIthId(4);
        PostcodeUpdateRequest updateRequest =
                createPostcodeUpdateRequestAddingLocations(postcodeId, lat, lon, newLocationsIds);
        mockPostcodeInRepo(postcodeId, "NW102A", lat, lon, existingLocations);
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        List<Location> updatedPostcodeLocations = ((Postcode) postcodeCaptor.getValue()).getLocations();
        assertThat(updatedPostcodeLocations, contains(existingLocations[0], existingLocations[1], newLocation));
    }

    @Test
    public void existingPostcodeLatitudeShouldBeUsedInUpdateIfLatitudeIsNotSetInTheRequest() {
        final Long locationId = 3L;
        final Long postcodeId = 9L;
        double oldLat = 210.02;
        double newLon = 198.212;
        Double oldLon = 567D;
        PostcodeUpdateRequest updateRequest =
                createPostcodeUpdateRequestAddingLocations(postcodeId, null, newLon, locationId);
        mockPostcodeInRepo(postcodeId, "NW102A", oldLat, oldLon, locationWIthId(locationId));
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        Postcode updatedPostcode = ((Postcode) postcodeCaptor.getValue());
        assertThat(updatedPostcode.getLatitude(), equalTo(new BigDecimal(oldLat)));
        assertThat(updatedPostcode.getLongitude(), equalTo(new BigDecimal(newLon)));
    }

    @Test
    public void existingPostcodeLongitudeShouldBeUsedInUpdateIfLongitudeIsNotSetInTheRequest() {
        final Long locationId = 3L;
        final Long postcodeId = 9L;
        double newLat = 100.02;
        double oldLat = 210.02;
        Double oldLon = 567D;
        PostcodeUpdateRequest updateRequest =
                createPostcodeUpdateRequestAddingLocations(postcodeId, newLat, null, locationId);
        mockPostcodeInRepo(postcodeId, "NW102A", oldLat, oldLon, locationWIthId(locationId));
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        Postcode updatedPostcode = ((Postcode) postcodeCaptor.getValue());
        assertThat(updatedPostcode.getLatitude(), equalTo(new BigDecimal(newLat)));
        assertThat(updatedPostcode.getLongitude(), equalTo(new BigDecimal(oldLon)));
    }

    @Test
    public void existingPostcodeLocationsShouldBeUsedInUpdateIfLocationsAreNotSetInTheRequest() {
        final Location[] existingLocations = {locationWIthId(1), locationWIthId(2)};
        final Long postcodeId = 9L;
        double newLat = 100.02;
        double oldLat = 640.02;
        double lon = 102.3;
        PostcodeUpdateRequest updateRequest =
                createPostcodeUpdateRequestAddingLocations(postcodeId, newLat, null);
        mockPostcodeInRepo(postcodeId, "NW102A", oldLat, lon, existingLocations);
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        Postcode updatedPostcode = (Postcode) postcodeCaptor.getValue();
        assertThat(updatedPostcode.getLatitude(), equalTo(new BigDecimal(newLat)));
        assertThat(updatedPostcode.getLongitude(), equalTo(new BigDecimal(lon)));
        assertThat(updatedPostcode.getLocations(), contains(existingLocations[0], existingLocations[1]));
    }

    @Test
    public void postcodeUpdateShouldNotBePerformedIfRequestContainsNoUpdate() {
        final Long postcodeId = 9L;
        double lat = 100.02;
        double lon = 102.3;
        PostcodeUpdateRequest updateRequest = createPostcodeUpdateRequestAddingLocations(postcodeId, null, null);
        mockPostcodeInRepo(postcodeId, "NW102A", lat, lon, locationWIthId(7), locationWIthId(9));

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(0)).save(any(Postcode.class));
    }

    @Test
    public void postcodeLocationshouldBeAbleToBeRemoved() {
        final Location[] existingLocations = {
                locationWIthId(1), locationWIthId(2), locationWIthId(3), locationWIthId(4), locationWIthId(CHILD_ID)};
        final Long[] locationsToBeRemoved = {2L, 4L, CHILD_ID_L};
        PostcodeUpdateRequest updateRequest = new PostcodeUpdateRequest(111L)
                .setLocationIdsToBeremoved(arrayToList(locationsToBeRemoved));
        mockPostcodeInRepo(111L, "NW102A", -1d, 52d, existingLocations);
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        Postcode updatedPostcode = (Postcode) postcodeCaptor.getValue();
        assertThat(updatedPostcode.getLocations(), contains(locationWIthId(1), locationWIthId(3)));
    }

    @Test
    public void removingNotExistingLocationFromPostcodeShouldChangeNoPostcodeData() {
        final Location[] existingLocations = {locationWIthId(1), locationWIthId(2), locationWIthId(3)};
        final Long[] locationsToBeRemoved = {1L, 3L};
        final Long[] locationsToBeAdded = {4L, CHILD_ID_L};
        final Location newLoc4 = createLocation(4L, "L4");
        final Location newLoc5 = createLocation(CHILD_ID_L, "L5");
        PostcodeUpdateRequest updateRequest = new PostcodeUpdateRequest(111L)
                .setLocationIdsToBeremoved(arrayToList(locationsToBeRemoved))
                .setLocationIdsToBeAdded(arrayToList(locationsToBeAdded));
        mockPostcodeInRepo(111L, "NW102A", -1d, 52d, existingLocations);
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        Postcode updatedPostcode = (Postcode) postcodeCaptor.getValue();
        assertThat(updatedPostcode.getLocations(), contains(existingLocations[1], newLoc4, newLoc5));
    }

    @Test
    public void postcodeLocationsShouldBeAbleToBeAddedAndRemovedInOneRequest() {
        final Location[] existingLocations = {locationWIthId(1), locationWIthId(2), locationWIthId(3)};
        final Long[] locationsToBeRemoved = {1L, 3L};
        final Long[] locationsToBeAdded = {4L, CHILD_ID_L};
        final Location newLoc4 = createLocation(4L, "L4");
        final Location newLoc5 = createLocation(CHILD_ID_L, "L5");
        PostcodeUpdateRequest updateRequest = new PostcodeUpdateRequest(111L)
                .setLocationIdsToBeremoved(arrayToList(locationsToBeRemoved))
                .setLocationIdsToBeAdded(arrayToList(locationsToBeAdded));
        mockPostcodeInRepo(111L, "NW102A", -1d, 52d, existingLocations);
        ArgumentCaptor postcodeCaptor = ArgumentCaptor.forClass(Postcode.class);

        service.updatePostcode(updateRequest);

        verify(postcodeRepository, times(1)).save((Postcode) postcodeCaptor.capture());
        Postcode updatedPostcode = (Postcode) postcodeCaptor.getValue();
        assertThat(updatedPostcode.getLocations(), contains(existingLocations[1], newLoc4, newLoc5));
    }

    private void mockPostcodeInRepo(
            Long postcodeId, String postcodeStr, Double latitude, Double longitude, Location... locations) {
        Postcode postcode = new Postcode();
        ReflectionTestUtils.setField(postcode, "id", postcodeId);
        ReflectionTestUtils.setField(postcode, "postcode", postcodeStr);
        postcode.setLatitude(new BigDecimal(latitude));
        postcode.setLongitude(new BigDecimal(longitude));
        for (Location location : locations) {
            postcode.addLocation(location);
        }
        when(postcodeRepository.findOne(postcodeId)).thenReturn(postcode);
    }

    private PostcodeUpdateRequest createPostcodeUpdateRequestAddingLocations(
            Long postcodeId, Double lat, Double lon, Long... locationIds) {
        PostcodeUpdateRequest updateRequest = new PostcodeUpdateRequest(postcodeId);
        if (lat != null) {
            updateRequest.setLatitude(new BigDecimal(lat));
        }
        if (lon != null) {
            updateRequest.setLongitude(new BigDecimal(lon));
        }
        if (locationIds != null) {
            updateRequest.setLocationIdsToBeAdded(arrayToList(locationIds));
        }
        return updateRequest;
    }

    private Location locationWIthId(int id) {
        return createLocation((long)id, "");
    }

    private Location locationWIthId(long id) {
        return createLocation(id, "");
    }

    private Location createLocation(Long id, String name) {
        Location location = new Location();
        ReflectionTestUtils.setField(location, "id", id);
        ReflectionTestUtils.setField(location, "name", name);
        when(locationRepository.findOne(id)).thenReturn(location);
        return location;
    }

    private void initializeZoomingOutBetween(Location contained, Location containing) {
        contained.addZoomOut(containing);
    }

    private void initializeZoomingInBetween(Location containing, Location contained) {
        containing.addZoomIn(contained);
    }

    private LocationCreationRequest newLocationCreationRequest() {
        return new LocationCreationRequest(1L).setName("some-location").setDisplayName("Some Location");
    }

    private Location[] noLocations() {
        return new Location[0];
    }

    private <T> List<T> arrayToList(T... elements) {
        List<T> elementsList = new ArrayList<T>();
        Collections.addAll(elementsList, elements);
        return elementsList;
    }

    private Location savedLocation() {
        ArgumentCaptor<Location> postcodeCaptor = ArgumentCaptor.forClass(Location.class);
        verify(locationRepository, times(1)).save(postcodeCaptor.capture());
        return postcodeCaptor.getValue();
    }

    private LocationUpdater update(Long locationId) {
        return new LocationUpdater(locationId);
    }

    private class LocationUpdater {
        private final Long locationId;

        private LocationUpdater(Long locationId) {
            this.locationId = locationId;
        }

        private LocationUpdateRequest addZoomOuts(Long... ids) {
            return newRequest(locationId).setZoomOutsToAdd(newArrayList(ids));
        }

        private LocationUpdateRequest addZoomIns(Long... ids) {
            return newRequest(locationId).setZoomInsToAdd(newArrayList(ids));
        }

        private LocationUpdateRequest removeZoomOuts(Long... ids) {
            return newRequest(locationId).setZoomOutsToRemove(newArrayList(ids));
        }

        private LocationUpdateRequest removeZoomIns(Long... ids) {
            return newRequest(locationId).setZoomInsToRemove(newArrayList(ids));
        }
    }

    // matchers

    private SavedMatcher saved(Location... locations) {
        return new SavedMatcher(true, locations);
    }

    private SavedMatcher didntSave(Location... locations) {
        return new SavedMatcher(false, locations);
    }

    private class SavedMatcher extends TypeSafeDiagnosingMatcher<LocationRepository> {
        private final boolean shouldSave;
        private final Location[] locations;
        private final int expectedSavesAmount;

        private SavedMatcher(boolean shouldSave, Location[] locations) {
            this.shouldSave = shouldSave;
            this.locations = locations;
            expectedSavesAmount = shouldSave ? 1 : 0;
        }

        @Override
        protected boolean matchesSafely(LocationRepository repository, Description description) {
            for (Location location : locations) {
                verify(locationRepository, times(expectedSavesAmount)).save(location);
            }
            return true;
        }

        @Override
        public void describeTo(Description description) {
            description.appendText("Repository should " + (shouldSave ? "" : "not ") + "save all "
                    + Arrays.deepToString(locations));
        }
    }

    private Matcher hasZoomingOut(final Location... outLocations) {
        return new ZoomingUpdateMatcher(outLocations) {

            @Override
            protected List<Location> getZooming(Location location) {
                return location.getZoomOuts();
            }

            @Override
            protected String getZoomingDescription() {
                return "zoom out to ";
            }
        };
    }

    private Matcher hasZoomingIn(final Location... inLocations) {
        return new ZoomingUpdateMatcher(inLocations) {
            @Override
            protected List<Location> getZooming(Location location) {
                return location.getZoomIns();
            }

            @Override
            protected String getZoomingDescription() {
                return "zoom in to ";
            }
        };
    }

    private abstract class ZoomingUpdateMatcher extends TypeSafeDiagnosingMatcher<Location> {
        private final Location[] expectedLocations;

        protected ZoomingUpdateMatcher(Location... zoomingLocation) {
            this.expectedLocations = zoomingLocation;
        }

        protected abstract List<Location> getZooming(Location location);

        protected abstract String getZoomingDescription();

        @Override
        public void describeTo(Description description) {
            description.appendText("Location should ").appendText(getZoomingDescription())
                    .appendValue(deepToString(expectedLocations));
        }

        @Override
        protected boolean matchesSafely(Location location, Description description) {
            List<Location> actualZooming = getZooming(location);
            updateFailDescription(description, location, actualZooming);
            ArrayList<Location> expectedZooming = Lists.newArrayList(expectedLocations);
            return actualZooming.containsAll(expectedZooming) && expectedZooming.containsAll(actualZooming);
        }

        private void updateFailDescription(Description description, Location location, List<Location> actualZooming) {
            description.appendValue(location).appendText(" did not " + getZoomingDescription())
                    .appendValue(deepToString(expectedLocations)).appendText("it " + getZoomingDescription())
                    .appendValue(deepToString(actualZooming.toArray())).appendText(" instead");
        }
    }
}
