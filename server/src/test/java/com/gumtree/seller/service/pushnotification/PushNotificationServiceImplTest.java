package com.gumtree.seller.service.pushnotification;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.gumtree.api.domain.pushnotification.PushNotificationType;
import com.gumtree.common.mongo.push.PushNotification;
import com.gumtree.common.mongo.push.PushNotificationsDBActions;
import com.gumtree.common.mongo.savedsearch.SavedSearch;
import com.gumtree.personalization.domain.pushnotification.PushNotificationItem;
import com.gumtree.personalization.domain.pushnotification.PushNotificationsRequest;
import com.gumtree.personalization.domain.pushnotification.PushNotificationsResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class PushNotificationServiceImplTest {

    private static final String ID = "id";
    private static final String DEVICE_ID = "deviceId";
    private static final String UUID = "uuid";
    private static final Long ACCOUNT_ID = 93232L;
    private static final Long USER_ID = 2313L;

    private PushNotificationsDBActions pushNotificationsDBActions;

    private PushNotificationService service;
    private PushNotificationsRequest input;
    private static final Answer<PushNotification> SAVE_ANSWER = new Answer<PushNotification>() {
        @Override
        public PushNotification answer(InvocationOnMock invocation) throws Throwable {
            final Object o = invocation.getArguments()[0];
            return (PushNotification) o;
        }
    };
    @Before
    public void setup() {
        PushNotificationItem item =
                new PushNotificationItem(ID, PushNotificationType.SAVED_SEARCH.name(), ACCOUNT_ID, USER_ID);
        List<PushNotificationItem> pushNotificationItems = Lists.newArrayList(item);
        input = new PushNotificationsRequest(DEVICE_ID, UUID, pushNotificationItems);
        pushNotificationsDBActions = mock(PushNotificationsDBActions.class);;
        service = new PushNotificationServiceImpl(pushNotificationsDBActions);
    }

    @Test
    public void testRegistrationNewPushNotification() throws PushNotificationsDBActions.PushNotificationDBActionsException {
        when(pushNotificationsDBActions.findByDeviceId(input.getDeviceId()))
                .thenReturn(ImmutableList.<PushNotification>of());



        when(pushNotificationsDBActions.save(any(PushNotification.class)))
                .thenAnswer(SAVE_ANSWER);

        PushNotificationsResponse pushNotificationsResponse = service.register(input);
        assertThat(pushNotificationsResponse.getDeviceId(), equalTo(input.getDeviceId()));
        assertThat(pushNotificationsResponse.getUuid(), equalTo(input.getUuid()));
        assertThat(pushNotificationsResponse.getNotificationRequests().size(), equalTo(1));
    }

    @Test
    public void testRegistrationExistingPushNotification() throws PushNotificationsDBActions.PushNotificationDBActionsException {
        Long differentUserId = 456L;
        PushNotification existingPushNotification = new PushNotification.Builder()
                .setId(ID.toString())
                .setDeviceId(input.getDeviceId())
                .setUuid(input.getUuid())
                .setAccountId(input.getNotificationRequests().get(0).getAccountId())
                .setNotificationType(input.getNotificationRequests().get(0).getType())
                .setUserId(differentUserId).build();
        when(pushNotificationsDBActions.findByDeviceId(input.getDeviceId()))
                .thenReturn(ImmutableList.of(existingPushNotification));



        when(pushNotificationsDBActions.save(any(PushNotification.class)))
                .thenAnswer(SAVE_ANSWER);

        PushNotificationsResponse pushNotificationsResponse = service.register(input);
        assertThat(pushNotificationsResponse.getDeviceId(), equalTo(input.getDeviceId()));
        assertThat(pushNotificationsResponse.getUuid(), equalTo(input.getUuid()));
        assertThat(pushNotificationsResponse.getNotificationRequests().size(), equalTo(1));
    }

    @Test
    public void testRegistrationFails() throws PushNotificationsDBActions.PushNotificationDBActionsException{
        when(pushNotificationsDBActions.findByDeviceId(input.getDeviceId()))
                .thenReturn(ImmutableList.<PushNotification>of());

        when(pushNotificationsDBActions.save(any(PushNotification.class)))
                .thenThrow(PushNotificationsDBActions.PushNotificationDBActionsException.class);

        PushNotificationsResponse pushNotificationsResponse = service.register(input);
        assertThat(pushNotificationsResponse.getDeviceId(), equalTo(input.getDeviceId()));
        assertThat(pushNotificationsResponse.getUuid(), equalTo(input.getUuid()));
        assertThat(pushNotificationsResponse.getNotificationRequests().isEmpty(), equalTo(true));
    }

    @Test
    public void testSuccessfulUnregister() throws PushNotificationsDBActions.PushNotificationDBActionsException {
        when(pushNotificationsDBActions.delete(input.getNotificationRequests().get(0).getId().toString())).thenReturn(true);
        PushNotificationsResponse pushNotificationsResponse = service.unregister(input);
        assertThat(pushNotificationsResponse.getDeviceId(), equalTo(input.getDeviceId()));
        assertThat(pushNotificationsResponse.getUuid(), equalTo(input.getUuid()));
        assertThat(pushNotificationsResponse.getNotificationRequests().isEmpty(), equalTo(true));
    }

    @Test
    public void testUnsuccessfulUnregister() throws PushNotificationsDBActions.PushNotificationDBActionsException {
        final String expectedPushId =input.getNotificationRequests().get(0).getId().toString();
        doThrow(PushNotificationsDBActions.PushNotificationDBActionsException.class).
            when(pushNotificationsDBActions).delete(expectedPushId);

        PushNotificationsResponse pushNotificationsResponse = service.unregister(input);
        assertThat(pushNotificationsResponse.getDeviceId(), equalTo(input.getDeviceId()));
        assertThat(pushNotificationsResponse.getUuid(), equalTo(input.getUuid()));
        assertThat(pushNotificationsResponse.getNotificationRequests().size(), equalTo(1));
    }


}
