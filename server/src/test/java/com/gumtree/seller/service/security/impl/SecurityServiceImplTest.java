package com.gumtree.seller.service.security.impl;

import com.gumtree.seller.domain.account.status.AccountStatus;
import com.gumtree.seller.domain.user.entity.ApiKey;
import com.gumtree.seller.domain.user.entity.Role;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.repository.security.RoleRepository;
import com.gumtree.seller.repository.user.ApiKeyRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.security.SecurityInfoModificationListener;
import com.gumtree.seller.security.permission.AccountAction;
import com.gumtree.seller.service.util.RandomValueGenerator;
import com.gumtree.seller.test.BaseShiroTest;
import org.apache.shiro.subject.Subject;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Matchers;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.orm.jpa.JpaTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.Assert;

import static com.gumtree.seller.util.DomainObjectBuilders.aUser;
import static com.gumtree.seller.util.DomainObjectBuilders.anAccount;
import static com.gumtree.seller.util.DomainObjectBuilders.anEmailAddress;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class SecurityServiceImplTest extends BaseShiroTest {

    private SecurityServiceImpl securityService;

    private ApiKeyRepository apiKeyRepository;

    private RandomValueGenerator valueGenerator;

    private RoleRepository roleRepository;

    private UserRepository userRepository;

    private SecurityInfoModificationListener securityListener;

    private User user;

    private Role apiUserRole;

    @Before
    public void init() {
        valueGenerator = mock(RandomValueGenerator.class);
        apiKeyRepository = mock(ApiKeyRepository.class);
        roleRepository = mock(RoleRepository.class);
        userRepository = mock(UserRepository.class);
        securityListener = mock(SecurityInfoModificationListener.class);
        when(valueGenerator.secureRandomValue(32)).thenReturn("String1", "String2");
        securityService = new SecurityServiceImpl();
        ReflectionTestUtils.setField(securityService, "apiKeyRepository", apiKeyRepository);
        ReflectionTestUtils.setField(securityService, "valueGenerator", valueGenerator);
        ReflectionTestUtils.setField(securityService, "userRepository", userRepository);
        ReflectionTestUtils.setField(securityService, "roleRepository", roleRepository);
        ReflectionTestUtils.setField(securityService, "securityListener", securityListener);

        when(apiKeyRepository.save(any(ApiKey.class))).thenAnswer(new Answer<ApiKey>() {
            @Override
            public ApiKey answer(InvocationOnMock invocation) throws Throwable {
                ApiKey key = (ApiKey) invocation.getArguments()[0];
                Assert.hasLength(key.getAccessKey());
                Assert.hasLength(key.getPrivateKey());
                Assert.notNull(key.getUser());
                ReflectionTestUtils.setField(key, "id", 20L);
                return key;
            }
        });

        user = aUser().withId(1L).withEmailAddress(anEmailAddress().withEmail("<EMAIL>")).build();
        apiUserRole = new Role();
        ReflectionTestUtils.setField(apiUserRole, "name", Role.RoleName.API_USER);

        when(roleRepository.findByName(Role.RoleName.API_USER)).thenReturn(apiUserRole);
    }

    @Test
    public void getUserWithRolesAndPermissionsReturnsNullForUnrecognisedUser() {
        UserRepository repository = mock(UserRepository.class);
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        ReflectionTestUtils.setField(securityService, "userRepository", repository);
        assertThat(securityService.getUserWithRolesAndPermissions("testUsername"), equalTo(null));
    }

    @Test
    public void getUserWithRolesAndPermissionsForRecognisedUserDelegatesToRepository() {
        UserRepository repository = mock(UserRepository.class);
        User testUser = new User();
        ReflectionTestUtils.setField(testUser, "id", 1L);
        when(repository.findByUsername("testUsername")).thenReturn(testUser);
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        ReflectionTestUtils.setField(securityService, "userRepository", repository);
        assertThat(securityService.getUserWithRolesAndPermissions("testUsername"), equalTo(testUser));
    }

    @Test
    public void getUserForRecognisedUserDelegatesToRepository() {
        UserRepository repository = mock(UserRepository.class);
        User testUser = new User();
        ReflectionTestUtils.setField(testUser, "id", 1L);
        when(repository.findByUsername("testUsername")).thenReturn(testUser);
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        ReflectionTestUtils.setField(securityService, "userRepository", repository);
        assertThat(securityService.getUser("testUsername"), equalTo(testUser));
    }

    @Test
    public void getRoleOfCurrentUserReturnsTrueIfAssignedToUser() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        Subject subject = mock(Subject.class);
        when(subject.hasRole(anyString())).thenReturn(true);
        setSubject(subject);
        assertThat(securityService.hasCurrentUserRole(Role.RoleName.FEED_USER), equalTo(true));
    }

    @Test
    public void getRoleOfCurrentUserReturnsFalseIfNotAssignedToUser() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        Subject subject = mock(Subject.class);
        when(subject.hasRole(anyString())).thenReturn(false);
        setSubject(subject);
        assertThat(securityService.hasCurrentUserRole(Role.RoleName.FEED_USER), equalTo(false));
    }

    @Test
    public void currentUserDoesNotHavePermissionWhenCurrentUserIsNotPermitted() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        Subject subject = mock(Subject.class);
        when(subject.isPermitted(anyString())).thenReturn(false);
        setSubject(subject);
        assertThat(securityService.currentUserHasPermission("domain:*:*"), equalTo(false));
    }

    @Test
    public void currentUserHasPermissionWhenCurrentUserIsPermitted() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        Subject subject = mock(Subject.class);
        when(subject.isPermitted("domain1:action2:1")).thenReturn(true);
        setSubject(subject);
        assertThat(securityService.currentUserHasPermission("domain1:action2:1"), equalTo(true));
    }

    @Test
    public void currentUserWithNoUserLoggedInReturnsNull() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        Subject subject = mock(Subject.class);
        setSubject(subject);
        assertThat(securityService.getCurrentUser(), equalTo(null));
    }

    @Test
    public void currentUserWithUserLoggedInReturnsUser() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        User user = new User();
        UserRepository repository = mock(UserRepository.class);
        Subject subject = mock(Subject.class);
        ReflectionTestUtils.setField(user, "id", 1L);
        ReflectionTestUtils.setField(securityService, "userRepository", repository);
        String username = "testUsername";
        when(subject.getPrincipal()).thenReturn(username);
        when(repository.findByUsername(username)).thenReturn(user);
        setSubject(subject);
        assertThat(securityService.getCurrentUser(), equalTo(user));
    }

    @Test
    public void getApiKeyDelegatesToRepository() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        ApiKeyRepository repository = mock(ApiKeyRepository.class);
        ApiKey apiKey = new ApiKey();
        ReflectionTestUtils.setField(apiKey, "id", 100L);
        ReflectionTestUtils.setField(securityService, "apiKeyRepository", repository);
        when(repository.findByAccessKey("test_key")).thenReturn(apiKey);
        ApiKey key = securityService.getApiKey("test_key");
        assertThat(key, equalTo(apiKey));
    }

    @Test
    public void currentAuditorWithNoUserLoggedInReturnsNull() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        Subject subject = mock(Subject.class);
        setSubject(subject);
        assertThat(securityService.getCurrentAuditor(), equalTo(null));
    }

    @Test
    public void currentAuditorWithUserLoggedInReturnsUser() {
        SecurityServiceImpl securityService = new SecurityServiceImpl();
        User user = new User();
        JpaTemplate jpaTemplate = mock(JpaTemplate.class);
        Subject subject = mock(Subject.class);
        ReflectionTestUtils.setField(user, "id", 1L);
        ReflectionTestUtils.setField(securityService, "jpaTemplate", jpaTemplate);
        String username = "testUsername";
        GetUserJpaCallback expectedCallback = new GetUserJpaCallback("testUsername");
        when(subject.getPrincipal()).thenReturn(username);
        when(jpaTemplate.execute(expectedCallback)).thenReturn(user);
        setSubject(subject);
        assertThat(securityService.getCurrentAuditor(), equalTo(user));
    }

    @Test
    public void createsApiKeyCorrectly() {
        User user = aUser().withId(1L).build();
        ApiKey apiKey = securityService.createApiKey(user);

        //ArgumentCaptor<ApiKey> apiKeyCaptor = ArgumentCaptor.forClass(ApiKey.class);
        verify(apiKeyRepository).save(apiKey);

        assertThat(apiKey.getId(), equalTo(20L));
        assertThat(apiKey.getUser(), equalTo(user));
        assertThat(apiKey.getAccessKey(), equalTo("String1"));
        assertThat(apiKey.getPrivateKey(), equalTo("String2"));
    }

    @Test
    public void addsRoleToUserCorrectlyAndNotifiesChangeInSecurityInfo() {
        setSubject(mock(Subject.class));
        securityService.addRole(user, Role.RoleName.API_USER);
        ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
        verify(userRepository).save(userCaptor.capture());
        User savedUser = userCaptor.getValue();
        assertThat(savedUser.getRoles().size(), equalTo(1));
        assertThat(savedUser.getRoles().get(0), equalTo(apiUserRole));
        verify(securityListener).securityInfoChanged("<EMAIL>");
    }

    @Test(expected = IllegalStateException.class)
    public void throwsIllegalStateExceptionIfRoleWithRoleNameDoesNotExist() {
        when(roleRepository.findByName(Matchers.<Role.RoleName>any())).thenReturn(null);
        securityService.addRole(user, Role.RoleName.API_USER);
    }

    @Test
    public void activateAccountCanPerformActions() {
        assertTrue(securityService.accountHasPermission(
                anAccount().withStatus(AccountStatus.ACTIVE).build(), AccountAction.CREATE_AD));
    }

    @Test
    public void inactivateAccountCannotPerformActions() {
        assertFalse(securityService.accountHasPermission(
                anAccount().withStatus(AccountStatus.COLLECTION_AGENCY).build(), AccountAction.CREATE_AD));
    }
}
