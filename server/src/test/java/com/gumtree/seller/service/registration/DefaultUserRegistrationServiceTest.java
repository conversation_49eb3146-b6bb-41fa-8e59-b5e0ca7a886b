package com.gumtree.seller.service.registration;

import com.google.common.collect.Lists;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.advert.AdvertRequest;
import com.gumtree.seller.domain.email.entity.EmailAddress;
import com.gumtree.seller.domain.user.CreateUserCommand;
import com.gumtree.seller.domain.user.UserActivationType;
import com.gumtree.seller.domain.user.UserCreationMethod;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.entity.UserActivationKey;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.seller.domain.user.exception.UserActivationKeyExpiredException;
import com.gumtree.seller.domain.user.exception.UserActivationKeyUnrecognisedException;
import com.gumtree.seller.domain.user.exception.UserAlreadyActivatedException;
import com.gumtree.seller.domain.user.exception.UserAlreadyExistsException;
import com.gumtree.seller.domain.user.exception.UserNotFoundException;
import com.gumtree.seller.event.user.ActivationRequestedEvent;
import com.gumtree.seller.event.user.AfterUserActivatedEvent;
import com.gumtree.seller.event.user.BeforeUserActivatedEvent;
import com.gumtree.seller.event.user.CreatePasswordEvent;
import com.gumtree.seller.repository.user.UserActivationKeyRepository;
import com.gumtree.seller.repository.user.UserRepository;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.service.keygen.KeyGeneratorService;
import com.gumtree.seller.service.user.ParticipatingUserInitializer;
import com.gumtree.seller.service.user.UserService;
import com.gumtree.seller.service.util.TestEventService;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.context.ApplicationEvent;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;
import java.util.List;

import static com.gumtree.seller.domain.email.entity.EmailVerificationKey.ACTIVE_DAYS;
import static com.gumtree.seller.domain.user.UserActivationType.ON_REACTIVATION;
import static com.gumtree.seller.domain.user.UserActivationType.ON_REGISTRATION;
import static com.gumtree.seller.domain.user.entity.UserStatus.ACTIVE;
import static com.gumtree.seller.domain.user.entity.UserStatus.AWAITING_ACTIVATION;
import static com.gumtree.seller.domain.user.entity.UserStatus.DEACTIVATED;
import static com.gumtree.seller.test.TestUtils.buildUser;
import static com.gumtree.seller.test.TestUtils.createUser;
import static com.gumtree.seller.test.TestUtils.userActivationKeyBuilder;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultUserRegistrationServiceTest {

    private static final DateTime NOW = new DateTime(2011, 11, 11, 0, 0, 0, 0);

    private static final DateTime EXPIRED_ACTIVATION_KEY_EXPIRY_DATE = new DateTime(2011, 11, 10, 11, 59, 59, 0);

    private static final DateTime UNEXPIRED_ACTIVATION_KEY_EXPIRY_DATE = new DateTime(2011, 11, 11, 0, 0, 1, 0);

    private static final String USER_NAME = "<EMAIL>";
    public static final String TEST_KEY = "TEST_KEY";

    @InjectMocks
    private DefaultUserRegistrationService registrationService;
    @Mock
    private UserService userService;
    @Mock
    private UserRepository userRepository;
    @Mock
    private UserActivationKeyRepository activationKeyRepository;
    @Mock
    private KeyGeneratorService keyGeneratorService;
    @Mock
    private Clock clock;

    @Spy
    private TestEventService eventService = new TestEventService();

    @Mock
    private ParticipatingUserInitializer authenticatedUserInitializer;
    @Mock
    private AdvertService advertService;

    @Before
    public void init() {
        when(activationKeyRepository.save(Matchers.<UserActivationKey>any())).thenAnswer(new Answer<UserActivationKey>() {
            @Override
            public UserActivationKey answer(InvocationOnMock invocation) throws Throwable {
                Object[] args = invocation.getArguments();
                UserActivationKey key = (UserActivationKey) args[0];
                assertThat("id must be null when creating a new activation key", key.getId(), equalTo(null));
                ReflectionTestUtils.setField(key, "id", 1L);
                return key;
            }
        });

        when(clock.getDateTime()).thenReturn(NOW);

        when(keyGeneratorService.generateKey(anyString(), anyString(), anyLong())).thenReturn(TEST_KEY);

        eventService.reset();
    }

    /**
     * ****************** REGISTER USER TESTS ******************
     */

    @Test(expected = UserAlreadyExistsException.class)
    public void registerUserThrowsExceptionIfDuplicateUsername() {
        when(userService.createUser(Matchers.<CreateUserCommand>anyObject())).thenThrow(
                new UserAlreadyExistsException("testUsername"));
        registrationService.registerUser(new CreateUserCommand());
    }

    @Test
    public void shouldRegisterUserWithBasicFields() {

        // given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(null);
        when(userService.createUser(any())).thenReturn(basicUser);

        // when
        User user = registrationService.registerUser(buildCreateUserCommand(basicUser, true));

        // then user
        assertThat(user.getUsername(), equalTo(basicUser.getUsername()));
        assertThat(user.getActivationDate(), nullValue());

        // then event
        assertActivationRequestedEventRaised(ON_REGISTRATION, user);
        verify(userService, times(1)).createUser(any(CreateUserCommand.class));

    }

    @Test
    public void shouldRegisterUserViaPostAdWithBasicFields() {

        // given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(null);
        when(userService.createUser(any())).thenReturn(basicUser);

        CreateUserCommand createUserCommand = buildCreateUserCommand(basicUser, true);
        createUserCommand.setUserCreationMethod(UserCreationMethod.POST_AD_FLOW);
        AdvertRequest requestAdvert = generateBasicCreateAdvertRequest(basicUser.getId());
        advertService.createAdvert(requestAdvert);

        // when
        User user = registrationService.registerUser(createUserCommand);

        // then user
        assertThat(user.getUsername(), equalTo(basicUser.getUsername()));
        assertThat(user.getActivationDate(), nullValue());

        // then event
        assertActivationRequestedEventRaised(UserActivationType.ON_POST_AD, user);
        verify(userService, times(1)).createUser(any(CreateUserCommand.class));

    }

    @Test
    public void shouldRegisterUserWithOptOutOfPromotionalEmails() {

        // given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(null);
        when(userService.createUser(any())).thenReturn(basicUser);

        // when
        User user = registrationService.registerUser(buildCreateUserCommand(basicUser, false));

        // then
        assertThat(user.getEmailAddress().isOptInMarketing(), equalTo(false));
        verify(userService, times(1)).createUser(any(CreateUserCommand.class));
        assertActivationRequestedEventRaised(ON_REGISTRATION, basicUser);
    }

    @Test
    public void shouldRegisterUserAndSendActivationRequestEvent() {

        // given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(null);
        when(userService.createUser(any())).thenReturn(basicUser);

        // when
        registrationService.registerUser(buildCreateUserCommand(basicUser, true));

        // then
        verify(authenticatedUserInitializer, times(1)).initializeUser(basicUser);
        verify(userService, times(1)).createUser(any(CreateUserCommand.class));
        assertActivationRequestedEventRaised(ON_REGISTRATION, basicUser);
    }

    @Test
    public void shouldReactivateUserAndSendActivationRequestEvent() {
        // given
        User deactivatedUser = buildUser(1L,"testUser", DEACTIVATED);
        deactivatedUser.setStatus(UserStatus.DEACTIVATED);

        when(userService.createOrUpdateUser(Matchers.<CreateUserCommand>any())).thenReturn(deactivatedUser);
        when(userRepository.findByUsername(deactivatedUser.getUsername())).thenReturn(deactivatedUser);

        // when
        User user = registrationService.registerUser(buildCreateUserCommand(deactivatedUser, true));

        // then
        assertThat(user.getStatus(), is(AWAITING_ACTIVATION));

        verify(userService, never()).createUser(any(CreateUserCommand.class));
        assertActivationRequestedEventRaised(ON_REACTIVATION, deactivatedUser);
    }

    @Test
    public void shouldSendActivationRequestEventForExistingAwaitingActivationUser() {
        // given
        User basicUser = buildUser(1L,"testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(basicUser);
        when(userService.createOrUpdateUser(Matchers.<CreateUserCommand>any())).thenReturn(basicUser);

        // when
        User result = registrationService.registerUser(buildCreateUserCommand(basicUser, true));

        // then
        verify(userService, never()).createUser(any(CreateUserCommand.class));
        assertActivationRequestedEventRaised(ON_REGISTRATION, result);
    }

    @Test
    public void shouldThrowExceptionWhenRegisterActiveUser() {
        // given
        User activeUser = buildUser(1L, "testUser", ACTIVE);
        when(userRepository.findByUsername(activeUser.getUsername())).thenReturn(activeUser);

        // when
        try {
            registrationService.registerUser(buildCreateUserCommand(activeUser, true));

            // then
            fail("Expected to throw exception but did not");
        } catch (UserAlreadyExistsException exc) {
            assertThat(exc.getUsername(), is(activeUser.getEmailAddress().getEmail()));
        }
        assertThat(eventService.queryPublishedEventsOfType(ApplicationEvent.class).size(), equalTo(0));
        assertThat(eventService.queryPublishedEventsAfterTransactionOfType(ApplicationEvent.class).size(), equalTo(0));
    }

    // Existing User Tests
    @Test
    public void shouldUseExistingActivationKeyForAwaitingActivationUser() {

        //given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(basicUser);
        when(userService.createOrUpdateUser(Matchers.<CreateUserCommand>any())).thenReturn(basicUser);

        UserActivationKey existingUserActivationKey = UserActivationKey.builder()
                .withId(1L).withKey(TEST_KEY).withExpiryDate(NOW.plusDays(10).toDate()).withUser(basicUser).build();

        when(activationKeyRepository.findKeysByUserId(basicUser.getId())).thenReturn(singletonList(existingUserActivationKey));

        //when
        registrationService.registerUser(buildCreateUserCommand(basicUser, true));

        //then
        verify(keyGeneratorService, never()).generateKey(anyString(), anyString(), anyLong());
        assertActivationRequestedEventRaised(ON_REGISTRATION, existingUserActivationKey);
    }

    @Test
    public void shouldRetrieveTheLatestKeyThatHasNotYetExpired() {
        //given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(basicUser);
        when(userService.createOrUpdateUser(Matchers.<CreateUserCommand>any())).thenReturn(basicUser);

        UserActivationKey originalActivationKey = UserActivationKey.builder()
                .withId(1L)
                .withExpiryDate(NOW.plusDays(4).toDate())
                .withKey("OLD_KEY")
                .withUser(basicUser).build();

        UserActivationKey newUserActivationKey = UserActivationKey.builder()
                .withId(2L)
                .withKey("TEST_KEY")
                .withExpiryDate(NOW.plusDays(10).toDate())
                .withUser(basicUser).build();

        when(activationKeyRepository.findKeysByUserId(basicUser.getId())).thenReturn(
                asList(newUserActivationKey, originalActivationKey));

        //when
        registrationService.registerUser(buildCreateUserCommand(basicUser, true));

        //then
        verify(keyGeneratorService, never()).generateKey(anyString(), anyString(), anyLong());
        assertActivationRequestedEventRaised(ON_REGISTRATION, newUserActivationKey);
    }

    @Test
    public void shouldCreateNewActivationKeyWhenExistingKeysHaveExpiredForRegisterUser() {

        //given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(basicUser);
        when(userService.createOrUpdateUser(Matchers.<CreateUserCommand>any())).thenReturn(basicUser);

        Date dateInPast = NOW.minusDays(1).toDate();

        UserActivationKey.Builder userActivationKeyBuilder = UserActivationKey.builder()
                .withId(1L)
                .withKey(TEST_KEY)
                .withExpiryDate(dateInPast)
                .withUser(basicUser);

        List<UserActivationKey> userActivationKeys = singletonList(userActivationKeyBuilder.build());
        when(activationKeyRepository.findKeysByUserId(anyLong())).thenReturn(userActivationKeys);

        //when
        User registeredUser = registrationService.registerUser(buildCreateUserCommand(basicUser, true));

        //then
        verify(activationKeyRepository, never()).delete(userActivationKeys);
        verify(keyGeneratorService).generateKey(anyString(), anyString(), anyLong());

        UserActivationKey newUserActivationKey = userActivationKeyBuilder.withExpiryDate(NOW.plusDays(14).toDate()).build();
        verify(activationKeyRepository, times(1)).save(newUserActivationKey);

        assertActivationRequestedEventRaised(ON_REGISTRATION, registeredUser);
    }

    /*
     * ******************************
     * registerOrUpdateUser TESTS
     * ******************************
     */

    @Test
    public void testRegisterOrUpdateUserCreationOrder() {

        // given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(null);
        when(userService.createOrUpdateUser(any())).thenReturn(basicUser);

        CreateUserCommand command = buildCreateUserCommand(basicUser, true);

        // when
        User user = registrationService.registerOrUpdateUser(command);

        // then
        InOrder inOrderObj = inOrder(userRepository, userService);
        inOrderObj.verify(userRepository).findByUsername(basicUser.getUsername());
        inOrderObj.verify(userService).createOrUpdateUser(command);
        assertUserEqualsCommand(user, command);
    }

    @Test
    public void testRegisterOrUpdateExistingUser() {

        // given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(basicUser);
        when(userService.createOrUpdateUser(any())).thenReturn(basicUser);

        CreateUserCommand command = buildCreateUserCommand(basicUser, true);

        // when
        User user = registrationService.registerOrUpdateUser(command);

        // then
        assertUserEqualsCommand(user, command);
    }

    @Test
    public void testRegisterOrUpdateNewUser() {

        // given
        User basicUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(null);
        when(userService.createOrUpdateUser(any())).thenReturn(basicUser);

        CreateUserCommand command = buildCreateUserCommand(basicUser, true);

        // when
        User user = registrationService.registerOrUpdateUser(command);

        // then
        assertUserEqualsCommand(user, command);
        assertCreateEmailEventRaised(user, TEST_KEY);
    }

    // Resent Activation Email

    @Test(expected = UserNotFoundException.class)
    public void errorOnResendActivationFailsWhenUserNotFound() {

        when(userService.getUser("user1")).thenThrow(new UserNotFoundException(1L));

        registrationService.resendActivationEmail("user1", false);
    }

    @Test(expected = UserIsNotAwaitingActivationException.class)
    public void resendActivationFailsWhenUserIsNotAwaitingActivation() {
        User existingUser = new User();
        existingUser.setUsername(createEmailAddress("user1"));
        existingUser.setStatus(UserStatus.DEACTIVATED);
        when(userService.getUser("user1")).thenReturn(existingUser);

        registrationService.resendActivationEmail("user1", false);
    }

    @Test
    public void shouldCreateNewActivationKeyWhenExistingKeysHaveExpiredForResendActivation() {
        User existingUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userService.getUser(existingUser.getUsername())).thenReturn(existingUser);

        UserActivationKey expiredUserActivationKey = UserActivationKey.builder()
                .withKey(TEST_KEY).withExpiryDate(NOW.minus(Days.days(ACTIVE_DAYS+1)).toDate()).withUser(existingUser).build();

        when(activationKeyRepository.findKeysByUserId(existingUser.getId()))
                .thenReturn(Lists.newArrayList(expiredUserActivationKey));

        // when
        registrationService.resendActivationEmail(existingUser.getUsername(), false);

        // then
        assertActivationRequestedEventRaised(ON_REGISTRATION, existingUser);

        UserActivationKey newUserActivationKey = UserActivationKey.builder()
                .withId(1L)
                .withKey(TEST_KEY)
                .withExpiryDate(NOW.plus(Days.days(ACTIVE_DAYS)).toDate())
                .withUser(existingUser)
                .build();

        verify(keyGeneratorService).generateKey(anyString(), anyString(), anyLong());
        verify(activationKeyRepository, never()).delete(Lists.newArrayList(expiredUserActivationKey));
        verify(activationKeyRepository, never()).flush();
        verify(activationKeyRepository).save(newUserActivationKey);
    }

    @Test
    public void resendActivationSucceedsNormally() {
        User existingUser = buildUser(1L, "testUser", AWAITING_ACTIVATION);
        when(userService.getUser(existingUser.getUsername())).thenReturn(existingUser);

        UserActivationKey activationKey = newActivationKey(existingUser);
        when(activationKeyRepository.findKeysByUserId(existingUser.getId())).thenReturn(singletonList(activationKey));

        // when
        registrationService.resendActivationEmail(existingUser.getUsername(), false);

        // then
        assertActivationRequestedEventRaised(ON_REGISTRATION, existingUser);
    }

    // these tests are horrible, make work again after mockito upgrade
    private UserActivationKey newActivationKey(User user) {
        return userActivationKeyBuilder(1L, TEST_KEY)
                    .withUser(user)
                    .withExpiryDate(NOW.plus(Days.days(14)).toDate())
                .build();
    }

    // USER ACTIVATION TESTS

    @Test(expected = UserActivationKeyUnrecognisedException.class)
    public void activateUserFailsIfKeyIsNotFound() {
        when(activationKeyRepository.findByKey(anyString())).thenReturn(null);
        registrationService.activateUser("unknown_key");
    }

    @Test(expected = UserActivationKeyExpiredException.class)
    public void activateUserFailsIfRecognisedKeyHasExpired() {

        UserActivationKey activationKey = userActivationKeyBuilder(1L, TEST_KEY,2L, "testUser")
                .withExpiryDate(EXPIRED_ACTIVATION_KEY_EXPIRY_DATE.toDate()).build();

        when(activationKeyRepository.findByKey(activationKey.getKey())).thenReturn(activationKey);
        registrationService.activateUser(activationKey.getKey());
    }

    @Test
    public void activateUserSucceedsWhenKeyIsRecognisedAndNotExpired() {
        User basicUser = createUser(2L, "testUser");

        UserActivationKey activationKey = getUserActivationKey(basicUser);

        when(activationKeyRepository.findByKey(activationKey.getKey())).thenReturn(activationKey);
        when(activationKeyRepository.findKeysByUserId(basicUser.getId())).thenReturn(singletonList(activationKey));

        User user = registrationService.activateUser(activationKey.getKey());
        verify(activationKeyRepository).delete(singletonList(activationKey));

        assertThat(user.getId(), equalTo(basicUser.getId()));
        assertThat(user.getUsername(), equalTo(basicUser.getUsername()));
        assertThat(user.getStatus(), equalTo(UserStatus.ACTIVE));
        assertThat(user.getActivationDate(), is(not(nullValue())));
    }

    private UserActivationKey getUserActivationKey(User basicUser) {
        return userActivationKeyBuilder(1L, TEST_KEY)
                    .withUser(basicUser)
                    .withExpiryDate(UNEXPIRED_ACTIVATION_KEY_EXPIRY_DATE.toDate()).build();
    }

    @Test
    public void activateUserFiresOffEvent() {
        User basicUser = buildUser(2L, "testUser", AWAITING_ACTIVATION);

        UserActivationKey activationKey = userActivationKeyBuilder(1L, TEST_KEY)
                .withUser(basicUser)
                .withExpiryDate(UNEXPIRED_ACTIVATION_KEY_EXPIRY_DATE.toDate()).build();

        when(activationKeyRepository.findByKey(activationKey.getKey())).thenReturn(activationKey);
        User user = registrationService.activateUser(activationKey.getKey());
        ArgumentCaptor<BeforeUserActivatedEvent> eventCaptor = ArgumentCaptor.forClass(BeforeUserActivatedEvent.class);
        verify(eventService).publish(eventCaptor.capture());
        BeforeUserActivatedEvent event = eventCaptor.getValue();
        assertThat(event.getAggregate(), equalTo(user));
    }

    /**
     * ********************* GET USER ACTIVATION KEY TESTS *********************
     */

    @Test(expected = UserActivationKeyUnrecognisedException.class)
    public void getUserActivationKeyFailsIfKeyIsNotFound() {
        when(activationKeyRepository.findByKey(anyString())).thenReturn(null);
        registrationService.getAndValidateUserActivationKey("anyUSer", "unknown_key");
    }

    @Test(expected = UserActivationKeyUnrecognisedException.class)
    public void getUserActivationKeyFailsIfKeyIsNotForUser() {
        User basicUser = buildUser(2L, "testUser", AWAITING_ACTIVATION);

        UserActivationKey activationKey = userActivationKeyBuilder(1L, TEST_KEY)
                .withUser(basicUser).withExpiryDate(UNEXPIRED_ACTIVATION_KEY_EXPIRY_DATE.toDate()).build();

        when(activationKeyRepository.findByKey(activationKey.getKey())).thenReturn(activationKey);
        registrationService.getAndValidateUserActivationKey("differentUser", activationKey.getKey());
    }

    @Test(expected = UserActivationKeyExpiredException.class)
    public void getUserActivationKeyFailsIfRecognisedKeyHasExpired() {
        User basicUser = buildUser(2L, "testUser", AWAITING_ACTIVATION);

        UserActivationKey activationKey = userActivationKeyBuilder(1L, TEST_KEY)
                .withUser(basicUser).withExpiryDate(EXPIRED_ACTIVATION_KEY_EXPIRY_DATE.toDate()).build();

        when(activationKeyRepository.findByKey(activationKey.getKey())).thenReturn(activationKey);
        registrationService.getAndValidateUserActivationKey(basicUser.getUsername(), activationKey.getKey());
    }

    @Test(expected = UserAlreadyActivatedException.class)
    public void getUserActivationKeyFailsWhenUserIsAlreadyActivated() {
        User basicUser = buildUser(2L, "testUser", ACTIVE);

        UserActivationKey activationKey = userActivationKeyBuilder(1L, TEST_KEY)
                .withUser(basicUser).withExpiryDate(UNEXPIRED_ACTIVATION_KEY_EXPIRY_DATE.toDate()).build();
        when(activationKeyRepository.findByKey(activationKey.getKey())).thenReturn(activationKey);
        registrationService.getAndValidateUserActivationKey(basicUser.getUsername(), activationKey.getKey());
    }

    @Test
    public void getUserActivationKeyReturnedWhenKeyIsRecognisedAndNotExpired() {
        User basicUser = buildUser(2L, "testUser", AWAITING_ACTIVATION);

        UserActivationKey activationKey = userActivationKeyBuilder(1L, TEST_KEY)
                .withUser(basicUser).withExpiryDate(UNEXPIRED_ACTIVATION_KEY_EXPIRY_DATE.toDate()).build();

        when(activationKeyRepository.findByKey(activationKey.getKey())).thenReturn(activationKey);
        UserActivationKey returnedActivationKey = registrationService.getAndValidateUserActivationKey(basicUser.getUsername(), activationKey.getKey());
        assertThat(returnedActivationKey.getUser().getUsername(), equalTo(basicUser.getUsername()));
        assertThat(returnedActivationKey.getKey(), equalTo(activationKey.getKey()));
    }

    @Test
    public void shouldSendActivatedEventsWhenUserIsAutoActivated() {
        //given
        User basicUser = buildUser(2L, "testUser", AWAITING_ACTIVATION);
        when(userRepository.findByUsername(basicUser.getUsername())).thenReturn(basicUser);
        when(userService.createOrUpdateUser(ArgumentMatchers.any())).thenReturn(basicUser);

        CreateUserCommand request = buildCreateUserCommand(basicUser, true);
        request.setUserCreationMethod(UserCreationMethod.POST_AD_FLOW);
        request.setActivateOnRegistration(true);

        //when
        registrationService.registerUser(request);

        //then
        List<BeforeUserActivatedEvent> publishedEvents = eventService.queryPublishedEventsOfType(BeforeUserActivatedEvent.class);
        assertThat(publishedEvents.size(), org.hamcrest.Matchers.equalTo(1));

        List<AfterUserActivatedEvent> publishedEventsAfterTransaction = eventService.
                queryPublishedEventsAfterTransactionOfType(AfterUserActivatedEvent.class);
        assertThat(publishedEventsAfterTransaction.size(), org.hamcrest.Matchers.equalTo(1));

        List<ActivationRequestedEvent> activationRequestedEvents
                = eventService.queryPublishedEventsAfterTransactionOfType(ActivationRequestedEvent.class);
        assertTrue(activationRequestedEvents.isEmpty());

    }

    @Test
    public void shouldSendActivationEventWhenUserBeingActivated() {
        //given
        User basicUser = buildUser(2L, "testUser", AWAITING_ACTIVATION);

        when(clock.getDateTime()).thenReturn(NOW.minusDays(15)).thenReturn(NOW);
        UserActivationKey userActivationKey = UserActivationKey.builder()
                .withKey(TEST_KEY).withExpiryDate(NOW.toDate()).withUser(basicUser).build();

        when(activationKeyRepository.findByKey(userActivationKey.getKey())).thenReturn(userActivationKey);

        //when
        registrationService.activateUser(userActivationKey.getKey());

        //then
        List<BeforeUserActivatedEvent> publishedEvents = eventService.queryPublishedEventsOfType(BeforeUserActivatedEvent.class);
        assertThat(publishedEvents.size(), org.hamcrest.Matchers.equalTo(1));
        assertThat(publishedEvents.get(0).getAggregate(), equalTo(basicUser));

        List<AfterUserActivatedEvent> publishedEventsAfterTransaction = eventService.queryPublishedEventsAfterTransactionOfType(AfterUserActivatedEvent.class);
        assertThat(publishedEventsAfterTransaction.size(), org.hamcrest.Matchers.equalTo(1));
        assertThat(publishedEventsAfterTransaction.get(0).getAggregate(), equalTo(basicUser));
    }

    private EmailAddress createEmailAddress(String username) {
        EmailAddress emailAddress = new EmailAddress();
        emailAddress.setEmail(username);
        emailAddress.setId(1L);
        return emailAddress;
    }

    private void assertActivationRequestedEventRaised(UserActivationType activationType, User user) {
        Date expectedExpiryDate = NOW.plus(Days.days(14)).toDate();
        UserActivationKey userActivationKey = UserActivationKey.builder()
                .withUser(user)
                .withExpiryDate(expectedExpiryDate)
                .withKey(TEST_KEY)
                .withId(1L)
                .build();
        assertActivationRequestedEventRaised(activationType,  userActivationKey);
    }

    private void assertActivationRequestedEventRaised(UserActivationType activationType, UserActivationKey expected) {
        List<ActivationRequestedEvent> events = eventService.queryPublishedEventsAfterTransactionOfType(ActivationRequestedEvent.class);
        ActivationRequestedEvent event = events.get(0);
        assertThat(event.getActivationType(), equalTo(activationType));
        UserActivationKey activationKey = event.getUserActivationKey();
        assertThat(activationKey.getUser(), equalTo(expected.getUser()));
        assertThat(activationKey.getKey(), equalTo(expected.getKey()));
        assertThat(activationKey.getExpiryDate(), equalTo(expected.getExpiryDate()));
        assertThat(activationKey.getId(), equalTo(expected.getId()));
    }

    private void assertCreateEmailEventRaised(User user, String activationKey) {
        List<CreatePasswordEvent> events = eventService.queryPublishedEventsAfterTransactionOfType(CreatePasswordEvent.class);
        CreatePasswordEvent event = events.get(0);
        assertThat(event.getUser(), equalTo(user));
        assertThat(event.getActivationKey().getKey(), equalTo(activationKey));
    }

    private CreateUserCommand buildCreateUserCommand(User user, boolean marketing) {
        CreateUserCommand request = new CreateUserCommand();
        request.setFirstName(user.getFirstName());
        request.setLastName(user.getLastName());
        request.setEmailAddress(user.getEmailAddress().getEmail());
        request.setContactPhone(user.getContactPhone());
        request.setPlainTextPassword(user.getPassword());
        request.setOptInMarketing(marketing);
        return request;
    }


    private void assertUserEqualsCommand(User user, CreateUserCommand command) {
        assertThat(command.getEmailAddress(), equalTo(user.getEmailAddress().getEmail()));
        assertThat(command.getFirstName(), equalTo(user.getFirstName()));
        assertThat(command.getLastName(), equalTo(user.getLastName()));
        assertThat(command.getContactPhone(), equalTo(user.getContactPhone()));
    }

    private AdvertRequest generateBasicCreateAdvertRequest(Long userId) {
        AdvertRequest createAdvertRequest = new AdvertRequest();
        createAdvertRequest.setCategoryId(1L);
        createAdvertRequest.setPostcode("SW18 5AS");
        createAdvertRequest.setContactUrl("http://www.gumtree.com");
        createAdvertRequest.setContactEmail("<EMAIL>");
        createAdvertRequest.setContactTelephone("01632 960001");
        createAdvertRequest.setDescription("A test description");
        createAdvertRequest.setTitle("A test title");
        createAdvertRequest.setAccountId(1L);
        createAdvertRequest.setIp("127.0.0.1");
        return createAdvertRequest;
    }
}
