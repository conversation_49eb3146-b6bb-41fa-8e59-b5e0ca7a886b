package com.gumtree.seller.service.postcode.impl;

import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.location.entity.Outcode;
import com.gumtree.seller.domain.location.entity.Postcode;
import com.gumtree.seller.domain.postcode.exception.PostcodeValidationException;
import com.gumtree.seller.repository.location.OutcodeRepository;
import com.gumtree.seller.repository.location.PostcodeRepository;
import com.gumtree.seller.service.location.LocationService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PostcodeServiceImplTest {

    private static final String POSTCODE = "TW91EH";
    private static final String OUTCODE = "TW90XX";
    private static final String INVALID = "BlahBlahBlah";

    @Mock
    private PostcodeRepository postcodeRepository;

    @Mock
    private OutcodeRepository outcodeRepository;

    @Mock
    private LocationService locationService;

    @InjectMocks
    private PostcodeServiceImpl postcodeService;
    private Location location;

    @Before
    public void setup() {
        location = new Location();
        location.setId(1L);
        when(locationService.getLevelForLocation(1L)).thenReturn(99);
    }

    @Test
    public void testShouldReturnLocationsForValidOutcodeOnly() {
        Outcode outcode = new Outcode();
        outcode.getLocations().add(location);

        when(postcodeRepository.findByPostcode(anyString())).thenReturn(null);
        when(outcodeRepository.findByOutcode(anyString())).thenReturn(outcode);

        Location location = postcodeService.findLeafLocationFromPostcode(OUTCODE);

        assertNotNull(location);
    }

    @Test
    public void testShouldReturnLocationsForValidPostcode() {
        Postcode postcode = new Postcode();
        postcode.getLocations().add(location);

        when(postcodeRepository.findByPostcode(anyString())).thenReturn(postcode);

        Location location = postcodeService.findLeafLocationFromPostcode(POSTCODE);

        assertNotNull(location);
    }

    @Test(expected = PostcodeValidationException.class)
    public void testShouldThrowPostcodeValidationExceptionForUnrecognisedPostcode() {
        when(postcodeRepository.findByPostcode(anyString())).thenReturn(null);
        when(outcodeRepository.findByOutcode(anyString())).thenReturn(null);

        postcodeService.findLeafLocationFromPostcode(OUTCODE);
    }

    @Test(expected = PostcodeValidationException.class)
    public void testShouldThrowPostcodeValidationExceptionForInvalidPostcode() {
        postcodeService.findLeafLocationFromPostcode(INVALID);
    }


}
