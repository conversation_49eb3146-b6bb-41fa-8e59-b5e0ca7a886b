package com.gumtree.seller.service.monetary;

import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.order.entity.OrderItem;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class MonetaryUtilsTest {

    private static final Long GUERNSEY = 11002842L;
    private static final Long ISLE_OF_MAN = 11002843L;
    private static final Long JERSEY = 11002844L;
    private static final Long LONDON = 10000344L;

    private MonetaryUtils monetaryUtils;

    @Before
    public void setUp() throws Exception {
        monetaryUtils = new MonetaryUtils();
        ReflectionTestUtils.setField(monetaryUtils, "unitedKingdomVatRate", 20.0);
        monetaryUtils.initialiseVatService();
    }

    @Test
    public void advertInGuernseyReturnsZeroRateVatService() {
        Advert advert = createAdvert(GUERNSEY);

        assertEquals(new BigDecimal(0L).setScale(6, BigDecimal.ROUND_HALF_DOWN),
                monetaryUtils.getInternalVatFromIncPrice(100L, advert));
        assertEquals(new BigDecimal(1L).setScale(6, BigDecimal.ROUND_HALF_DOWN),
                monetaryUtils.getInternalExcVatFromIncPrice(100L, advert));
    }

    @Test
    public void advertInIsleOrManReturnsZeroRateVatService() {
        Advert advert = createAdvert(ISLE_OF_MAN);

        assertEquals(new BigDecimal(0L).setScale(6, BigDecimal.ROUND_HALF_DOWN),
                monetaryUtils.getInternalVatFromIncPrice(100L, advert));
        assertEquals(new BigDecimal(1L).setScale(6, BigDecimal.ROUND_HALF_DOWN),
                monetaryUtils.getInternalExcVatFromIncPrice(100L, advert));
    }

    @Test
    public void advertInJerseyReturnsZeroRateVatService() {
        Advert advert = createAdvert(JERSEY);

        assertEquals(new BigDecimal(0L).setScale(6, BigDecimal.ROUND_HALF_DOWN),
                monetaryUtils.getInternalVatFromIncPrice(100L, advert));
        assertEquals(new BigDecimal(1L).setScale(6, BigDecimal.ROUND_HALF_DOWN),
                monetaryUtils.getInternalExcVatFromIncPrice(100L, advert));
    }

    @Test
    public void advertInLondonReturnsNonZeroRateVatService() {
        Advert advert = createAdvert(LONDON);

        assertEquals(20.0, monetaryUtils.getInternalVatFromIncPrice(12000L, advert).doubleValue(), 0);
        assertEquals(100.0, monetaryUtils.getInternalExcVatFromIncPrice(12000L, advert).doubleValue(), 0);
    }

    @Test
    public void advertInNoLocationReturnsNonZeroRateVatService() {
        Advert advert = new Advert();
        advert.setHierarchicalLocations(null);

        assertEquals(20.0, monetaryUtils.getInternalVatFromIncPrice(12000L, advert).doubleValue(), 0);
        assertEquals(100.0, monetaryUtils.getInternalExcVatFromIncPrice(12000L, advert).doubleValue(), 0);
    }

    @Test
    public void convert1PenceToPounds() {
        assertEquals(0.01, monetaryUtils.convertPenceToPounds(1L).doubleValue(), 0);
    }

    @Test
    public void convert100PenceToPounds() {
        assertEquals(1.00, monetaryUtils.convertPenceToPounds(100L).doubleValue(), 0);
    }

    @Test
    public void convert999999PenceToPounds() {
        assertEquals(9999.99, monetaryUtils.convertPenceToPounds(999999L).doubleValue(), 0);
    }

    @Test
    public void getTotalVat() {
        List<OrderItem> orderItems = new ArrayList<OrderItem>();
        orderItems.add(createOrderItem(500L, GUERNSEY));
        orderItems.add(createOrderItem(1000L, LONDON));
        orderItems.add(createOrderItem(50000L, LONDON));
        orderItems.add(createOrderItem(300L, JERSEY));

        assertEquals(8500L, monetaryUtils.getTotalVat(orderItems).longValue());
    }

    private Advert createAdvert(Long locationId) {
        Advert advert = new Advert();

        Location location = mock(Location.class);
        when(location.getId()).thenReturn(locationId);
        Set<Location> locations = new HashSet<Location>();
        locations.add(location);
        advert.setHierarchicalLocations(locations);

        return advert;
    }

    private OrderItem createOrderItem(Long priceIncVat, Long locationId) {
        OrderItem item = mock(OrderItem.class);
        Advert advert = createAdvert(locationId);
        when(item.getAdvert()).thenReturn(advert);
        when(item.getPriceIncVat()).thenReturn(priceIncVat);
        return item;
    }
}
