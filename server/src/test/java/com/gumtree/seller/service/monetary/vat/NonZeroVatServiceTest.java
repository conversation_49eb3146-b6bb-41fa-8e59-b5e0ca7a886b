package com.gumtree.seller.service.monetary.vat;

import org.junit.Test;

import java.math.BigDecimal;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.*;

public class NonZeroVatServiceTest {

    @Test
    public void basicGetTotalVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(20.0);

        Long totalVat = vatService.getTotalVatFromIncPrice(12000L);
        assertThat(totalVat, equalTo(2000L));
    }

    @Test
    public void complexGetTotalVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(34.0);

        Long totalVat = vatService.getTotalVatFromIncPrice(17000L);
        assertThat(totalVat, equalTo(4313L));
    }

    @Test
    public void getTotalVatFromIncPriceCalculatesVatCorrectlyWhenManyDecimals() {
        NonZeroVatService vatService = new NonZeroVatService(17.5);

        Long totalVat = vatService.getTotalVatFromIncPrice(19435L);
        assertThat(totalVat, equalTo(2895L));
    }

    @Test
    public void basicGetTotalExcVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(20.0);

        Long totalExcVat = vatService.getTotalExcVatFromIncPrice(12000L);
        assertThat(totalExcVat, equalTo(10000L));
    }

    @Test
    public void complexGetTotalExcVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(34.0);

        Long totalExcVat = vatService.getTotalExcVatFromIncPrice(17000L);
        assertThat(totalExcVat, equalTo(12687L));
    }

    @Test
    public void getTotalExcVatFromIncPriceCalculatesVatCorrectlyWhenManyDecimals() {
        NonZeroVatService vatService = new NonZeroVatService(17.5);

        Long totalExcVat = vatService.getTotalExcVatFromIncPrice(19435L);
        assertThat(totalExcVat, equalTo(16540L));
    }

    @Test
    public void basicGetInternalVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(20.0);

        BigDecimal internalVat = vatService.getInternalVatFromIncPrice(12000L);
        assertThat(internalVat, equalTo(new BigDecimal(20L).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }

    @Test
    public void complexGetInternalVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(34.0);

        BigDecimal internalVat = vatService.getInternalVatFromIncPrice(17000L);
        assertThat(internalVat, equalTo(new BigDecimal(43.134328).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }

    @Test
    public void getInternalVatFromIncPriceCalculatesVatCorrectlyWhenManyDecimals() {
        NonZeroVatService vatService = new NonZeroVatService(17.5);

        BigDecimal internalVat = vatService.getInternalVatFromIncPrice(19435L);
        assertThat(internalVat, equalTo(new BigDecimal(28.945745).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }

    @Test(expected = NullPointerException.class)
    public void nullGetInternalVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(20.0);

        BigDecimal internalVat = vatService.getInternalVatFromIncPrice(null);
        assertThat(internalVat, equalTo(new BigDecimal(20L).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }

    @Test
    public void zeroGetInternalVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(20.0);

        BigDecimal internalVat = vatService.getInternalVatFromIncPrice(0L);
        assertThat(internalVat, equalTo(new BigDecimal(0L).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }

    @Test
    public void basicGetInternalExcVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(20.0);

        BigDecimal internalVat = vatService.getInternalExcVatFromIncPrice(12000L);
        assertThat(internalVat, equalTo(new BigDecimal(100L).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }

    @Test
    public void complexGetInternalExcVatFromIncPriceCalculatesVatCorrectly() {
        NonZeroVatService vatService = new NonZeroVatService(34.0);

        BigDecimal internalVat = vatService.getInternalExcVatFromIncPrice(17000L);
        assertThat(internalVat, equalTo(new BigDecimal(126.865672).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }

    @Test
    public void getInternalExcVatFromIncPriceCalculatesVatCorrectlyWhenManyDecimals() {
        NonZeroVatService vatService = new NonZeroVatService(17.5);

        BigDecimal internalVat = vatService.getInternalExcVatFromIncPrice(19435L);
        assertThat(internalVat, equalTo(new BigDecimal(165.404255).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }
}
