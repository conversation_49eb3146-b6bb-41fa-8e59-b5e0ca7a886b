package com.gumtree.seller.service.location.impl;

import com.google.common.collect.Lists;
import com.gumtree.api.PostcodeFailure;
import com.gumtree.api.PostcodeRecognition;
import com.gumtree.api.PostcodeResponse;
import com.gumtree.common.util.location.LocationResolutionResult;
import com.gumtree.seller.domain.location.TestLocation;
import com.gumtree.seller.domain.location.TestLocations;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.location.entity.Outcode;
import com.gumtree.seller.domain.location.entity.Postcode;
import com.gumtree.seller.domain.postcode.exception.PostcodeValidationException;
import com.gumtree.seller.repository.location.PostcodeLocator;
import com.gumtree.seller.service.location.LocationModel;
import com.gumtree.seller.service.location.LocationService;
import org.hamcrest.MatcherAssert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static com.gumtree.seller.domain.location.TestLocation.LATITUDE;
import static com.gumtree.seller.domain.location.TestLocation.LONGITUDE;
import static com.gumtree.seller.domain.location.TestLocation.RADIUS;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LocationFacadeImplTest {

    private static final Long LOCATION_ID = 1L;

    @InjectMocks private LocationFacadeImpl facade;
    @Mock private LocationModel locationModel;
    @Mock private PostcodeLocator postcodeLocator;
    @Mock private LocationService locationService;

    @Before
    public void setup() {
        when(locationService.getLevelForLocation(1L)).thenReturn(1);
    }

    @Test
    public void testRecognisedPostcode() {
        Postcode recognisedPostcode = new Postcode();
        recognisedPostcode.addLocation(location(1L));

        // given
        when(postcodeLocator.findPostcode("TW9 1EH")).thenReturn(recognisedPostcode);

        // when
        PostcodeResponse response = facade.validatePostcode("TW9 1EH");

        // then
        assertThat(response.getPostcodeRecognition()).isEqualTo((PostcodeRecognition.FULL_POSTCODE));
        assertThat(response.getLocationId()).isEqualTo(1L);
    }

    @Test
    public void testRecognisedPostcodeWithLowerCase() {
        Postcode recognisedPostcode = new Postcode();
        recognisedPostcode.addLocation(location(1L));

        // given
        when(postcodeLocator.findPostcode("tw9 1EH")).thenReturn(recognisedPostcode);

        // when
        PostcodeResponse response = facade.validatePostcode("tw9 1EH");

        // then
        assertThat(response.getPostcodeRecognition()).isEqualTo((PostcodeRecognition.FULL_POSTCODE));
        assertThat(response.getLocationId()).isEqualTo(1L);
    }

    @Test
    public void testRecognisedOutcode() {
        Outcode recognisedOutcode = new Outcode();
        recognisedOutcode.getLocations().add(location(1L));

        // given
        when(postcodeLocator.findOutcode("W30 0NU")).thenReturn(recognisedOutcode);

        // when
        PostcodeResponse response = facade.validatePostcode("W30 0NU");

        // then
        assertThat(response.getPostcodeRecognition()).isEqualTo(PostcodeRecognition.OUTCODE_ONLY);
        assertThat(response.getLocationId()).isEqualTo(1L);
    }

    @Test
    public void testEmptyPostcodeThrowsError() {
        try {
            facade.validatePostcode("");
            throw new IllegalStateException("Should not have got here");
        } catch (PostcodeValidationException pve) {
            assertThat(pve.getPostcodeFailure()).isEqualTo(PostcodeFailure.MISSING);
        }
    }

    @Test
    public void testInvalidFormatPostcodeThrowsError() {
        try {
            facade.validatePostcode("I am not a post code lol");
            throw new IllegalStateException("Should not have got here");
        } catch (PostcodeValidationException pve) {
            MatcherAssert.assertThat(pve.getPostcodeFailure(), equalTo(PostcodeFailure.INVALID));
        }
    }

    @Test
    public void testUnrecognisedPostcodeThrowsError() {
        try {
            facade.validatePostcode("SW99 9ZZ");
            throw new IllegalStateException("Should not have got here");
        } catch (PostcodeValidationException pve) {
            MatcherAssert.assertThat(pve.getPostcodeFailure(), equalTo(PostcodeFailure.UNRECOGNISED));
        }
    }

    @Test
    public void shouldResolveLocationToUnknownLocation() {
        // given
        String input = "london";
        when(locationService.getAllLocations()).thenReturn(new LocationModelImpl(Lists.<Location>newArrayList()));

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(LocationResolutionResult.unknownLocation(input));
    }

    @Test
    public void shouldResolveLocationToPostcodeLocationIfPostcodeIsRecognized() {
        String input = "TW209PZ";
        // given
        when(postcodeLocator.findPostcode(input)).thenReturn(postcode());
        when(locationService.getLevelForLocation(LOCATION_ID)).thenReturn(1);

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(
                LocationResolutionResult.postcodeLocationBuilder()
                        .withLocationId(LOCATION_ID).withGeoPoint(LATITUDE, LONGITUDE).build());
    }

    @Test
    public void shouldResolveLocationToPostcodeLocationIfPostcodeIsRecognizedWhileIgnoringWhitespaces() {
        String input = "TW20 9PZ";
        // given
        when(postcodeLocator.findPostcode(input)).thenReturn(postcode());
        when(locationService.getLevelForLocation(LOCATION_ID)).thenReturn(1);

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(
                LocationResolutionResult.postcodeLocationBuilder()
                        .withLocationId(LOCATION_ID).withGeoPoint(LATITUDE, LONGITUDE).build());
    }

    @Test
    public void shouldResolveLocationToOutcodeLocationIfPostcodeIsNotRecognizedButOutcodeIs() {
        String input = "TW209PZ";
        // given
        Outcode outcode = new Outcode();
        when(postcodeLocator.findOutcode(input)).thenReturn(outcode);

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(LocationResolutionResult.outcodeLocation(input));
    }

    @Test
    public void shouldResolveLocationToOutcodeLocationIfPostcodeIsNotRecognizedButOutcodeIsWhileIgnoringWhitespaces() {
        String input = "TW20 9PZ";
        // given
        Outcode outcode = new Outcode();
        when(postcodeLocator.findOutcode(input)).thenReturn(outcode);

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(LocationResolutionResult.outcodeLocation(input));
    }

    @Test
    public void shouldResolveLocationToNamedLocation() {
        String input = "london";
        // given
        List<Location> locations = new TestLocations().add(new TestLocation().withId(1L).withName("London")).create();
        when(locationService.getAllLocations()).thenReturn(new LocationModelImpl(locations));

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(LocationResolutionResult.namedLocationBuilder()
                .withLocationId(LOCATION_ID).withGeoPoint(LATITUDE, LONGITUDE).withRadius(RADIUS).build());
    }

    @Test
    public void shouldResolveLocationToNamedLocationForLocationHierarchy() {
        String input = "Richmond, London";
        // given
        List<Location> locations = new TestLocations()
                .add(new TestLocation().withId(1L).withName("Liverpool"))
                .add(new TestLocation().withId(2L).withName("Richmond"))
                .add(new TestLocation().withId(3L).withName("London").withZoomIns(2L))
                .create();

        when(locationService.getAllLocations()).thenReturn(new LocationModelImpl(locations));

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(LocationResolutionResult.namedLocationBuilder()
                .withLocationId(2L).withGeoPoint(LATITUDE, LONGITUDE).withRadius(RADIUS).build());
    }

    @Test
    public void shouldResolveLocationToNamedLocationForInvalidLocationHierarchy() {
        String input = "Richmond, Liverpool";

        // given
        List<Location> locations = new TestLocations()
                .add(new TestLocation().withId(1L).withName("Liverpool"))
                .add(new TestLocation().withId(2L).withName("Richmond"))
                .add(new TestLocation().withId(3L).withName("London").withZoomIns(2L))
                .create();

        when(locationService.getAllLocations()).thenReturn(new LocationModelImpl(locations));

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(LocationResolutionResult.unknownLocation(input));
    }

    @Test
    public void shouldResolveLocationToUnknownLocationForLocationWithNonMatchingDisplayName() {
        String input = "london";
        // given
        when(locationService.getAllLocations()).thenReturn(locationModel);
        when(locationModel.getByDisplayName(input)).thenReturn(Lists.<Location>newArrayList());

        // when
        LocationResolutionResult result = facade.resolveLocation(input);

        // then
        assertThat(result).isEqualTo(LocationResolutionResult.unknownLocation(input));
    }

    private Postcode postcode() {
        Postcode postcode = new Postcode();
        postcode.setLatitude(TestLocation.LATITUDE);
        postcode.setLongitude(TestLocation.LONGITUDE);
        postcode.addLocation(rootLocation());
        return postcode;
    }

    private Location rootLocation() {
        return location(LOCATION_ID);
    }

    private Location location(long id) {
        return new TestLocation().withId(id).create();
    }
}
