package com.gumtree.seller.service.attribute.impl;

import com.gumtree.seller.domain.advert.annotation.CoreAttribute;
import org.junit.Test;

import java.util.Collection;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class AttributeMetadataServiceImplTest {

    /**
     * AC.: verify that getCoreAttribute of the service AttributeMetadataServiceImpl get the list of Core Attribute
     * Given the instance of AttributeMetadataServiceImpl
     * When call the getCoreAttribute
     * Then the list should be populate
     * And the number of attribute discovered is 23 (in this way I have checked that also annotation
     * inside classes are captured)
     *
     */
    @Test
    public void shouldPopulateTheCoreAttributeList(){
        //given
        AttributeMetadataServiceImpl service = new AttributeMetadataServiceImpl();
        //when
        Collection<CoreAttribute> result = service.getCoreAttribute();
        //and
        assertThat(result.size(), equalTo(23));
    }
}
