package com.gumtree.seller.service.monetary.vat;

import org.junit.Test;

import java.math.BigDecimal;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class ZeroVatServiceTest {

    @Test
    public void basicGetTotalVatFromIncPriceCalculatesVatCorrectly() {
        ZeroVatService vatService = new ZeroVatService();

        Long totalVat = vatService.getTotalVatFromIncPrice(19435L);
        assertThat(totalVat, equalTo(0L));
    }

    @Test
    public void getTotalExcVatFromIncPriceCalculatesVatCorrectly() {
        ZeroVatService vatService = new ZeroVatService();

        Long totalExcVat = vatService.getTotalExcVatFromIncPrice(19435L);
        assertThat(totalExcVat, equalTo(19435L));
    }

    @Test
    public void getInternalVatFromIncPriceCalculatesVatCorrectly() {
        ZeroVatService vatService = new ZeroVatService();

        BigDecimal internalVat = vatService.getInternalVatFromIncPrice(19435L);
        assertThat(internalVat, equalTo(new BigDecimal(0).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }

    @Test
    public void getInternalExcVatFromIncPriceCalculatesVatCorrectly() {
        ZeroVatService vatService = new ZeroVatService();

        BigDecimal internalVat = vatService.getInternalExcVatFromIncPrice(19435L);
        assertThat(internalVat, equalTo(new BigDecimal(194.350000).setScale(6, BigDecimal.ROUND_HALF_DOWN)));
    }
}
