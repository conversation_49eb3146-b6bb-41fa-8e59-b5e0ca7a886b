package com.gumtree.seller.service.location.impl;

import com.google.common.collect.Lists;
import com.gumtree.seller.domain.location.TestLocation;
import com.gumtree.seller.domain.location.TestLocations;
import com.gumtree.seller.domain.location.entity.Location;
import com.gumtree.seller.domain.location.exception.LocationNotFoundException;
import com.gumtree.seller.repository.location.LocationRepository;
import com.gumtree.seller.service.location.LocationModel;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class LocationModelImplTest {

    private static LocationRepository locationRepository;

    private LocationModel model;
    private static Location location1;
    private static Location location2;

    @BeforeClass
    public static void createTestData() {
        location1 = createLocation(1L, Arrays.asList(2L,3L), Arrays.asList(2L,3L,4L), Arrays.asList(3L));
        location2 = createLocation(2L, Arrays.asList(1L,3L), Arrays.asList(2L), Arrays.asList(1L));
        List<Location> locations = new ArrayList<Location>();
        locations.add(location1);
        locations.add(location2);
        locations.add(createLocation(3L, new ArrayList<Long>(), Arrays.asList(6L), Arrays.asList(2L)));
        locations.add(createLocation(4L, Arrays.asList(5L,6L), Arrays.asList(3L), Arrays.asList(6L)));
        locations.add(createLocation(5L, Arrays.asList(4L, 6L), Arrays.asList(2L), Arrays.asList(4L)));
        locations.add(createLocation(6L, Arrays.asList(4L,5L), Arrays.asList(1L), Arrays.asList(5L)));

        locationRepository = mock(LocationRepository.class);
        when(locationRepository.findAll()).thenReturn(locations);
    }

    @Before
    public void before() {
        model = new LocationModelImpl(locationRepository);
    }

    @Test
    public void existsShouldReturnTrueForALocationThatExists() {
        assertThat(model.exists(1L), equalTo(true));
    }

    @Test
    public void existsShouldReturnFalseForALocationThatDoesNotExist() {
        assertThat(model.exists(7L), equalTo(false));
    }

    @Test
    public void getByIdReturnsLocation() {
        assertThat(model.getById(1L).getId(), equalTo(1L));
    }

    @Test(expected = LocationNotFoundException.class)
    public void getByIdThrowsExceptionWhenIdNotFound() {
        model.getById(99L);
    }

    @Test
    public void getAllReturnsAllLocations() {
        assertThat(model.getAll().size(), equalTo(6));
    }

    @Test
    public void getAllReturnsLocationsWithNearbys() {
        Collection<Location> locations = model.getAll();
        assertThat(locations.iterator().next().getNearbys().contains(2L), equalTo(true));
        assertThat(locations.iterator().next().getNearbys().contains(3L), equalTo(true));
        assertThat(locations.iterator().next().getNearbys().contains(5L), equalTo(false));
    }

    @Test
    public void getAllReturnsLocationsWithZoomIns() {
        Collection<Location> locations = model.getAll();
        assertThat(locations.iterator().next().getZoomIns().contains(2L), equalTo(true));
        assertThat(locations.iterator().next().getZoomIns().contains(3L), equalTo(true));
        assertThat(locations.iterator().next().getZoomIns().contains(4L), equalTo(true));
        assertThat(locations.iterator().next().getZoomIns().contains(5L), equalTo(false));
    }

    @Test
    public void getAllReturnsLocationsWithZoomOuts() {
        Collection<Location> locations = model.getAll();
        assertThat(locations.iterator().next().getZoomOuts().contains(3L), equalTo(true));
        assertThat(locations.iterator().next().getZoomOuts().contains(5L), equalTo(false));
    }

    @Test
    public void shouldBeChildOfParent() {
        // given
        List<Location> locations = new TestLocations()
                .add(new TestLocation().withId(0L).withZoomIns(1L, 2L))
                .add(new TestLocation().withId(1L).withZoomIns(3L))
                .add(new TestLocation().withId(2L))
                .add(new TestLocation().withId(3L))
                .create();

        model = new LocationModelImpl(locations);

        // when + then
        assertThat(model.isChild(locations.get(3), locations.get(1)), equalTo(true));
        assertThat(model.isChild(locations.get(3), locations.get(0)), equalTo(true));
        assertThat(model.isChild(locations.get(1), locations.get(0)), equalTo(true));
        assertThat(model.isChild(locations.get(2), locations.get(0)), equalTo(true));
        assertThat(model.isChild(locations.get(0), locations.get(1)), equalTo(false));
        assertThat(model.isChild(locations.get(1), locations.get(2)), equalTo(false));
        assertThat(model.isChild(locations.get(2), locations.get(1)), equalTo(false));
        assertThat(model.isChild(locations.get(2), null), equalTo(false));
        assertThat(model.isChild(null, locations.get(1)), equalTo(false));
    }

    @Test
    public void shouldBeChildOfAnyParent() {
        // given
        List<Location> locations = new TestLocations()
                .add(new TestLocation().withId(0L).withZoomIns(1L, 2L))
                .add(new TestLocation().withId(1L).withZoomIns(3L))
                .add(new TestLocation().withId(2L))
                .add(new TestLocation().withId(3L))
                .create();

        model = new LocationModelImpl(locations);

        // when + then
        assertThat(model.isChildOfAny(locations.get(3), Lists.newArrayList(locations.get(1), locations.get(2))), equalTo(true));
        assertThat(model.isChildOfAny(locations.get(0), Lists.newArrayList(locations.get(1), locations.get(2))), equalTo(false));
        assertThat(model.isChildOfAny(locations.get(0), Lists.<Location>newArrayList()), equalTo(false));
        assertThat(model.isChildOfAny(locations.get(0), Lists.<Location>newArrayList()), equalTo(false));
    }

    private static Location createLocation(Long id, List<Long> nearbys, List<Long> zoomIns, List<Long> zoomOuts) {
        Location location = new Location();
        location.setDisplayName(id.toString());
        ReflectionTestUtils.setField(location, "id", id);
        ReflectionTestUtils.setField(location, "nearbys", nearbys);
        ReflectionTestUtils.setField(location, "zoomIns", zoomIns);
        ReflectionTestUtils.setField(location, "zoomOuts", zoomOuts);
        return location;
    }
}
