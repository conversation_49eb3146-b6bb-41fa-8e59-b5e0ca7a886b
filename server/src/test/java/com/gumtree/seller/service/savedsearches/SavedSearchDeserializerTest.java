package com.gumtree.seller.service.savedsearches;

import com.gumtree.api.domain.savedsearches.SearchBean;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertFalse;

/**
 * Unit tests for the {@link SavedSearchDeserializer} class.
 */
public class SavedSearchDeserializerTest {

    private SavedSearchDeserializer deserializer;

    @Before
    public void setup() {
        deserializer = new SavedSearchDeserializer();
    }

    @Test
    public void emptyJsonCreatesEmptyBean() {
        // Given
        String json = "{}";
        // When
        SearchBean search = deserializer.fromJson(json);
        // Then
        assertNull(search.getKeywords());
    }

    @Test
    public void keywordsOnlyCreatesCorrectBean() {
        // Given
        String json = "{\"keywords\":\"my keywords here\"}";
        // When
        SearchBean search = deserializer.fromJson(json);
        // Then
        assertEquals("my keywords here", search.getKeywords());
    }

    @Test
    public void fullyLoadedJsonCreatesCorrectBean() {
        // Given
        String json = "{\"location\":\"my location\",\"keywords\":\"my keywords here\",\"categoryId\":100," +
                "\"imagesOnly\":true,\"urgentOnly\":true,\"featuredOnly\":true,\"price\":{\"maxValue\":500}," +
                "\"sellerType\":\"private\",\"vehicleFuelType\":\"fruit\"," +
                "\"vehicleRegistrationYear\":{\"minValue\":2011},\"vehicleBodyType\":\"saloon\"," +
                "\"vehicleModel\":\"carrot\",\"vehicleColour\":\"red\",\"vehicleMileage\":{\"maxValue\":60000}," +
                "\"vehicleTransmission\":\"manual\",\"vehicleMake\":\"turnip\"," +
                "\"vehicleEngineSize\":{\"minValue\":1000,\"maxValue\":2000},\"jobContractType\":\"perm\"," +
                "\"propertyType\":\"house\",\"propertyCouples\":\"what?\",\"propertyRoomType\":\"double\"," +
                "\"propertyNumBeds\":{\"minValue\":3}}";
        // When
        SearchBean search = deserializer.fromJson(json);
        // Then
        assertEquals((Long) 100L, search.getCategoryId());
        assertTrue(search.getFeaturedOnly());
        assertTrue(search.getImagesOnly());
        assertEquals("perm", search.getJobContractType());
        assertEquals("my keywords here", search.getKeywords());
        assertEquals("my location", search.getLocation());
        assertEquals((Long) 500L, search.getPrice().getMaxValue());
        assertNull(search.getPrice().getMinValue());
        assertEquals("what?", search.getPropertyCouples());
        assertEquals((Long) 3L, search.getPropertyNumBeds().getMinValue());
        assertNull(search.getPropertyNumBeds().getMaxValue());
        assertEquals("double", search.getPropertyRoomType());
        assertEquals("house", search.getPropertyType());
        assertEquals("private", search.getSellerType());
        assertTrue(search.getUrgentOnly());
        assertEquals("saloon", search.getVehicleBodyType());
        assertEquals("red", search.getVehicleColour());
        assertEquals((Long) 1000L, search.getVehicleEngineSize().getMinValue());
        assertEquals((Long) 2000L, search.getVehicleEngineSize().getMaxValue());
        assertEquals("fruit", search.getVehicleFuelType());
        assertEquals("turnip", search.getVehicleMake());
        assertNull(search.getVehicleMileage().getMinValue());
        assertEquals((Long) 60000L, search.getVehicleMileage().getMaxValue());
        assertEquals("carrot", search.getVehicleModel());
        assertEquals((Long) 2011L, search.getVehicleRegistrationYear().getMinValue());
        assertNull(search.getVehicleRegistrationYear().getMaxValue());
        assertEquals("manual", search.getVehicleTransmission());
    }
}
