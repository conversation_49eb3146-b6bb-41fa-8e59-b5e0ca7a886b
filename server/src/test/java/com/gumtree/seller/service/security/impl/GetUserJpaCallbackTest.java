package com.gumtree.seller.service.security.impl;

import com.gumtree.seller.domain.user.entity.User;
import org.junit.Test;
import org.mockito.InOrder;
import org.mockito.Matchers;

import javax.persistence.EntityManager;
import javax.persistence.FlushModeType;
import javax.persistence.TypedQuery;

import static org.hamcrest.CoreMatchers.sameInstance;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class GetUserJpaCallbackTest {

    @Test
    public void generatesCorrectQuery() {
        User user = new User();
        TypedQuery query = mock(TypedQuery.class);
        EntityManager em = mock(EntityManager.class);
        GetUserJpaCallback callback = new GetUserJpaCallback("testUser");
        when(query.getSingleResult()).thenReturn(user);
        when(em.createQuery(anyString(), Matchers.<Class<Object>>anyObject())).thenReturn(query);
        User returnedUser = callback.doInJpa(em);
        InOrder order = inOrder(em, query);
        order.verify(em).createQuery("SELECT u FROM User u WHERE u.username.email = :username", User.class);
        order.verify(query).setParameter("username", "testUser");
        order.verify(query).setFlushMode(FlushModeType.COMMIT);
        order.verify(query).getSingleResult();
        assertThat(returnedUser, sameInstance(user));
    }
}
