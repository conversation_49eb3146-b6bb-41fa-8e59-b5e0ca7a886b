package com.gumtree.seller.service.saveads;

import com.gumtree.common.properties.GtPropertiesInitializer;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SavedAdsMigrationServiceTest {
    private static final String TOPIC = "favourite-adverts-migration";
    @InjectMocks
    private SavedAdsMigrationService service;
    @Mock
    private Producer<Long, String> kafkaProducer;

    @BeforeClass
    public static void init() throws ConfigurationException {
        GtPropertiesInitializer.init("bapi");
    }

    @Test
    public void notifyAdvertAddedAndExceptionsAreSwallowed() {
        // given
        when(kafkaProducer.send(any(), any())).thenThrow(new RuntimeException("kafka add exception"));

        // when
        service.notifyAdvertAdded(1L, 2L);

        // then
        verify(kafkaProducer).send(eq(new ProducerRecord<>(TOPIC, 1L, "{\"action\":\"add\",\"user_id\":1,\"advert_id\":2}")), any(Callback.class));
    }

    @Test
    public void notifyAdvertRemovedAndExceptionsAreSwallowed() {
        // given
        when(kafkaProducer.send(any(), any())).thenThrow(new RuntimeException("kafka remove exception"));

        // when
        service.notifyAdvertRemoved(1L, 2L);

        // then
        verify(kafkaProducer).send(eq(new ProducerRecord<>(TOPIC, 1L, "{\"action\":\"remove\",\"user_id\":1,\"advert_id\":2}")), any(Callback.class));
    }

    @Test
    public void notifyAdvertsDeletedAndExceptionsAreSwallowed() {
        // given
        when(kafkaProducer.send(any(), any())).thenThrow(new RuntimeException("kafka delete exception"));

        // when
        service.notifyAdvertsDeleted(1L);

        // then
        verify(kafkaProducer).send(eq(new ProducerRecord<>(TOPIC, 1L, "{\"action\":\"delete\",\"user_id\":1}")), any(Callback.class));
    }
}
