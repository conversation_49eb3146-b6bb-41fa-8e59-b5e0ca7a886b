package com.gumtree.seller.service.security.impl;

import com.google.common.base.Optional;
import com.gumtree.common.util.security.exception.UnknownUserAccountException;
import com.gumtree.common.util.security.exception.UserAccountNotActiveException;
import com.gumtree.seller.domain.user.entity.ApiKey;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.exception.UserNotFoundException;
import com.gumtree.seller.service.user.ParticipatingUserInitializer;
import com.gumtree.seller.service.user.PasswordService;
import com.gumtree.seller.service.user.UserService;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.gumtree.seller.domain.user.entity.UserStatus.ACTIVE;
import static com.gumtree.seller.domain.user.entity.UserStatus.AWAITING_ACTIVATION;
import static com.gumtree.seller.domain.user.entity.UserStatus.DEACTIVATED;
import static com.gumtree.seller.util.DomainObjectBuilders.aUser;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultUserAuthenticationServiceTest {

    @InjectMocks private DefaultUserAuthenticationService userAuthenticationService;

    @Mock private UserService userService;
    @Mock private ParticipatingUserInitializer participatingUserInitializer;
    @Mock private PasswordService passwordService;

    @Test(expected = UnknownUserAccountException.class)
    public void throwsUnknownUserAccountExceptionForEmptyUsername() {
        when(userService.getUser(anyString())).thenThrow(new UserNotFoundException(""));

        userAuthenticationService.authenticate("", "somepassword");
    }

    @Test(expected = UnknownUserAccountException.class)
    public void throwsUnknownUserAccountExceptionForUnrecognisedUser() {
        when(userService.getUser(anyString())).thenThrow(new UserNotFoundException(""));

        userAuthenticationService.authenticate("unknown", "somepassword");
    }

    @Test(expected = IncorrectCredentialsException.class)
    public void throwsIncorrectCredentialsExceptionWhenPasswordDoesNotMatchForKnownUser() {
        // given
        User user = aUser().withStatus(ACTIVE).withPasswordAndSalt("vbwms4RaqyLW/L2tPmNVkQ/rY0z7zeOqRSv73eF1D9k=", "dGVzdHNhbHQ=").build();
        when(userService.getUser("knownuser")).thenReturn(user);
        when(passwordService.verifyCredentials(user, "notMyPassword")).thenReturn(Optional.<Long>absent());

        // when
        userAuthenticationService.authenticate("knownuser", "notMyPassword");
    }

    @Test(expected = UserAccountNotActiveException.class)
    public void throwsUserAccountNotActiveExceptionWhenUserIsAwaitingActivation() {
        User user = aUser().withStatus(AWAITING_ACTIVATION).withPasswordAndSalt("vbwms4RaqyLW/L2tPmNVkQ/rY0z7zeOqRSv73eF1D9k=", "dGVzdHNhbHQ=").build();
        when(userService.getUser("knownuser")).thenReturn(user);

        userAuthenticationService.authenticate("knownuser", "myPassword");
    }

    @Test(expected = UserAccountNotActiveException.class)
    public void shouldThrowsUserAccountNotActiveExceptionWhenUserIsDeactivated() {
        User user = aUser().withStatus(DEACTIVATED).withPasswordAndSalt("vbwms4RaqyLW/L2tPmNVkQ/rY0z7zeOqRSv73eF1D9k=", "dGVzdHNhbHQ=").build();
        when(userService.getUser("knownuser")).thenReturn(user);

        userAuthenticationService.authenticate("knownuser", "myPassword");
    }


    @Test(expected = IllegalArgumentException.class)
    public void throwsIllegalArgumentExceptionWhenUserApiKeysIsEmpty() {
        // given
        User user = aUser().withStatus(ACTIVE).withId(1L).withPasswordAndSalt("vbwms4RaqyLW/L2tPmNVkQ/rY0z7zeOqRSv73eF1D9k=", "dGVzdHNhbHQ=").build();
        when(userService.getUser("knownuser")).thenReturn(user);
        when(passwordService.verifyCredentials(user, "myPassword")).thenReturn(Optional.of(1234L));

        // when
        userAuthenticationService.authenticate("knownuser", "myPassword");
    }

    @Test(expected = IllegalArgumentException.class)
    public void throwsIllegalArgumentExceptionWhenAccountsIsEmpty() {
        // given
        ApiKey.Builder apiKeyBuilder = new ApiKey.Builder().withAccessKey("accesskey").withPrivateKey("privateKey");
        User user = aUser().withStatus(ACTIVE).withId(1L)
                .withAnApiKey(apiKeyBuilder).withPasswordAndSalt("vbwms4RaqyLW/L2tPmNVkQ/rY0z7zeOqRSv73eF1D9k=", "dGVzdHNhbHQ=").build();
        when(userService.getUser("knownuser")).thenReturn(user);
        when(passwordService.verifyCredentials(user, "myPassword")).thenReturn(Optional.of(1234L));

        // when
        userAuthenticationService.authenticate("knownuser", "myPassword");
    }

}
