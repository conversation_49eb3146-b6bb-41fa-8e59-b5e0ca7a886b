package com.gumtree.seller.category;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;

import java.util.ArrayList;
import java.util.List;

public final class TestCategory {

    public static Category category(Long id) {
        Category category = new Category();
        category.setId(id);
        return category;
    }

    public static Category category(Long id, String seoName, String displayName) {
        Category category = new Category();
        category.setId(id);
        category.setSeoName(seoName);
        category.setName(displayName);
        return category;
    }

    public static Optional<Category> categoryOption(Long id) {
        return Optional.of(category(id));
    }

    public static Category leafCategory() {
        Category category = new Category();
        category.setChildren(new ArrayList<Category>());
        return category;
    }

    public static Optional<Category> leafCategoryOption() {
        return Optional.of(leafCategory());
    }

    public static Optional<Category> nonLeafCategoryOption() {
        return withChildrenOption(1L, Lists.newArrayList(new Category()));
    }

    public static Category withSeoName(Long id, String name) {
        Category category = new Category();
        category.setId(id);
        category.setSeoName(name);
        return category;
    }

    public static Optional<Category> withSeoNameOption(Long id, String name) {
        return Optional.of(withSeoName(id, name));
    }

    public static Optional<Category> withChildrenOption(Long id, List<Category> children) {
        Category category = new Category();
        category.setId(id);
        category.setChildren(children);
        return Optional.of(category);
    }
}
