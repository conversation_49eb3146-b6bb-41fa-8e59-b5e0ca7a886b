package com.gumtree.seller.category;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.DefaultCategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryAttribute;
import com.gumtree.common.util.json.SnakeCaseObjectMapperFactory;
import org.codehaus.jackson.map.ObjectMapper;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;

public final class TestCategoryModel {

    public static CategoryModel loadFromJsonFile(String file) {
        Category category = null;
        try {
            ObjectMapper objectMapper = new SnakeCaseObjectMapperFactory().create();
            InputStream input = TestCategoryModel.class.getClassLoader().getResourceAsStream(file);
            category = objectMapper.readValue(input, Category.class);
        } catch (IOException e) {
            throw new RuntimeException("can't load test category from json, file = " + file , e);
        }
        return new DefaultCategoryModel("1", category, scala.Option.<Collection<AttributeMetadata>>apply(null));
    }
}
