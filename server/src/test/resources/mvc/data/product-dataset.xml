<dataset>

    <!-- requires user-dataset.xml, location-dataset.xml -->
    <product id="1" name="INSERTION"/>
    <product id="2" name="FEATURE_3_DAY"/>
    <product id="3" name="BUMP_UP"/>
    <product id="4" name="URGENT"/>
    <product id="5" name="HOMEPAGE_SPOTLIGHT"/>
    <product id="6" name="FEATURE_7_DAY"/>
    <product id="7" name="FEATURE_14_DAY"/>
    <product id="8" name="WEBSITE_URL"/>
    <product id="14" name="SEARCH_STANDOUT"/>
    <product id="15" name="EXTENDED_VEHICLE_HISTORY_CHECK"/>
    <product id="16" name="CALL_TRACKING_ACCOUNT_LEVEL"/>
    <product id="17" name="CALL_TRACKING_ACCOUNT_LEVEL_HIDDEN"/>
    <product id="18" name="CALL_TRACKING_ADVERT_LEVEL"/>
    <product id="19" name="CALL_TRACKING_ADVERT_LEVEL_WITH_BLACKLISTING"/>
    <product id="20" name="EMG_FREESPEE_PERMISSION"/>

    <product_price id="4" category_id="1" location_id="2" product_id="4" inc_vat="950"/>
    <product_price id="1" category_id="1" location_id="2" product_id="1" inc_vat="12"/>
    <product_price id="3" category_id="1" location_id="2" product_id="3" inc_vat="50"/>

    <package_type id="1" display_name="London Insertion" salesforce_product_id="ASALESFORCEID" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="1" category_id="1"/>
    <package_location package_type_id="1" location_id="2"/>
    <package_product package_type_id="1" product_id="1"/>
    <package_product package_type_id="1" product_id="3"/>
    <package_product package_type_id="1" product_id="4"/>

    <credit_package id="1" package_type_id="1" account_id="1" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="[expiry_date_future]" salesforce_id="ASALESFORCEID" deleted="0"/>

    <package_type id="2" display_name="London Insertion" salesforce_product_id="ASALESFORCEID3" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="2" category_id="1"/>
    <package_location package_type_id="2" location_id="2"/>
    <package_product package_type_id="2" product_id="1"/>
    <package_product package_type_id="2" product_id="3"/>
    <package_product package_type_id="2" product_id="4"/>
    <credit_package id="2" package_type_id="2" account_id="3" initial_credits="15" used_credits="1" creation_date="2016-07-27 00:00:00" start_date="2016-07-27 00:00:00" end_date="2031-01-01 00:00:00" salesforce_id="ASALESFORCEID3" deleted="0"/>

</dataset>
