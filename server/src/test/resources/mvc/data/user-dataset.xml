<dataset>
    <account id="1" name="Test Account 1" description="First test account" is_pro="1" public_id="hash_1"/>
    <account id="2" name="Test Account 2" description="Second test account" is_pro="0" public_id="hash_2"/>
    <account id="3" name="Test Account 3" description="First test account" is_pro="1" public_id="hash_3"/>
    <account id="4" name="No Credits" description="Test Account with No Credits" is_pro="0" public_id="hash_5"/>
    <account id="5" name="Fraudster" description="Fraudster Account" is_pro="0" public_id="hash_5"/>

    <role id="1" name="API_USER" description="User can post adverts via the Write API"/>
    <role id="2" name="SUPER_USER" description="can do anything"/>
    <role id="3" name="OTHER_USER" description="for testing purposes only"/>
    <role_permission id="1" role_id="1" domain="account" action="manageaccounts" target="*"/>
    <role_permission id="3" role_id="2" domain="account" action="managepackages" target="*"/>
    <role_permission id="4" role_id="2" domain="account" action="manageads" target="*"/>
    <role_permission id="5" role_id="2" domain="account" action="manageaccounts" target="*"/>

    <email_address id="1" email="<EMAIL>"/>
    <usr id="1" username_id="1" password="2dSVDx2kWjBjnHN+oVc20JjYFLeTsOeJBp00cBw7V1I="
         salt="og0Ck8qQJ6v1D2EJFeFA6Xo3Eaze3vPsDx6oTdnrvgTUi+hqIR/6UmiMkd6S56Vw/WLL8J7u2C4VULI8dQoYErD3IUMPB9ixX5zCzZqrtj2jKUpr5qzItKtn/qmGsHXCC4YFM74pLEwux5CStBzb7QNjSINH5UedHtLY57erLhg="
         first_name="Joe" last_name="Bloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="1" role_id="1"/>
    <usr_account usr_id="1" account_id="1"/>
    <api_key id="1" usr_id="1" access_key="apiUserAccessKey" private_key="apiUserPrivateKey"/>

    <email_address id="2" email="<EMAIL>"/>
    <usr id="2" username_id="2" password="4JI2XRDkcZXYHgiCm54FJeozQQ7wAa3VgdzDB8VBkkg="
         salt="I923rvT+lFGuySXhInJr3ypjOt/fFb4vzn0VQNuVeH9KpJLnRnK8wAWDaV/d8BfYfJ9mvsPlJdDWLSEYg1v1DmB+5Q0UXHFly8Vp+hiuvlpy5ebkV5e7gjowrVCezuOm6LQXGIIdZqHMEeObE8OUtzxu+wDB6AYdc5D6+1Bl+UU="
         first_name="Joe" last_name="Cloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="2" role_id="2"/>
    <usr_account usr_id="2" account_id="2"/>
    <api_key id="2" usr_id="2" access_key="superUserAccessKey" private_key="superUserPrivateKey"/>

    <email_address id="3" email="<EMAIL>"/>
    <usr id="3" username_id="3" password="4JI2XRDkcZXYHgiCm54FJeozQQ7wAa3VgdzDB8VBkkg="
         salt="I923rvT+lFGuySXhInJr3ypjOt/fFb4vzn0VQNuVeH9KpJLnRnK8wAWDaV/d8BfYfJ9mvsPlJdDWLSEYg1v1DmB+5Q0UXHFly8Vp+hiuvlpy5ebkV5e7gjowrVCezuOm6LQXGIIdZqHMEeObE8OUtzxu+wDB6AYdc5D6+1Bl+UU="
         first_name="Joe" last_name="Cloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="3" role_id="2"/>
    <usr_account usr_id="3" account_id="3"/>
    <api_key id="3" usr_id="3" access_key="testApiUserAccessKey" private_key="testApiUserPrivateKey"/>

    <email_address id="4" email="<EMAIL>"/>
    <usr id="4" username_id="4" password="4JI2XRDkcZXYHgiCm54FJeozQQ7wAa3VgdzDB8VBkkg="
         salt="I923rvT+lFGuySXhInJr3ypjOt/fFb4vzn0VQNuVeH9KpJLnRnK8wAWDaV/d8BfYfJ9mvsPlJdDWLSEYg1v1DmB+5Q0UXHFly8Vp+hiuvlpy5ebkV5e7gjowrVCezuOm6LQXGIIdZqHMEeObE8OUtzxu+wDB6AYdc5D6+1Bl+UU="
         first_name="MT" last_name="Pockets" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="4" role_id="2"/>
    <usr_account usr_id="4" account_id="4"/>
    <api_key id="4" usr_id="4" access_key="mtPocketsAccessKey" private_key="mtPocketsPrivateKey"/>

    <email_address id="5" email="<EMAIL>"/>
    <usr id="5" username_id="5" password="4JI2XRDkcZXYHgiCm54FJeozQQ7wAa3VgdzDB8VBkkg="
         salt="I923rvT+lFGuySXhInJr3ypjOt/fFb4vzn0VQNuVeH9KpJLnRnK8wAWDaV/d8BfYfJ9mvsPlJdDWLSEYg1v1DmB+5Q0UXHFly8Vp+hiuvlpy5ebkV5e7gjowrVCezuOm6LQXGIIdZqHMEeObE8OUtzxu+wDB6AYdc5D6+1Bl+UU="
         first_name="Bernie" last_name="Madoff" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="5" role_id="2"/>
    <usr_account usr_id="5" account_id="5"/>
    <api_key id="5" usr_id="5" access_key="fraudsterAccessKey" private_key="fraudsterPrivateKey"/>


</dataset>
