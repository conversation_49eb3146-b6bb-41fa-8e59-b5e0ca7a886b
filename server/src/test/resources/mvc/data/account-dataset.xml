<dataset>
    <account id="1" name="Test Account 1" description="First test account" is_pro="1" public_id="hash1"/>
    <account id="2" name="Test Account 2" description="Second test account" is_pro="0" public_id="hash2"/>
    <account id="3" name="Test Account 3" description="First test account" is_pro="1" public_id="hash3"/>

    <role id="1" name="API_USER" description="User can post adverts via the Write API"/>
    <role id="2" name="API_USER" description="User can post adverts via the Write API"/>
    <role id="3" name="API_USER" description="User can post adverts via the Write API"/>
    <role_permission id="1" role_id="1" domain="account" action="manageaccounts" target="*"/>
    <role_permission id="3" role_id="3" domain="account" action="manageads" target="#"/>

    <email_address id="1" email="<EMAIL>" />
    <usr id="1" username_id="1" password="2dSVDx2kWjBjnHN+oVc20JjYFLeTsOeJBp00cBw7V1I=" salt="og0Ck8qQJ6v1D2EJFeFA6Xo3Eaze3vPsDx6oTdnrvgTUi+hqIR/6UmiMkd6S56Vw/WLL8J7u2C4VULI8dQoYErD3IUMPB9ixX5zCzZqrtj2jKUpr5qzItKtn/qmGsHXCC4YFM74pLEwux5CStBzb7QNjSINH5UedHtLY57erLhg=" first_name="Joe" last_name="Bloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="1" role_id="1"/>
    <usr_account usr_id="1" account_id="1"/>
    <api_key id="1" usr_id="1" access_key="testApiUser1AccessKey" private_key="testApiUser1PrivateKey"/>

    <email_address id="2" email="<EMAIL>" />
    <usr id="2" username_id="2" password="4JI2XRDkcZXYHgiCm54FJeozQQ7wAa3VgdzDB8VBkkg=" salt="I923rvT+lFGuySXhInJr3ypjOt/fFb4vzn0VQNuVeH9KpJLnRnK8wAWDaV/d8BfYfJ9mvsPlJdDWLSEYg1v1DmB+5Q0UXHFly8Vp+hiuvlpy5ebkV5e7gjowrVCezuOm6LQXGIIdZqHMEeObE8OUtzxu+wDB6AYdc5D6+1Bl+UU=" first_name="Joe" last_name="Cloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="2" role_id="2"/>
    <usr_account usr_id="2" account_id="2"/>
    <api_key id="2" usr_id="2" access_key="testApiUser2AccessKey" private_key="testApiUser2PrivateKey"/>

    <email_address id="3" email="<EMAIL>" />
    <usr id="3" username_id="3" password="4JI2XRDkcZXYHgiCm54FJeozQQ7wAa3VgdzDB8VBkkg=" salt="I923rvT+lFGuySXhInJr3ypjOt/fFb4vzn0VQNuVeH9KpJLnRnK8wAWDaV/d8BfYfJ9mvsPlJdDWLSEYg1v1DmB+5Q0UXHFly8Vp+hiuvlpy5ebkV5e7gjowrVCezuOm6LQXGIIdZqHMEeObE8OUtzxu+wDB6AYdc5D6+1Bl+UU=" first_name="Joe" last_name="Cloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="3" role_id="3"/>
    <usr_account usr_id="3" account_id="3"/>
    <api_key id="3" usr_id="3" access_key="testApiUser3AccessKey" private_key="testApiUser3PrivateKey"/>

</dataset>
