<dataset>
    <package_type id="1" display_name="Standard ad in Jobs, London" salesforce_product_id="01u20000000i7kCAAQ" is_automatic="false" default_initial_credits="0" is_unlimited="false" limits_behaviour="ACCRUED" is_capability="false" payment_instrument="CREDIT"/>
    <package_type id="2" display_name="3-day featured ad in Jobs, London" salesforce_product_id="01u20000001h8SkAAI" is_automatic="false" default_initial_credits="0" is_unlimited="false" limits_behaviour="ACCRUED" is_capability="false" payment_instrument="CREDIT"/>

    <account id="1" name="Test Account 1" description="First test account" is_pro="1" public_id="hash_1"/>
    <role id="1" name="API_USER" description="User can post adverts via the Write API"/>
    <credit_package id="1" package_type_id="1" account_id="1" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2013-01-01 00:00:00" salesforce_id="DELETESALESFORCEID1"/>
    <credit_package id="2" package_type_id="1" account_id="1" initial_credits="5" used_credits="3" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2013-01-01 00:00:00" salesforce_id="SF-001"/>

    <email_address id="1" email="<EMAIL>" />
    <usr id="1" username_id="1" password="2dSVDx2kWjBjnHN+oVc20JjYFLeTsOeJBp00cBw7V1I=" salt="og0Ck8qQJ6v1D2EJFeFA6Xo3Eaze3vPsDx6oTdnrvgTUi+hqIR/6UmiMkd6S56Vw/WLL8J7u2C4VULI8dQoYErD3IUMPB9ixX5zCzZqrtj2jKUpr5qzItKtn/qmGsHXCC4YFM74pLEwux5CStBzb7QNjSINH5UedHtLY57erLhg=" first_name="Joe" last_name="Bloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <role_permission id="1" role_id="1" domain="account" action="manageads" target="#"/>
    <usr_role usr_id="1" role_id="1"/>
    <role_permission id="2" role_id="1" domain="account" action="managepackages" target="*"/>
    <usr_role usr_id="1" role_id="2"/>
    <usr_account usr_id="1" account_id="1"/>
    <api_key id="1" usr_id="1" access_key="testApiUser1AccessKey" private_key="testApiUser1PrivateKey"/>


    <account id="2" name="Test Account 2" description="First test account" is_pro="0" public_id="hash_2"/>
    <role id="2" name="API_USER" description="User can post adverts via the Write API"/>
    <credit_package id="3" package_type_id="2" account_id="2" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2013-01-01 00:00:00" salesforce_id="DELETESALESFORCEID2"/>
    <credit_package id="4" package_type_id="2" account_id="2" initial_credits="5" used_credits="3" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2013-01-01 00:00:00" salesforce_id="SF-002"/>

    <email_address id="2" email="<EMAIL>" />
    <usr id="2" username_id="2" password="2dSVDx2kWjBjnHN+oVc20JjYFLeTsOeJBp00cBw7V1I=" salt="og0Ck8qQJ6v1D2EJFeFA6Xo3Eaze3vPsDx6oTdnrvgTUi+hqIR/6UmiMkd6S56Vw/WLL8J7u2C4VULI8dQoYErD3IUMPB9ixX5zCzZqrtj2jKUpr5qzItKtn/qmGsHXCC4YFM74pLEwux5CStBzb7QNjSINH5UedHtLY57erLhg=" first_name="Joe" last_name="Bloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <role_permission id="3" role_id="3" domain="account" action="manageads" target="#"/>
    <usr_role usr_id="2" role_id="3"/>
    <api_key id="2" usr_id="2" access_key="testApiUser2AccessKey" private_key="testApiUser2PrivateKey"/>

</dataset>
