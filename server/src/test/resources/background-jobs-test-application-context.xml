<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		                http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:property-placeholder
            location="classpath:/background-jobs-test.properties"
            ignore-resource-not-found="false"
            system-properties-mode="OVERRIDE"/>

    <import resource="classpath:com/gumtree/api/service/price/seller-test-application-context.xml"/>

    <import resource="classpath:META-INF/config/spring/bapi-server-application-context.xmll"/>

    <bean id="executorServiceFactory" class="com.gumtree.common.jobs.executor.SimpleExecutorServiceFactory"/>

    <bean id="platformRealm" class="com.gumtree.seller.security.PlatformRealm"/>

    <bean id="sellerDatabaseOperations" class="org.springframework.jdbc.core.JdbcTemplate">
            <constructor-arg name="dataSource" ref="sellerDatasource"/>
    </bean>

    <bean id="chaseEmailService" class="com.gumtree.api.service.chaseemail.ChaseEmailServiceImpl">
        <constructor-arg name="clock">
            <bean id="stoppedClock" class="com.gumtree.common.util.time.StoppedClock">
                <constructor-arg name="milliseconds" value="1343213940000"/>
            </bean>
        </constructor-arg>
    </bean>

    <bean id="responsiveLoginExperimentService" class="com.gumtree.api.service.chaseemail.ResponsiveExperimentService"/>

</beans>
