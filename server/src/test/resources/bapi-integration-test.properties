gumtree.seller.jdbc.driver=org.h2.Driver
gumtree.seller.jdbc.url=jdbc:h2:~/.gumtree/seller/db/seller;AUTO_SERVER=TRUE;MODE=PostgreSQL
gumtree.seller.jdbc.username=seller
gumtree.seller.jdbc.password=seller
gumtree.seller.jdbc.pool.size.max=30
gumtree.seller.jdbc.pool.min.idle=3
gumtree.seller.jdbc.pool.idle.timeout=60000
gumtree.seller.jdbc.pool.max.lifetime=60000
gumtree.seller.jdbc.pool.connection.timeout=60000
gumtree.seller.jdbc.read.url=jdbc:h2:~/.gumtree/seller/db/seller;AUTO_SERVER=TRUE;MODE=PostgreSQL
gumtree.seller.jdbc.read.username=seller
gumtree.seller.jdbc.read.password=seller
gumtree.seller.jdbc.read.pool.size.max=30
gumtree.seller.jdbc.read.pool.min.idle=3
gumtree.seller.jdbc.read.pool.idle.timeout=60000
gumtree.seller.jdbc.read.pool.max.lifetime=60000
gumtree.seller.jdbc.read.pool.connection.timeout=60000
gumtree.seller.database.provider=H2
gumtree.seller.images.path=/tmp
gumtree.mailgun.smtp_port=2500
gumtree.smtp.starttls=false
gumtree.seller.images.copy_to_disc=false
hpi.service.enabled=false
gumtree.replies.platformid=gumtreeuk
gumtree.replies.replyts1.platformid=gumtreeuk
gumtree.replies.replyts2.platformid=gumtreeuk2
gumtree.replies.replyts2.groupsize=0
gumtree.replies.split.override.prefix=gumtreeOverridePrefix
gumtree.reply.email.header=false
gumtree.blocked.passwords.list=Qwerty123
gumtree.advert.eventlog.enabled=false
gumtree.user.eventlog.enabled=true

# Pricing API
gumtree.pricing.api.host=priceapi.gt-staging.ams1.cloud
gumtree.pricing.api.port=80
gumtree.pricing.api.basepath=http://${gumtree.pricing.api.host}:${gumtree.pricing.api.port}
gumtree.pricing.api.connection.timeout=5000
gumtree.pricing.api.read.timeout=30000

# Payment API
gumtree.payment.api.host=paymentapi.gt-staging.ams1.cloud
gumtree.payment.api.port=80
gumtree.payment.api.basepath=http://${gumtree.payment.api.host}:${gumtree.payment.api.port}
gumtree.payment.api.connection.timeout=5000
gumtree.payment.api.read.timeout=5000

# Kafka
gumtree.advert.change_events.destination=seda:test-ad-changed-notification
gumtree.advert.reindex_events.destination=seda:test-ad-changed-notification

# Seller type
gumtree.sellertype.categories=10201, 4610
gumtree.sellertype.advertlimits=2, 1
