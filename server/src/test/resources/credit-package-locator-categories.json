{"id": 1, "name": "Classifieds", "seo_name": "classifieds", "price_available": 0, "read_only": false, "enabled": true, "children": [{"id": 3, "name": "Cars", "seo_name": "cars", "parent_id": 1, "read_only": false, "enabled": true, "price_available": 0, "children": [{"id": 12, "name": "Wanted Cars", "seo_name": "wanted-cars", "parent_id": 3, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 13, "name": "Cars, Vans &amp; Motorbikes", "seo_name": "cars-vans-motorbikes", "parent_id": 3, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 14, "name": "Car Parts", "seo_name": "car-parts", "parent_id": 3, "read_only": false, "enabled": true, "price_available": 0, "children": []}]}, {"id": 2, "name": "Housing", "seo_name": "housing", "parent_id": 1, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 4, "name": "Jobs", "seo_name": "jobs", "parent_id": 1, "read_only": false, "enabled": true, "price_available": 0, "children": [{"id": 15, "name": "Uncovered Jobs", "seo_name": "uncovered-jobs", "parent_id": 4, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 5, "name": "Wanted Jobs", "seo_name": "wanted-jobs", "parent_id": 4, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 6, "name": "Accounts Assistant &amp; Bookkeeping", "seo_name": "accounts-assistant-bookkeeping", "parent_id": 4, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 7, "name": "Bar Staff &amp; Management", "seo_name": "bar-jobs", "parent_id": 4, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 8, "name": "Nursing", "seo_name": "nursing-jobs", "parent_id": 4, "read_only": false, "enabled": true, "price_available": 0, "children": [{"id": 9, "name": "Specialist", "seo_name": "specialist-nursing-jobs", "parent_id": 8, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 10, "name": "General", "seo_name": "general-nursing-jobs", "parent_id": 8, "read_only": false, "enabled": true, "price_available": 0, "children": []}, {"id": 11, "name": "Other", "seo_name": "other-nursing-jobs", "parent_id": 8, "read_only": false, "enabled": true, "price_available": 0, "children": []}]}]}]}