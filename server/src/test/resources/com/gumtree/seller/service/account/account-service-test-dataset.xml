<dataset>
    <account id="1" name="accountName" is_pro="0" force_post_mask="0" public_id="hash1"/>
    <usr id="1" username_id="1" password="blah" salt="foo" first_name="donald" last_name="draper" status="ACTIVE" version="1"/>
    <usr_account account_id="1" usr_id="1"/>
    <email_address id="1" email="user1"/>
    <image id="1" url="http://test.url.com/image1.jpg"/>

    <image id="3001" url="https://i.ebayimg.com/00/s/1/$_86.PNG"/>

    <image id="3002" url="https://i.ebayimg.com/00/s/2/$_86.PNG"/>
    <media id="3002" url="https://imagedelivery.net/abc/2/1"/>

    <image id="3003" url="https://i.ebayimg.com/00/s/3/$_86.PNG"/>
    <media id="3003" url=""/>

    <image id="3004" url=""/>
    <media id="3004" url="https://imagedelivery.net/abc/4/1"/>

    <account id="31111" name="proAccount" is_pro="true" public_id="hash31111"/>
    <credit_package
            id="311112" package_type_id="2" account_id="31111" initial_credits="5" used_credits="1"
            creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="[expiry_date_future]"
            salesforce_id="ASALESFORCEID_2"/>
    <credit_package
            id="311111" package_type_id="8888" account_id="31111" initial_credits="5" used_credits="0"
            creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2030-01-01 00:00:00"
            />

    <account id="2222" name="nonProAccount" is_pro="false" public_id="2222"/>
    <credit_package
            id="211111" package_type_id="8888" account_id="2222" initial_credits="5" used_credits="0"
            creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2030-01-01 00:00:00" />
    <credit_package
            id="211112" package_type_id="2" account_id="2222" initial_credits="5" used_credits="1"
            creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="[expiry_date_future]"
            />

    <package_type id="8888" display_name="Automatic Product" salesforce_product_id="SFID-PT-008" is_automatic="true" limits_behaviour="UNLIMITED" default_initial_credits="5" payment_instrument="CREDIT"/>
    <package_type id="2" display_name="Non Automatic Product" salesforce_product_id="SFID-PT-002" is_automatic="false" limits_behaviour="ACCRUED" default_initial_credits="5" payment_instrument="CREDIT"/>

    <account id="31113" name="car dealer" force_post_mask="2" is_pro="false" public_id="hash31113"/>
    <account id="31114" name="vans dealer" force_post_mask="4" is_pro="false" public_id="hash_31114"/>

    <account id="31115" name="private with 1 live car ad" force_post_mask="0" is_pro="false" public_id="hash31115"/>
    <advert id="31115"
            category_id="10302"
            account_id="31115"
            title="live audi ad"
            description="live audi ad"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            created_by="1"
            last_modified_by="1"
            ip="127.0.0.1" />

    <account id="31116" name="private with 1 awaiting cs review car ad" force_post_mask="0" is_pro="false" public_id="hash31116"/>
    <advert id="31116"
            category_id="10302"
            account_id="31116"
            title="live audi ad"
            description="live audi ad"
            created_date="2011-01-01 09:30:00"
            status="AWAITING_CS_REVIEW"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            created_by="1"
            last_modified_by="1"
            ip="127.0.0.1" />

    <account id="31117" name="private with 1 awaiting activation car ad" force_post_mask="0" is_pro="false" public_id="hash31117"/>
    <advert id="31117"
            category_id="10302"
            account_id="31117"
            title="live audi ad"
            description="live audi ad"
            created_date="2011-01-01 09:30:00"
            status="AWAITING_ACTIVATION"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            created_by="1"
            last_modified_by="1"
            ip="127.0.0.1"/>

    <account_image account_id="31114" image_id="3001"/>
    <account_image account_id="31115" image_id="3002"/>
    <account_image account_id="31116" image_id="3003"/>
    <account_image account_id="31117" image_id="3004"/>
</dataset>
