<dataset>
    <attribute id="1" name="VEHICLE_MAKE"/>
    <attribute id="2" name="VEHICLE_COLOUR"/>
    <attribute id="3" name="VEHICLE_FUEL_TYPE"/>

    <location id="1" name="location-one" display_name="Location One"/>
    <location id="2" name="location-two" display_name="Location Two"/>
    <location id="3" name="location-three" display_name="Location Three"/>
    <location id="4" name="location-four" display_name="Location Four"/>
    <location id="5" name="location-five" display_name="Location Five"/>
    <location id="6" name="location-six" display_name="Location Six"/>
    <location id="7" name="location-seven" display_name="Location Seven"/>

    <postcode id="1" postcode="SW185AS" latitude="55.55" longitude="11.11"/>
    <postcode id="2" postcode="TW91RP" latitude="51.55" longitude="14.11"/>

    <postcode_location location_id="2" postcode_id="1"/>
    <postcode_location location_id="3" postcode_id="1"/>
    <postcode_location location_id="4" postcode_id="2"/>
    <postcode_location location_id="5" postcode_id="2"/>

    <outcode id="1" outcode="SW19" latitude="0.0" longitude="0.0"/>

    <outcode_location location_id="6" outcode_id="1"/>
    <outcode_location location_id="7" outcode_id="1"/>

    <account id="1" name="Test account 1" description="First test account" public_id="hash_1"/>
    <account id="2" name="Test account 2" description="Second test account" public_id="hash_2"/>

    <image id="1" url="http://foo.bar/1.jpg"/>
    <image id="2" url="http://foo.bar/2.jpg"/>

    <!-- We need to have an existing advert in the database to test update -->
    <advert id="1"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            price="10.99"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            ip="127.0.0.1"/>

    <advert_category advert_id="1" category_id="1"/>
    <usr_specified_location advert_id="1" location_id="1"/>
    <advert_attribute advert_id="1" attribute_id="1" value="BMW"/>
    <advert_image advert_id="1" image_id="1" main="1" position="0"/>

    <usr id="1" username_id="1" password="blah" salt="foo" first_name="sam" last_name="smith" status="ACTIVE" version="1"/>
    <email_address id="1" email="user1" />
    <usr_account account_id="1" usr_id="1"/>

    <advert id="2"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="AWAITING_SCREENING"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            price="10.99"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            ip="127.0.0.1"/>

    <advert_category advert_id="2" category_id="1"/>

    <advert id="3"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            price="10.99"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            ip="127.0.0.1"/>

    <advert_category advert_id="3" category_id="1"/>

</dataset>
