<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa-1.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

    <context:annotation-config/>
    <aop:aspectj-autoproxy/>
    <tx:annotation-driven transaction-manager="transactionManager" order="2"/>

    <bean class="com.gumtree.seller.service.image.aspect.ImageServiceFailureAspect"/>
    <bean class="com.gumtree.seller.test.MockImageService"/>

    <bean id="transactionManager" class="com.gumtree.seller.test.MockPlatformTransactionManager">
        <!-- Do not fail on commit -->
        <constructor-arg value="false"/>
    </bean>

    <bean id="imageFailureListener" class="com.gumtree.seller.test.MockObjectFactoryBean">
        <constructor-arg value="com.gumtree.seller.event.listener.image.ImageFailureListener"/>
    </bean>

    <bean id="transactionNotifier" class="com.gumtree.seller.test.TransactionNotifierFactoryBean"/>

</beans>
