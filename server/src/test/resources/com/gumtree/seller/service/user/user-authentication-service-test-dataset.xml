<dataset>

    <role id="1" name="API_USER" description="Api User"/>
    <role id="2" name="SUPER_USER" description="User can do anything"/>

    <usr id="100" status="ACTIVE"              first_name="donald" last_name="draper"  username_id="100" password="TjM2nDhymfBiTNh8z997Xl3zsaloPWuxZFyMmWP/Kj0=" salt="i+pq/EfsVcFEEqj9I4+oR0mY1ji82CaUm7nmsoAX4Rg9DXFT2OS4ciKAqAJZG2UodvzGWVLH1e7VVcG6y3un3DHQEbK+Sb/Y/5U8qKwbDkWv9AmqstHp/y9kgw5/GuQS7mfvyQJHVi4XkHf1hGkA3jxwm16jASrt8K/7Aq0w10o="  version="1"/>
    <usr id="200" status="ACTIVE"              first_name="reginald" last_name="baker" username_id="200" password="TjM2nDhymfBiTNh8z997Xl3zsaloPWuxZFyMmWP/Kj0=" salt="i+pq/EfsVcFEEqj9I4+oR0mY1ji82CaUm7nmsoAX4Rg9DXFT2OS4ciKAqAJZG2UodvzGWVLH1e7VVcG6y3un3DHQEbK+Sb/Y/5U8qKwbDkWv9AmqstHp/y9kgw5/GuQS7mfvyQJHVi4XkHf1hGkA3jxwm16jASrt8K/7Aq0w10o="  version="1"/>
    <usr id="300" status="DEACTIVATED"         first_name="chris" last_name="pike" username_id="300" password="TjM2nDhymfBiTNh8z997Xl3zsaloPWuxZFyMmWP/Kj0=" salt="i+pq/EfsVcFEEqj9I4+oR0mY1ji82CaUm7nmsoAX4Rg9DXFT2OS4ciKAqAJZG2UodvzGWVLH1e7VVcG6y3un3DHQEbK+Sb/Y/5U8qKwbDkWv9AmqstHp/y9kgw5/GuQS7mfvyQJHVi4XkHf1hGkA3jxwm16jASrt8K/7Aq0w10o=" version="1"/>
    <usr id="500" status="AWAITING_ACTIVATION" first_name="Gabe" last_name="Harvey" username_id="500" password="TjM2nDhymfBiTNh8z997Xl3zsaloPWuxZFyMmWP/Kj0=" salt="i+pq/EfsVcFEEqj9I4+oR0mY1ji82CaUm7nmsoAX4Rg9DXFT2OS4ciKAqAJZG2UodvzGWVLH1e7VVcG6y3un3DHQEbK+Sb/Y/5U8qKwbDkWv9AmqstHp/y9kgw5/GuQS7mfvyQJHVi4XkHf1hGkA3jxwm16jASrt8K/7Aq0w10o="  version="1"/>
    <usr id="600" status="ACTIVE"              first_name="jack" last_name="fox"  username_id="600" password="sicImM0HFDaVLlLQILWEJSOzue3oiLyKZ5UQ/e1idAU=" salt="pr1oyZB4DJmpfLCEngnelA==" version="1"/>

    <email_address id="100" email="<EMAIL>"/>
    <email_address id="200" email="<EMAIL>"/>
    <email_address id="300" email="<EMAIL>"/>
    <email_address id="500" email="<EMAIL>"/>
    <email_address id="600" email="<EMAIL>"/>

    <account id="100" name="1st account" description="the first account" is_pro="0" public_id="hash_100"/>
    <account id="600" name="1st account" description="the first account" is_pro="0" public_id="hash_100"/>

    <usr_account account_id="100" usr_id="100"/>
    <usr_account account_id="600" usr_id="600"/>

    <api_key id="100" usr_id="100" access_key="testApiUser1AccessKey" private_key="testApiUser1PrivateKey"/>
    <api_key id="600" usr_id="600" access_key="testApiUser600AccessKey" private_key="testApiUser600PrivateKey"/>

    <usr_role usr_id="100" role_id="2"/>
    <usr_role usr_id="600" role_id="2"/>

    <usr_credentials id="600" version="1" username="<EMAIL>" password_hash="sicImM0HFDaVLlLQILWEJSOzue3oiLyKZ5UQ/e1idAU=" salt="pr1oyZB4DJmpfLCEngnelA==" created="2020-01-01 10:25:00" last_modified="2020-01-01 10:25:00" />

</dataset>