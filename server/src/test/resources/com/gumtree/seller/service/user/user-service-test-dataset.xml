<dataset>
    <usr id="1" username_id="1" password="blah" salt="foo" first_name="donald" last_name="draper" status="ACTIVE" version="1"/>
    <usr id="7" username_id="7" password="blah" salt="foo" first_name="donald" last_name="draper" status="ACTIVE" version="1"/>
    <usr id="8" username_id="8" password="blah" salt="foo" first_name="donald" last_name="draper" status="ACTIVE" version="1"/>
    <usr id="9" username_id="9" password="blah" salt="foo" first_name="donald" last_name="draper" status="ACTIVE" version="1"/>
    <usr id="600" status="ACTIVE"              first_name="jack" last_name="fox"  username_id="600" password="sicImM0HFDaVLlLQILWEJSOzue3oiLyKZ5UQ/e1idAU=" salt="pr1oyZB4DJmpfLCEngnelA==" version="1"/>

    <account id="1" name="1st account" description="the first account" is_pro="0" public_id="hash_1"/>
    <account id="2" name="2nd account" description="the second account" is_pro="0" public_id="hash_2"/>
    <usr_account account_id="1" usr_id="1"/>
    <usr_account account_id="2" usr_id="1"/>
    <email_address id="1" email="<EMAIL>"/>
    <email_address id="2" email="<EMAIL>"/>
    <email_address id="3" email="<EMAIL>"/>
    <email_address id="4" email="<EMAIL>"/>
    <email_address id="5" email="<EMAIL>"/>
    <email_address id="6" email="<EMAIL>"/>
    <email_address id="7" email="<EMAIL>"/>
    <email_address id="8" email="<EMAIL>"/>
    <email_address id="9" email="<EMAIL>"/>
    <email_address id="600" email="<EMAIL>"/>

    <api_key id="9" usr_id="9" access_key="testApiUser9AccessKey1" private_key="testApiUser9PrivateKey1"/>
    <api_key id="600" usr_id="600" access_key="testApiUser600AccessKey" private_key="testApiUser600PrivateKey"/>

    <usr id="2" username_id="2" password="blah" salt="foo" first_name="frank" last_name="fish" status="AWAITING_ACTIVATION" version="1"/>

    <usr id="3" username_id="3" password="blah" salt="foo" first_name="frank" last_name="fish" status="AWAITING_ACTIVATION" version="1"/>
    <usr_activation_key id="1" activation_key="TESTKEY101" usr_id="3" expiry_date="2100-01-01 10:30:00"/>

    <reset_password_key id="1" key="TEST_PASSWORD_KEY" usr_id="3" expiry_date="2012-01-01 10:30:00"/>

    <usr id="4" username_id="4" password="blah" salt="foo" first_name="bobby-joe" last_name="weasel" status="ACTIVE" version="1"/>
    <reset_password_key id="2" key="TEST_PASSWORD_KEY_2" usr_id="4" expiry_date="2012-01-01 10:30:00"/>
    <reset_password_key id="3" key="TEST_PASSWORD_KEY_3" usr_id="4" expiry_date="2012-01-01 10:30:00"/>
    <reset_password_key id="4" key="TEST_PASSWORD_KEY_4" usr_id="4" expiry_date="2012-01-01 10:30:00"/>

    <role id="1" name="API_USER" description="Api User"/>
    <role id="10" name="JOBS_USER" description="Jobs User"/>
    <usr_role usr_id="600" role_id="1"/>

    <usr id="5" username_id="5" password="blah" salt="foo" first_name="robert" last_name="hugehead" status="AWAITING_ACTIVATION" version="1"/>
    <usr_activation_key id="5" activation_key="TESTKEY102" usr_id="5" expiry_date="2100-01-01 10:30:00"/>
    <account id="5" name="1st account" description="the first account" is_pro="0" public_id="hash_5"/>
    <account id="3" name="1st account" description="the first account" is_pro="0" public_id="hash_3"/>
    <account id="600" name="1st account" description="the first account" is_pro="0" public_id="hash_100"/>

    <usr_account account_id="3" usr_id="3"/>
    <usr_account account_id="5" usr_id="5"/>
    <usr_account account_id="600" usr_id="600"/>

    <usr_credentials id="600" version="1" username="<EMAIL>" password_hash="sicImM0HFDaVLlLQILWEJSOzue3oiLyKZ5UQ/e1idAU=" salt="pr1oyZB4DJmpfLCEngnelA==" created="2020-01-01 10:25:00" last_modified="2020-01-01 10:25:00" />
    <advert id="1"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="DRAFT"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="5"
            version="1"
            created_by="5"
            last_modified_by="5"
            category_id="1"
            ip="127.0.0.1"/>
    <postcode id="1" postcode="SW185AS" latitude="55.55" longitude="11.11"/>
</dataset>
