<dataset>
    <role id="1" name="API_USER" description="Api User"/>

    <attribute id="1" name="VEHICLE_MAKE"/>
    <attribute id="2" name="VEHICLE_COLOUR"/>
    <attribute id="3" name="VEHICLE_FUEL_TYPE"/>
    <attribute id="4" name="PRICE"/>
    <attribute id="5" name="PRICE_FREQUENCY"/>

    <location id="1" name="location-one" display_name="Location One"/>
    <location id="2" name="location-two" display_name="Location Two"/>
    <location id="3" name="location-three" display_name="Location Three"/>
    <location id="4" name="location-four" display_name="Location Four"/>
    <location id="5" name="location-five" display_name="Location Five"/>
    <location id="6" name="location-six" display_name="Location Six"/>
    <location id="7" name="location-seven" display_name="Location Seven"/>

    <location_zoom_out owner_id="3" zoom_out_id="2"/>
    <location_zoom_out owner_id="5" zoom_out_id="4"/>
    <location_zoom_out owner_id="7" zoom_out_id="6"/>
    <location_zoom_in owner_id="2" zoom_in_id="3"/>
    <location_zoom_in owner_id="4" zoom_in_id="5"/>
    <location_zoom_in owner_id="6" zoom_in_id="7"/>

    <postcode id="1" postcode="SW185AS" latitude="55.55" longitude="11.11"/>
    <postcode id="2" postcode="TW91RP" latitude="51.55" longitude="14.11"/>

    <postcode_location location_id="2" postcode_id="1"/>
    <postcode_location location_id="3" postcode_id="1"/>
    <postcode_location location_id="4" postcode_id="2"/>
    <postcode_location location_id="5" postcode_id="2"/>

    <outcode id="1" outcode="SW19" latitude="0.0" longitude="0.0"/>

    <outcode_location location_id="6" outcode_id="1"/>
    <outcode_location location_id="7" outcode_id="1"/>

    <account id="1" name="Test account 1" description="First test account" is_pro="0" public_id="hash_1"/>
    <account id="2" name="Test account 2" description="Second test account" is_pro="0" public_id="hash_2"/>

    <image id="40001" url="http://foo.bar/40001.jpg"/>
    <image id="40002" url="http://foo.bar/40002.jpg"/>

    <!-- We need to have an existing advert in the database to test update -->
    <advert id="1"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <usr_specified_location advert_id="1" location_id="1"/>
    <advert_attribute advert_id="1" attribute_id="1" value="BMW"/>
    <advert_attribute advert_id="1" attribute_id="4" value="10"/>
    <advert_image advert_id="1" image_id="40001" main="1" position="0"/>

    <usr id="1" username_id="1" password="blah" salt="foo" first_name="sam" last_name="smith" status="ACTIVE" version="1"/>
    <email_address id="1" email="user1" />
    <usr_account account_id="1" usr_id="1"/>

    <advert id="2"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="AWAITING_SCREENING"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <advert id="3"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <advert id="4"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <advert id="5"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="AWAITING_CS_REVIEW"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <advert id="6"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <advert id="7"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="DELETED_USER"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <advert id="8"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="AWAITING_SCREENING"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <advert id="9"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="AWAITING_SCREENING"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <advert_fingerprint advert_id="1" fingerprint="141751791f5980343bdad5a682092fe47eeedfa2"/>
    <advert_fingerprint advert_id="3" fingerprint="91751791fsfsdf43bdad5a682092fe47esedsafd"/>

    <advert_external_reference advert_id="4" usr_id="1" account_id="1" external_ref="an-existing-external-reference"/>
    <advert_url advert_id="4" website_url="http://existing.website.com" is_paid="true"/>

    <advert id="222" title="blah1"
            description="test1"
            status="EXPIRED"
            account_id="1"
            created_date="2011-09-28 18:37:54"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            postcode_id="1"
            version="1"
            ip="127.0.0.1"/>
    <advert_feature id="5" advert_id="222" product_id="2" expiry_date="2022-01-01 12:12:12"/>
    <purchase_order id="222" account_id="1"/>
    <purchase_order_item id="223" purchase_order_id="222" advert_id="222" product_id="1" status="UNPAID" price_inc_vat="12"/>
    <purchase_order_item id="224" purchase_order_id="222" advert_id="222" product_id="2" status="PAID"/>

    <product id="1" name="INSERTION"/>
    <product id="2" name="URGENT"/>
    <product id="3" name="FEATURE_3_DAY"/>
    <product id="4" name="BUMP_UP"/>
    <product id="5" name="HOMEPAGE_SPOTLIGHT"/>
    <product id="6" name="FEATURE_7_DAY"/>
    <product id="7" name="FEATURE_14_DAY"/>
    <product id="8" name="WEBSITE_URL"/>


    <!-- test with missing images: "main" advert_image but missing main image and no other image-->
    <advert id="300"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <usr_specified_location advert_id="300" location_id="1"/>
    <advert_attribute advert_id="300" attribute_id="1" value="BMW"/>
    <advert_attribute advert_id="300" attribute_id="4" value="10"/>
    <advert_image advert_id="300" image_id="-9999" main="1" position="0"/>

    <!-- test with missing images: "main" advert_image but missing main image and there is another image-->
    <advert id="301"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <usr_specified_location advert_id="301" location_id="1"/>
    <advert_attribute advert_id="301" attribute_id="1" value="BMW"/>
    <advert_attribute advert_id="301" attribute_id="4" value="10"/>
    <advert_image advert_id="301" image_id="-9999" main="1" position="0"/>
    <advert_image advert_id="301" image_id="2" main="0" position="0"/>

    <!-- test with missing images: "main" advert_image but missing main image and there are multiple other images -->
    <advert id="302"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="1"
            ip="127.0.0.1"/>

    <usr_specified_location advert_id="302" location_id="1"/>
    <advert_attribute advert_id="302" attribute_id="1" value="BMW"/>
    <advert_attribute advert_id="302" attribute_id="4" value="10"/>
    <advert_image advert_id="302" image_id="-9999" main="1" position="0"/>
    <advert_image advert_id="302" image_id="2" main="0" position="1"/>
    <advert_image advert_id="302" image_id="1" main="0" position="2"/>

</dataset>
