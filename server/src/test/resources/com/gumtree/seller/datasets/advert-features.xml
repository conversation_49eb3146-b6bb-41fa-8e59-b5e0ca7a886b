<dataset>
    <account id="1" name="first account" is_pro="0" public_id="hash_1"/>
    <usr id="1" username_id="1" password="AAA" salt="ZZZ" first_name="sam" last_name="smith" status="ACTIVE" version="1"/>
    <email_address id="1" email="<EMAIL>" />
    <usr_account usr_id="1" account_id="1"/>
    <role id="1" name="API_USER" description="User can do everything"/>
    <role_permission id="1" role_id="1" domain="account" action="manageads" target="#"/>
    <usr_role usr_id="1" role_id="1"/>

    <advert id="111" title="blah1" description="test1" status="LIVE" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <advert id="112" title="blah2" description="test2" status="AWAITING_CS_REVIEW" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <location id="2" name="london" is_landing="0" display_name="London"/>
    <location id="20" name="outside london" is_landing="0" display_name="Outside London"/>
    <postcode id="1" postcode="SW185AS" latitude="55.55" longitude="11.11"/>
    <postcode id="10" postcode="LE75AS" latitude="55.55" longitude="11.11"/>
    <postcode_location location_id="2" postcode_id="1"/>
    <postcode_location location_id="20" postcode_id="10"/>

    <package_type id="1" display_name="London Insertion" salesforce_product_id="ASALESFORCEID" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="1" category_id="1"/>
    <package_location package_type_id="1" location_id="2"/>
    <package_product package_type_id="1" product_id="1"/>
    <package_product package_type_id="1" product_id="3"/>
    <package_product package_type_id="1" product_id="4"/>
    <credit_package id="1" package_type_id="1" account_id="1" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="[expiry_date_future]" salesforce_id="ASALESFORCEID" deleted="0"/>

    <product id="1" name="INSERTION"/>
    <product id="2" name="FEATURE_3_DAY"/>
    <product id="3" name="BUMP_UP"/>
    <product id="4" name="URGENT"/>
    <product id="5" name="HOMEPAGE_SPOTLIGHT"/>
    <product id="6" name="FEATURE_7_DAY"/>
    <product id="7" name="FEATURE_14_DAY"/>
    <product id="8" name="WEBSITE_URL"/>
    <product id="14" name="SEARCH_STANDOUT"/>
    <product id="15" name="EXTENDED_VEHICLE_HISTORY_CHECK"/>
    <product id="16" name="CALL_TRACKING_ACCOUNT_LEVEL"/>
    <product id="17" name="CALL_TRACKING_ACCOUNT_LEVEL_HIDDEN"/>
    <product id="18" name="CALL_TRACKING_ADVERT_LEVEL"/>
    <product id="19" name="CALL_TRACKING_ADVERT_LEVEL_WITH_BLACKLISTING"/>
    <product id="20" name="EMG_FREESPEE_PERMISSION"/>

    <advert_feature id="1" advert_id="111" product_id="1" expiry_date="2010-01-01 12:12:12"/>
    <advert_feature id="2" advert_id="111" product_id="2" expiry_date="2011-01-01 12:12:12"/>
    <advert_feature id="3" advert_id="111" product_id="2" expiry_date="2052-01-01 12:12:12"/>
    <advert_feature id="4" advert_id="111" product_id="3" expiry_date="2052-01-01 12:12:12"/>

    <advert id="222" title="blah1" description="test1" status="EXPIRED" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <advert_feature id="5" advert_id="222" product_id="2" expiry_date="2022-01-01 12:12:12"/>

    <advert id="333" title="blah1" description="test1" status="LIVE" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <advert id="444" title="free1" description="free1" status="LIVE" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="10" ip="127.0.0.1"/>

    <purchase_order id="111" account_id="1"/>
    <purchase_order_item id="112" purchase_order_id="111" advert_id="111" product_id="1" status="UNPAID" price_inc_vat="12"/>

    <purchase_order id="222" account_id="1"/>
    <purchase_order_item id="223" purchase_order_id="222" advert_id="222" product_id="1" status="UNPAID" price_inc_vat="12"/>
    <purchase_order_item id="224" purchase_order_id="222" advert_id="222" product_id="2" status="PAID"/>
    <package_payment id="212" purchase_order_item_id="224" credit_package_id="2"/>

    <credit_package id="2" package_type_id="1" account_id="1" initial_credits="4" used_credits="4" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="[expiry_date_future]" salesforce_id="BSALESFORCEID" deleted="0"/>

    <advert id="555" title="draft1" description="draft1" status="DRAFT" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <purchase_order id="333" account_id="1"/>
    <purchase_order_item id="334" purchase_order_id="333" advert_id="555" product_id="2" status="PAID" price_inc_vat="0"/>
    <purchase_order_item id="335" purchase_order_id="333" advert_id="555" product_id="3" status="PAID" price_inc_vat="0"/>

    <advert id="666" title="live1" description="live1" status="LIVE" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <purchase_order id="444" account_id="1"/>
    <purchase_order_item id="445" purchase_order_id="444" advert_id="666" product_id="2" status="PAID" price_inc_vat="0"/>
    <purchase_order_item id="446" purchase_order_id="444" advert_id="666" product_id="3" status="PAID" price_inc_vat="0"/>

    <advert id="777" title="draft1" description="draft1" status="DRAFT" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>

    <advert id="888" title="Plain Ad" description="test1" status="LIVE" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>

    <advert id="999" title="live1" description="live1" status="LIVE" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <purchase_order id="555" account_id="1"/>
    <purchase_order_item id="556" purchase_order_id="555" advert_id="999" product_id="1" status="UNPAID" price_inc_vat="0"/>
    <purchase_order_item id="557" purchase_order_id="555" advert_id="999" product_id="1" status="PAID" price_inc_vat="0"/>

    <advert id="101010" title="blah1" description="test1" status="LIVE" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <advert_feature id="6" advert_id="101010" product_id="8" expiry_date="2022-01-01 12:12:12"/>
    <advert_url advert_id="101010" website_url="www.gumtree.com/adverts/101010" is_paid="1"/>
    <purchase_order id="101010" account_id="1"/>
    <purchase_order_item id="101011" purchase_order_id="101010" advert_id="101010" product_id="8" status="UNPAID" price_inc_vat="12"/>

    <advert id="111111" title="draft2" description="draft2" status="DRAFT" account_id="1" created_date="2011-09-28 18:37:54" created_by="1" last_modified_by="1" category_id="1" postcode_id="1" ip="127.0.0.1"/>
    <purchase_order id="101011" account_id="1"/>
    <purchase_order_item id="101012" purchase_order_id="101011" advert_id="111111" product_id="1" status="PAID" price_inc_vat="12"/>
    <purchase_order_item id="101013" purchase_order_id="101011" advert_id="111111" product_id="3" status="PAID" price_inc_vat="12"/>

</dataset>
