<dataset>
    <role id="1" name="SUPER_USER" description="User can do everything"/>
    <role_permission id="1" role_id="1" domain="account" action="managepackages" target="*"/>

    <account id="1" name="Test Account 1" description="First test account" is_pro="1" public_id="hash_1"/>
    <usr id="1" username_id="1" password="password" salt="salt" first_name="<PERSON>" last_name="Bloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="1" role_id="1"/>
    <email_address id="1" email="user1" />

    <account id="8" name="Test Account 8" description="Eighth test account" is_pro="1" public_id="hash_8"/>
    <usr id="2" username_id="2" password="password" salt="salt" first_name="Jane" last_name="Bloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="2" role_id="1"/>
    <email_address id="2" email="user2" />

    <account id="9" name="Test Account 9" description="Ninth test account" is_pro="1" public_id="hash_9"/>

    <!-- UK -->
    <location id="1" name="uk" is_landing="0" display_name="United Kingdom"/>
    <location id="2" name="london" is_landing="0" display_name="London"/>
    <location id="3" name="brighton" is_landing="0" display_name="Brighton"/>
    <location id="4" name="richmond" is_landing="0" display_name="Richmond"/>
    <location id="5" name="manchester" is_landing="0" display_name="Manchester"/>

    <location_zoom_out owner_id="2" zoom_out_id="1"/>
    <location_zoom_out owner_id="3" zoom_out_id="1"/>
    <location_zoom_out owner_id="5" zoom_out_id="1"/>
    <location_zoom_out owner_id="4" zoom_out_id="2"/>

    <product id="1" name="INSERTION"/>
    <product id="2" name="FEATURE_7_DAY"/>

    <package_type id="1" display_name="London Bookkeeping Insertion" salesforce_product_id="ASALESFORCEID" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="1" category_id="6"/>
    <package_location package_type_id="1" location_id="2"/>
    <package_product package_type_id="1" product_id="1"/>

    <package_type id="2" display_name="Richmond Bookkeeping Insertion" salesforce_product_id="SFID-PT-002" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="2" category_id="6"/>
    <package_location package_type_id="2" location_id="4"/>
    <package_product package_type_id="2" product_id="1"/>

    <package_type id="3" display_name="Brighton Bookkeeping Insertion" salesforce_product_id="SFID-PT-003" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="3" category_id="6"/>
    <package_location package_type_id="3" location_id="3"/>
    <package_product package_type_id="3" product_id="1"/>

    <package_type id="4" display_name="Manchester Bookkeeping Insertion" salesforce_product_id="SFID-PT-004" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="4" category_id="6"/>
    <package_location package_type_id="4" location_id="5"/>
    <package_product package_type_id="4" product_id="1"/>

    <package_type id="5" display_name="Richmond Cars Insertion" salesforce_product_id="SFID-PT-005" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="5" category_id="3"/>
    <package_location package_type_id="5" location_id="4"/>
    <package_product package_type_id="5" product_id="1"/>

    <package_type id="6" display_name="Packaged Created In Error" salesforce_product_id="DELETEDPACKAGE" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="6" category_id="12"/>
    <package_location package_type_id="6" location_id="2"/>
    <package_product package_type_id="6" product_id="1"/>

    <package_type id="7" display_name="London Jobs Insertion" salesforce_product_id="SFID-PT-006" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="7" category_id="6"/>
    <package_category package_type_id="7" category_id="7"/>
    <package_category package_type_id="7" category_id="8"/>
    <package_location package_type_id="7" location_id="2"/>
    <package_product package_type_id="7" product_id="1"/>

    <package_type id="8" display_name="RoUK Jobs Insertion" salesforce_product_id="SFID-PT-008" is_automatic="0" limits_behaviour="ACCRUED" default_initial_credits="0" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="8" category_id="6"/>
    <package_category package_type_id="8" category_id="7"/>
    <package_category package_type_id="8" category_id="8"/>
    <package_location package_type_id="8" location_id="3"/>
    <package_location package_type_id="8" location_id="5"/>
    <package_product package_type_id="8" product_id="1"/>

    <package_type id="9" display_name="5 free job ads per year" is_automatic="1" limits_behaviour="UNLIMITED" default_initial_credits="5" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="9" category_id="6"/>
    <package_category package_type_id="9" category_id="7"/>
    <package_category package_type_id="9" category_id="8"/>
    <package_location package_type_id="9" location_id="3"/>
    <package_location package_type_id="9" location_id="4"/>
    <package_location package_type_id="9" location_id="5"/>
    <package_product package_type_id="9" product_id="1"/>

    <package_type id="10" display_name="Limited cars in London" is_automatic="0" limits_behaviour="LIMITED" default_initial_credits="5" is_capability="0" payment_instrument="CREDIT"/>
    <package_category package_type_id="10" category_id="3"/>
    <package_location package_type_id="10" location_id="2"/>
    <package_product package_type_id="10" product_id="1"/>

    <credit_package id="1" package_type_id="1" account_id="1" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="ASALESFORCEID" deleted="0"/>
    <credit_package id="2" package_type_id="2" account_id="1" initial_credits="5" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-002" deleted="0"/>
    <credit_package id="3" package_type_id="3" account_id="1" initial_credits="5" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-003" deleted="0"/>
    <credit_package id="4" package_type_id="4" account_id="1" initial_credits="5" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-004" deleted="0"/>

    <credit_package id="5" package_type_id="5" account_id="1" initial_credits="5" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-005" deleted="0"/>
    <credit_package id="6" package_type_id="5" account_id="1" initial_credits="15" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2099-02-01 00:00:00" salesforce_id="SFID-CP-006" deleted="0"/>
    <credit_package id="7" package_type_id="6" account_id="1" initial_credits="10" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2099-02-01 00:00:00" salesforce_id="SFID-CP-007" deleted="1"/>

    <postcode id="1" postcode="SW185AS" latitude="55.55" longitude="11.11"/>
    <postcode_location location_id="2" postcode_id="1"/>

    <account id="2" name="Test Account 2" description="Second test account" is_pro="1" public_id="hash_2"/>
    <advert id="1"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="2"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package id="10" package_type_id="1" account_id="2" initial_credits="5" used_credits="5" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-010" deleted="0"/>
    <credit_package id="11" package_type_id="1" account_id="2" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-011" deleted="0"/>

    <account id="3" name="Test Account 3" description="Third test account" is_pro="1" public_id="hash_3"/>
    <advert id="2"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="3"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package id="12" package_type_id="1" account_id="3" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2023-12-12 00:00:00" salesforce_id="SFID-CP-012" deleted="0"/>
    <credit_package id="13" package_type_id="1" account_id="3" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2022-12-12 00:00:00" salesforce_id="SFID-CP-013" deleted="0"/>

    <account id="4" name="Test Account 4" description="Fourth test account" is_pro="1" public_id="hash_4"/>
    <advert id="3"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="4"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package id="14" package_type_id="1" account_id="4" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2023-01-01 00:00:00" salesforce_id="SFID-CP-014" deleted="0"/>
    <credit_package id="15" package_type_id="1" account_id="4" initial_credits="7" used_credits="6" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2023-01-01 00:00:00" salesforce_id="SFID-CP-015" deleted="0"/>

    <account id="5" name="Test Account 5" description="Fifth test account" is_pro="1" public_id="hash_5"/>
    <advert id="4"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="5"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package id="16" package_type_id="1" account_id="5" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-016" deleted="0"/>
    <credit_package id="17" package_type_id="1" account_id="5" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-017" deleted="0"/>

    <account id="6" name="Test Account 6" description="Sixth test account" is_pro="1" public_id="hash_6"/>
    <advert id="5"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="6"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package id="18" package_type_id="1" account_id="6" initial_credits="5" used_credits="1" creation_date="2012-01-01 01:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-018" deleted="0"/>
    <credit_package id="19" package_type_id="1" account_id="6" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-019" deleted="0"/>

    <account id="7" name="Test Account 7" description="Seventh test account" is_pro="1" public_id="hash_7"/>
    <advert id="6"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="7"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package id="20" package_type_id="1" account_id="7" initial_credits="5" used_credits="8" creation_date="2012-01-01 00:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-020" deleted="0"/>
    <credit_package id="21" package_type_id="1" account_id="7" initial_credits="5" used_credits="12" creation_date="2012-01-01 00:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SFID-CP-021" deleted="0"/>
    <credit_package id="22" package_type_id="6" account_id="1" initial_credits="15" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2099-02-01 00:00:00" salesforce_id="SFID-CP-022" deleted="0"/>
    <credit_package id="23" package_type_id="7" account_id="1" initial_credits="15" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2099-02-01 00:00:00" salesforce_id="LONDON_JOBS_PACKAGE" deleted="0"/>
    <credit_package id="24" package_type_id="8" account_id="8" initial_credits="15" used_credits="0" creation_date="2012-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2099-02-01 00:00:00" salesforce_id="ROUK_JOBS_PACKAGE" deleted="0"/>

    <postcode id="2" postcode="ABCD18" latitude="55.55" longitude="11.11"/>
    <postcode_location location_id="3" postcode_id="2"/>

    <account id="10" name="Test Account 10" description="Tenth test account" is_pro="0" public_id="hash_10"/>
    <advert id="7"
            title="Automatic package test ad"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="2"
            outcode="ABC"
            incode="D18"
            visible_on_map="0"
            account_id="10"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>

    <account id="11" name="Test Account 11" description="Eleventh test account" is_pro="1" public_id="hash_11"/>
    <advert id="8"
            title="Automatic package test ad for pro account"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="2"
            outcode="ABC"
            incode="D18"
            visible_on_map="0"
            account_id="11"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>

    <account id="12" name="Test Account 12" description="Twelfth test account" is_pro="0" public_id="hash_12"/>
    <advert id="9"
            title="Automatic package test ad where package has expired"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="2"
            outcode="ABC"
            incode="D18"
            visible_on_map="0"
            account_id="12"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package package_type_id="9" account_id="12" initial_credits="5" used_credits="3" creation_date="2011-01-01 00:00:00" start_date="2011-02-01 00:00:00" end_date="2012-02-01 00:00:00" deleted="0"/>

    <account id="13" name="Test Account 13" description="Thirteenth test account" is_pro="0" public_id="hash_13"/>
    <advert id="10"
            title="Automatic package test ad where package has expired"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="2"
            outcode="ABC"
            incode="D18"
            visible_on_map="0"
            account_id="13"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package package_type_id="9" account_id="13" initial_credits="5" used_credits="5" creation_date="2011-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2050-02-01 00:00:00" deleted="0"/>

    <!-- Account with jobs insertion in both London and RoUK-->
    <account id="14" name="Test Account 14" description="Fourteenth test account" is_pro="1" public_id="hash_14"/>
    <credit_package id="100" package_type_id="7" account_id="14" initial_credits="5" used_credits="0" creation_date="2011-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2050-02-01 00:00:00" salesforce_id="LONDON_JOBS_CP_100" deleted="0"/>
    <credit_package id="101" package_type_id="8" account_id="14" initial_credits="5" used_credits="0" creation_date="2011-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2012-06-01 00:00:00" salesforce_id="ROUK_JOBS_CP_101" deleted="0"/>
    <credit_package id="201" package_type_id="8" account_id="14" initial_credits="5" used_credits="0" creation_date="2011-01-01 00:00:00" start_date="2012-02-01 00:00:00" end_date="2050-02-01 00:00:00" salesforce_id="ROUK_JOBS_CP_201" deleted="0"/>

    <postcode id="3" postcode="M126AP" latitude="48.55" longitude="-7.11"/>
    <postcode_location location_id="5" postcode_id="3"/>

    <advert id="11"
            title="Specialist nursing job in manchester"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="3"
            outcode="M12"
            incode="6AP"
            visible_on_map="0"
            account_id="14"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="9"
            ip="127.0.0.1"/>

    <advert id="12"
            title="Specialist nursing job in london"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="14"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="9"
            ip="127.0.0.1"/>

    <credit_package id="202" package_type_id="10" account_id="10" initial_credits="5" used_credits="5" creation_date="2012-01-01 00:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="LONDON_CARS__LIMITED_1" deleted="0"/>
    <credit_package id="203" package_type_id="10" account_id="10" initial_credits="2" used_credits="2" creation_date="2012-01-01 00:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="LONDON_CARS__LIMITED_2" deleted="0"/>

    <package_type id="11" display_name="Limited cars in London Capability" is_automatic="0" limits_behaviour="LIMITED" default_initial_credits="5" is_capability="1" payment_instrument="CAPABILITY"/>
    <package_product package_type_id="10" product_id="14"/>

    <account id="15" name="Test Account 15" description="Fifteenth test account" is_pro="1" public_id="hash_15"/>
    <advert id="13"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="15"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="6"
            ip="127.0.0.1"/>
    <credit_package id="204" package_type_id="1" account_id="15" initial_credits="5" used_credits="1" creation_date="2012-01-01 01:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SF_CAPABILITY_01" deleted="0"/>
    <credit_package id="205" package_type_id="11" account_id="15" initial_credits="5" used_credits="1" creation_date="2012-01-01 00:00:00" start_date="2011-01-01 00:00:00" end_date="2099-01-01 00:00:00" salesforce_id="SF_CAPABILITY_02" deleted="0"/>

    <purchase_order id="1" created_date="2012-01-01 09:30:00" account_id="14"/>
    <purchase_order id="2" created_date="2012-01-02 09:30:00" account_id="14"/>
    <purchase_order id="3" created_date="2012-01-03 09:30:00" account_id="14"/>
    <purchase_order id="4" created_date="2012-01-04 09:30:00" account_id="14"/>
    <purchase_order id="5" created_date="2012-01-05 09:30:00" account_id="14"/>
    <purchase_order id="6" created_date="2012-01-05 09:30:00" account_id="7"/>
    <purchase_order id="7" created_date="2012-01-05 09:30:00" account_id="15"/>

    <purchase_order_item id="1" purchase_order_id="1" advert_id = "8" product_id="1" status="ACTIONED"/>
    <purchase_order_item id="2" purchase_order_id="1" advert_id = "8" product_id="2" status="ACTIONED"/>
    <purchase_order_item id="3" purchase_order_id="2" advert_id = "9" product_id="1" status="PAID"/>
    <purchase_order_item id="4" purchase_order_id="2" advert_id = "9" product_id="2" status="PAID"/>
    <purchase_order_item id="5" purchase_order_id="3" advert_id = "10" product_id="1" status="UNPAID"/>
    <purchase_order_item id="6" purchase_order_id="3" advert_id = "10" product_id="2" status="UNPAID"/>
    <purchase_order_item id="7" purchase_order_id="4" advert_id = "11" product_id="1" status="ACTIONED"/>
    <purchase_order_item id="8" purchase_order_id="4" advert_id = "11" product_id="2" status="ACTIONED"/>
    <purchase_order_item id="9" purchase_order_id="5" advert_id = "12" product_id="1" status="UNPAID"/>
    <purchase_order_item id="10" purchase_order_id="5" advert_id = "12" product_id="2" status="UNPAID"/>
    <purchase_order_item id="11" purchase_order_id="6" advert_id = "12" product_id="2" status="PAID"/>
    <purchase_order_item id="12" purchase_order_id="6" advert_id = "12" product_id="2" status="PAID"/>
    <purchase_order_item id="13" purchase_order_id="7" advert_id = "13" product_id="1" status="PAID"/>
    <purchase_order_item id="14" purchase_order_id="7" advert_id = "13" product_id="14" status="PAID"/>

    <package_payment id="1" purchase_order_item_id="1" credit_package_id="100" payment_date="2012-01-01 00:00:00"/>
    <package_payment id="2" purchase_order_item_id="2" credit_package_id="101" payment_date="2012-01-02 00:00:00"/>
    <package_payment id="3" purchase_order_item_id="3" credit_package_id="100" payment_date="2012-01-03 00:00:00"/>
    <package_payment id="4" purchase_order_item_id="4" credit_package_id="101" payment_date="2012-01-04 00:00:00"/>
    <package_payment id="5" purchase_order_item_id="5" credit_package_id="100"/>
    <package_payment id="6" purchase_order_item_id="6" credit_package_id="101"/>
    <package_payment id="7" purchase_order_item_id="7" credit_package_id="100" payment_date="2012-01-05 00:00:00"/>
    <package_payment id="8" purchase_order_item_id="8" credit_package_id="101" payment_date="2012-01-06 00:00:00"/>
    <package_payment id="9" purchase_order_item_id="9" credit_package_id="100" payment_date="2012-01-07 00:00:00"/>
    <package_payment id="10" purchase_order_item_id="10" credit_package_id="101" payment_date="2012-01-08 00:00:00"/>
    <package_payment id="11" purchase_order_item_id="11" credit_package_id="20" payment_date="2012-01-09 00:00:00"/>
    <package_payment id="12" purchase_order_item_id="12" credit_package_id="21" payment_date="2012-01-10 00:00:00"/>
    <package_payment id="13" purchase_order_item_id="13" credit_package_id="204" payment_date="2012-01-09 00:00:00"/>
    <package_payment id="14" purchase_order_item_id="14" credit_package_id="205" payment_date="2012-01-10 00:00:00"/>

</dataset>
