<dataset>


    <role id="1" name="SUPER_USER" description="User can do everything"/>
    <role_permission id="1" role_id="1" domain="account" action="managepackages" target="*"/>

    <usr id="1" username_id="1" password="password" salt="salt" first_name="<PERSON>" last_name="Bloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="1" role_id="1"/>
    <email_address id="1" email="user1" />

    <account id="1" name="Test Account 1" description="First test account" is_pro="0" public_id="1ca15c6ccd265eaaae5e25a2964818222"/>
    <account id="2" name="Test Account 2" description="Second test account" is_pro="0" public_id="22153b9c4d1c8f3b2482f6764b57e0580"/>

    <usr_account account_id="1" usr_id="1" />
    <usr_account account_id="2" usr_id="2" />

    <image id="152001" url="http://domain.com/01.JPG" />

    <image id="152002" url="http://domain.com/02.JPG" />
    <media id="152002" url="https://imagedelivery.net/gt/001/2" />

    <account_image account_id="1" image_id="152001"/>
    <account_image account_id="2" image_id="152002"/>

</dataset>
