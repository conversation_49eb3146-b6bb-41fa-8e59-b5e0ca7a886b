<dataset>
    <role id="1" name="API_USER" description="User can post adverts via the Write API"/>
    <role id="2" name="SUPER_USER" description="User can do anything"/>
    <role id="3" name="ADMIN_USER" description="User can do almost anything"/>
    <usr id="1" username_id="1" password="password1" salt="salt1" first_name="sam" last_name="smith" status="ACTIVE" version="1"/>
    <email_address id="1" email="user1"/>
    <usr_role usr_id="1" role_id="1"/>
    <usr_role usr_id="1" role_id="2"/>
    <role_permission id="1" role_id="1" domain="domain1" action="add" target="*"/>
    <role_permission id="2" role_id="1" domain="domain1" action="delete" target="*"/>
    <role_permission id="3" role_id="1" domain="domain1" action="update" target="*"/>
    <usr_permission id="1" usr_id="1" domain="domain2" action="add" target="1"/>
    <usr_permission id="2" usr_id="1" domain="domain2" action="delete" target="1"/>
    <usr_permission id="3" usr_id="1" domain="domain2" action="update" target="1"/>

    <api_key id="1" usr_id="1" access_key="testApiUser1AccessKey1" private_key="testApiUser1PrivateKey1"/>
    <api_key id="2" usr_id="1" access_key="testApiUser1AccessKey2" private_key="testApiUser1PrivateKey2"/>

    <usr id="2" username_id="2" password="password2" salt="salt2" first_name="bob" last_name="sleigh" status="ACTIVE" version="1"/>
    <email_address id="2" email="user-with-no-roles"/>

    <user_marketing_preference id="1" email_address_id="3" opt_in_gumtree="false" opt_in_ebay="true" opt_in_third_party="false" postcode_location="8888"/>
    <user_marketing_preference_category id="1" user_marketing_preference_id="1" category_id="1234"/>
    <user_marketing_preference_category id="2" user_marketing_preference_id="1" category_id="5678"/>


    <email_address id="3" email="<EMAIL>"/>

    <!-- 3rd user -->
    <usr id="3" username_id="4" password="password1" salt="salt1" first_name="sam" last_name="admin" status="ACTIVE" version="1"/>
    <email_address id="4" email="<EMAIL>"/>
    <api_key id="3" usr_id="3" access_key="user3" private_key="user3"/>
    <api_key id="4" usr_id="3" access_key="user3-other" private_key="user3-other"/>

    <!-- deleting testing -->
    <usr id="10" username_id="10" password="password" salt="salt" first_name="sam" last_name="admin" status="ACTIVE" version="1"/>
    <email_address id="10" email="delete1"/>
    <gender usr_id="10" gender="male"/>
    <social_data usr_id="10" platform="FACEBOOK_LOGIN"/>
    <usr_role usr_id="10" role_id="1"/>
    <usr_role usr_id="10" role_id="2"/>

    <usr id="11" username_id="11" password="password" salt="salt" first_name="sam" last_name="admin" status="ACTIVE" version="1"/>
    <email_address id="11" email="delete2"/>
    <gender usr_id="11" gender="male"/>
    <social_data usr_id="11" platform="GOOGLE_LOGIN"/>
    <usr_role usr_id="11" role_id="1"/>
    <usr_role usr_id="11" role_id="2"/>

</dataset>
