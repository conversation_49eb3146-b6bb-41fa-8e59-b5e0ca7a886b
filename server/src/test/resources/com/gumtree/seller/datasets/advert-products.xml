<dataset>
    <role id="1" name="SUPER_USER" description="User can do everything"/>
    <role_permission id="1" role_id="1" domain="account" action="managepackages" target="*"/>

    <usr id="1" username_id="1" password="password" salt="salt" first_name="<PERSON>" last_name="Bloggs" contact_phone="01632 960001" status="ACTIVE" version="1"/>
    <usr_role usr_id="1" role_id="1"/>
    <email_address id="1" email="user1" />

    <account id="1" name="Test Account 1" description="First test account" is_pro="0" public_id="hash_1"/>
    <account id="2" name="Test Account 2" description="Second test account" is_pro="0" public_id="hash_2"/>

    <location id="1" name="uk" is_landing="0" display_name="United Kingdom"/>
    <location id="2" name="london" is_landing="0" display_name="London"/>

    <postcode id="1" postcode="SW185AS" latitude="55.55" longitude="11.11"/>
    <postcode_location location_id="2" postcode_id="1"/>

    <product id="1" name="INSERTION"/>
    <product id="2" name="URGENT"/>
    <product id="3" name="BUMP_UP"/>

    <package_type id="1" display_name="London Jobs Insertion" salesforce_product_id="SFID-PT-001" is_automatic="0" limits_behaviour="ACCRUED" payment_instrument="CREDIT" default_initial_credits="100" />
    <package_category package_type_id="1" category_id="4"/>
    <package_location package_type_id="1" location_id="2"/>
    <package_product package_type_id="1" product_id="1"/>

    <credit_package
            id="1" package_type_id="1" account_id="1" salesforce_id="SFID-CP-001" initial_credits="5" used_credits="0"
            creation_date="2012-01-01 00:00:00" start_date="2012-01-01 00:00:00" end_date="2030-01-01 00:00:00" />

    <advert id="1"
            title="This title must change"
            description="this description must change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.oldurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="4"
            ip="127.0.0.1"/>

    <advert id="2"
            title="This title must also change"
            description="this description must also change"
            created_date="2011-01-01 09:30:00"
            status="LIVE"
            contact_email="<EMAIL>"
            contact_telephone="01632 960001"
            contact_url="http://www.newurl.com"
            published_date="2011-01-01 10:30:00"
            last_modified_date="2011-01-01 10:25:00"
            postcode_id="1"
            outcode="SW18"
            incode="5AS"
            visible_on_map="0"
            account_id="1"
            version="1"
            created_by="1"
            last_modified_by="1"
            category_id="4"
            ip="127.0.0.1"/>

    <!-- advert, insertion and urgent -->
    <purchase_order id="101" account_id="1" created_date="2012-01-01 00:00:00" fulfillment_date="2012-01-01 15:00:00" />
    <purchase_order_item id="101" purchase_order_id="101" advert_id="1" product_id="1" status="actioned" price_inc_vat="1150" />
    <purchase_order_item id="102" purchase_order_id="101" advert_id="1" product_id="2" status="actioned" price_inc_vat="989"/>

    <purchase_order_item_payment purchase_order_item_id="101" payment_id="1" />
    <payment id="1" payment_date="2012-01-02 15:10:00" vat_amount="1150" amount="1100" payment_method="MANUAL" />

    <!-- advert bump up paid by package -->
    <purchase_order id="102" account_id="1" created_date="2012-01-06 14:00:00" fulfillment_date="2012-01-06 14:05:00"/>
    <purchase_order_item id="103" purchase_order_id="102" advert_id="1" product_id="3" status="actioned" price_inc_vat="550"/>

    <package_payment id="1" purchase_order_item_id="103" payment_date="2012-01-06 14:55:00" credit_package_id="1" />
</dataset>
