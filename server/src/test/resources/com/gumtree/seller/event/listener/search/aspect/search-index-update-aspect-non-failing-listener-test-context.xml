<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa-1.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd">

    <context:annotation-config/>
    <aop:aspectj-autoproxy/>

    <bean id="searchIndexListenerLoggingAspect"
          class="com.gumtree.seller.event.listener.search.aspect.SearchIndexListenerLoggingAspect"/>
    <bean id="mockSearchIndexListener" class="com.gumtree.seller.event.listener.search.MockSearchIndexListener">
        <constructor-arg value="false"/>
    </bean>
    <bean id="loggingService" class="com.gumtree.seller.test.LoggingServiceFactoryBean"/>
    <bean id="orderVerifier" class="com.gumtree.seller.test.OrderVerifierFactoryBean"/>

</beans>
