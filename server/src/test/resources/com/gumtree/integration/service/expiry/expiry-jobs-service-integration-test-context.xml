<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                            http://www.springframework.org/schema/beans/spring-beans.xsd
                            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <bean id="advertsService" class="com.gumtree.api.service.expiry.ExpireAdvertsServiceImpl"/>
    <bean id="draftAdvertsService" class="com.gumtree.api.service.expiry.ArchiveDraftAdvertsServiceImpl"/>
    <bean id="ordersService" class="com.gumtree.api.service.expiry.ExpireOrdersServiceImpl"/>

    <bean id="sellerDatasource" class="org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseFactoryBean">
        <property name="databaseType" value="H2"/>
        <!--<property name="databaseName" value="sellerdb-#{ T(java.util.UUID).randomUUID().toString() }"/>-->
    </bean>

    <bean id="springLiquibase" class="liquibase.integration.spring.SpringLiquibase">
        <property name="dataSource" ref="sellerDatasource"/>
        <property name="changeLog" value="classpath:com/gumtree/seller/liquibase/changelog-master.xml"/>
    </bean>

    <bean id="sellerDatabaseOperations" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="sellerDatasource"/>
    </bean>

    <bean id="clock" class="com.gumtree.test.FixedTimeClock"/>

    <context:property-placeholder
            location="classpath:/com/gumtree/api/service/expiry/bapi-server.properties"
            ignore-resource-not-found="true"
            system-properties-mode="OVERRIDE"/>

</beans>