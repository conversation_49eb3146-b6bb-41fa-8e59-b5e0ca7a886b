<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jpa="http://www.springframework.org/schema/data/jpa"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:ehcache="http://ehcache-spring-annotations.googlecode.com/svn/schema/ehcache-spring"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		                http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa-1.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd
		                http://ehcache-spring-annotations.googlecode.com/svn/schema/ehcache-spring http://ehcache-spring-annotations.googlecode.com/svn/schema/ehcache-spring/ehcache-spring-1.1.xsd">

    <context:property-placeholder ignore-resource-not-found="true" ignore-unresolvable="false"/>
    <bean class="com.codahale.metrics.MetricRegistry" id="metricRegistry" />

    <bean class="com.gumtree.config.DroolsConfig"/>
    <bean class="com.gumtree.config.PricingConfig"/>

    <context:component-scan base-package="com.gumtree.seller.test"/>
    <context:component-scan base-package="com.gumtree.seller.service">
        <context:exclude-filter type="annotation" expression="org.springframework.context.annotation.Configuration" />
        <context:exclude-filter type="regex" expression="com\.gumtree\.seller\.service\.advert\.expiry\.ExpiryDateApplicatorImpl"/>
        <!--<context:exclude-filter type="regex" expression="com\.gumtree\.seller\.service\.packages.CreditPackageConfig"/>-->
        <context:exclude-filter type="regex" expression="com\.gumtree\.seller\.service\.reindex.*"/>
        <context:exclude-filter type="regex" expression="com\.gumtree\.seller\.service\..*Context"/>
    </context:component-scan>

    <context:component-scan base-package="com.gumtree.seller.notifications"/>
    <context:component-scan base-package="com.gumtree.seller.email"/>
    <context:component-scan base-package="com.gumtree.common.email"/>
    <context:component-scan base-package="com.gumtree.seller.event"/>
    <context:component-scan base-package="com.gumtree.seller.repository"/>
    <context:component-scan base-package="com.gumtree.gumshield.api"/>
    <context:component-scan base-package="com.gumtree.api.converter"/>
    <context:component-scan base-package="com.gumtree.personalization">
        <context:exclude-filter type="annotation" expression="org.springframework.context.annotation.Configuration" />
    </context:component-scan>

    <bean class="com.gumtree.api.config.StubCategoryModelConfig" />

    <context:annotation-config/>

    <aop:aspectj-autoproxy/>
    <tx:annotation-driven/>
    <ehcache:annotation-driven cache-manager="sellerCacheManager" create-missing-caches="true"/>

    <import resource="classpath:/META-INF/config/spring/seller-email-context.xml"/>
    <import resource="classpath:/META-INF/config/spring/seller-event-context.xml"/>
    <import resource="classpath:/META-INF/config/spring/seller-mongo-context.xml"/>

    <jpa:repositories base-package="com.gumtree.seller.repository"/>
    <jpa:auditing auditor-aware-ref="securityServiceImpl"/>

    <bean id="conversionService" class="org.springframework.context.support.ConversionServiceFactoryBean"/>

    <bean id="sellerDatasource" primary="true" class="org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseFactoryBean">
        <property name="databaseType" value="H2"/>
        <!--<property name="databaseName" value="sellerdb-#{ T(java.util.UUID).randomUUID().toString() }"/>-->
    </bean>

    <bean id="sellerDatabaseTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="sellerDatasource"/>
    </bean>

    <bean id="transactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <constructor-arg ref="transactionManager"/>
    </bean>

    <bean id="sellerDatabaseOperations" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg name="dataSource" ref="sellerDatasource"/>
    </bean>

    <bean id="sellerJpaTemplate" class="org.springframework.orm.jpa.JpaTemplate">
        <property name="entityManagerFactory" ref="entityManagerFactory"/>
    </bean>

    <bean id="expiryDateApplicator" class="com.gumtree.seller.service.advert.expiry.ExpiryDateApplicatorImpl"
          depends-on="springLiquibase" />

    <bean id="springLiquibase" class="liquibase.integration.spring.SpringLiquibase">
        <property name="dataSource" ref="sellerDatasource"/>
        <property name="changeLog" value="classpath:com/gumtree/seller/liquibase/changelog-master.xml"/>
    </bean>

    <bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
        <property name="entityManagerFactory" ref="entityManagerFactory"/>
    </bean>

    <bean id="entityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
        <property name="dataSource" ref="sellerDatasource"/>
        <property name="jpaDialect">
            <bean class="com.gumtree.seller.util.Spring41HibernateJpaDialect"/>
        </property>
        <property name="jpaVendorAdapter">
            <bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
                <property name="database" value="H2"/>
                <property name="showSql" value="true"/>
            </bean>
        </property>
        <property name="persistenceUnitName" value="gumtree.seller"/>
        <property name="jpaProperties">
            <map>
                <entry key="hibernate.show_sql" value="false"/>
                <entry key="hibernate.cache.region.factory_class" value="org.hibernate.cache.ehcache.EhCacheRegionFactory"/>
                <entry key="hibernate.cache.use_second_level_cache" value="false"/>
                <entry key="net.sf.ehcache.configurationResourceName" value="/META-INF/ehcache-jpa.xml"/>
                <entry key="hibernate.jdbc.batch_size" value="1000"/>
            </map>
        </property>
    </bean>

    <bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean"/>

    <bean id="csNotificationExecutor" class="com.gumtree.seller.util.concurrent.ThreadPoolFactoryBean">
        <constructor-arg name="threadCount" value="${gumtree.seller.cs.notification.thread.count:5}"/>
    </bean>

    <bean id="searchIndexNotificationExecutor" class="com.gumtree.seller.util.concurrent.ThreadPoolFactoryBean">
        <constructor-arg name="threadCount" value="${gumtree.seller.search.notification.thread.count:5}"/>
    </bean>

    <bean id="emailDispatchExecutor" class="com.gumtree.seller.util.concurrent.ThreadPoolFactoryBean">
        <constructor-arg name="threadCount" value="${gumtree.seller.email.dispatch.thread.count:5}"/>
    </bean>


    <bean id="sellerCacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
        <property name="configLocation" value="classpath:/META-INF/config/ehcache-seller.xml"/>
        <property name="shared" value="true"/>
    </bean>


    <bean id="loggingService" class="com.gumtree.common.logging.service.DummyLoggingService"/>

    <bean id="requestTokenService"
          class="com.gumtree.common.logging.service.requesttoken.ThreadLocalRequestTokenService"/>

    <bean id="jsonAttributeEnumValues" class="org.springframework.core.io.ClassPathResource">
        <constructor-arg name="path" value="com/gumtree/seller/domain/attribute/attribute-enum-labels.json"/>
    </bean>

    <bean id="platformRealm" class="com.gumtree.seller.security.PlatformRealm"/>

    <bean id="payPalIPNConfigBean" class="com.gumtree.seller.service.payment.paypal.ipn.PayPalIPNConfigBean">
        <property name="nvpUrl" value ="${gumtree.seller.paypal.nvp.url:https://api-3t.sandbox.paypal.com/nvp}"/>
        <property name="ipnVerificationUrl" value="${gumtree.seller.paypal.ipn.verification.url:https://www.paypal.com/cgi-bin/webscr}"/>
        <property name="business" value="${gumtree.seller.paypal.email:<EMAIL>}"/>
    </bean>

    <bean id="payPalHttpClient" class="com.gumtree.seller.test.MockHttpClient"/>

    <bean id="payPalPaymentServiceImpl" class="com.gumtree.seller.service.payment.paypal.PayPalPaymentServiceImpl">
        <constructor-arg>
            <bean class="com.gumtree.seller.test.MockHttpClient"/>
        </constructor-arg>
    </bean>

    <bean id="testClock" class="com.gumtree.common.util.time.StoppedClock" />

    <bean id="gumshieldApi" class="com.gumtree.gumshield.api.client.impl.RemoteGumshieldApiFactoryBean">
        <property name="baseUri" value="http://dummy"/>
    </bean>

    <bean class="com.gumtree.seller.notifications.akka.AdvertNotificationAkkaSystemConfig" destroy-method="destroy"/>

    <beans profile="stubbed-external-api">
        <!-- overrides using stubs -->
        <bean name="defaultEmailSender" class="com.gumtree.seller.email.StubEmailSender" />
    </beans>
</beans>
