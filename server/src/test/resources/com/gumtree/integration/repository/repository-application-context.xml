<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jpa="http://www.springframework.org/schema/data/jpa"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		                http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa-1.0.xsd">

    <context:property-placeholder ignore-resource-not-found="true" ignore-unresolvable="false"/>



    <jpa:repositories base-package="com.gumtree.seller.repository" />
    <jpa:repositories base-package="com.gumtree.wallet.packages.repository" />

    <bean class="com.gumtree.seller.repository.advert.DuplicatedAdvertRepository"/>


    <bean id="sellerDatasource" primary="true" class="org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseFactoryBean">
        <property name="databaseType" value="H2"/>
        <property name="databaseName" value="seller-repository-test"/>
    </bean>



    <bean id="springLiquibase" class="liquibase.integration.spring.SpringLiquibase">
        <property name="dataSource" ref="sellerDatasource"/>
        <property name="changeLog" value="classpath:com/gumtree/seller/liquibase/changelog-master.xml"/>
    </bean>

    <bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
        <property name="entityManagerFactory" ref="entityManagerFactory"/>
    </bean>

    <bean id="sellerDatabaseOperations" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="sellerDatasource"/>
    </bean>


    <bean id="advertFeaturesRepository" class="com.gumtree.seller.service.advert.features.AdvertFeaturesRepository">
    </bean>

    <bean id="advertJdbcOperations" class="com.gumtree.seller.repository.advert.AdvertJdbcOperations" >
        <constructor-arg ref="sellerDatabaseOperations"/>
    </bean>

    <bean id="entityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
        <property name="dataSource" ref="sellerDatasource"/>
        <property name="jpaDialect">
            <bean class="com.gumtree.seller.util.Spring41HibernateJpaDialect"/>
        </property>
        <property name="jpaVendorAdapter">
            <bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
                <property name="database" value="H2"/>
                <property name="showSql" value="true"/>
            </bean>
        </property>
        <property name="persistenceUnitName" value="gumtree.seller"/>
        <property name="jpaProperties">
            <map>
                <entry key="hibernate.show_sql" value="true"/>
                <entry key="hibernate.cache.region.factory_class" value="org.hibernate.cache.ehcache.EhCacheRegionFactory"/>
                <entry key="hibernate.cache.use_second_level_cache" value="false"/>
                <entry key="net.sf.ehcache.configurationResourceName" value="/META-INF/ehcache-jpa.xml"/>
                <entry key="hibernate.jdbc.batch_size" value="1000"/>
            </map>
        </property>
    </bean>

    <bean id="sellerCacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
        <property name="configLocation" value="classpath:/META-INF/config/ehcache-seller.xml"/>
        <property name="shared" value="true"/>
    </bean>

    <bean id="eventService" class="com.gumtree.seller.service.util.TestEventService"/>
    <context:component-scan base-package="com.gumtree.seller.domain">
        <context:include-filter type="regex" expression="com\.gumtree\.seller\.domain.*\.entity.listener"/>
    </context:component-scan>

</beans>
