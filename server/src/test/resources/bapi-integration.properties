gumtree.seller.jdbc.driver=org.h2.Driver
gumtree.seller.jdbc.url=jdbc:h2:~/.gumtree/seller/db/seller;AUTO_SERVER=TRUE;MODE=PostgreSQL
gumtree.seller.jdbc.username=seller
gumtree.seller.jdbc.password=seller
gumtree.seller.jdbc.pool.size.max=30
gumtree.seller.jdbc.pool.min.idle=3
gumtree.seller.jdbc.pool.idle.timeout=60000
gumtree.seller.jdbc.pool.max.lifetime=60000
gumtree.seller.jdbc.pool.connection.timeout=60000
gumtree.seller.jdbc.read.url=jdbc:h2:~/.gumtree/seller/db/seller;AUTO_SERVER=TRUE;MODE=PostgreSQL
gumtree.seller.jdbc.read.username=seller
gumtree.seller.jdbc.read.password=seller
gumtree.seller.jdbc.read.pool.size.max=30
gumtree.seller.jdbc.read.pool.min.idle=3
gumtree.seller.jdbc.read.pool.idle.timeout=60000
gumtree.seller.jdbc.read.pool.max.lifetime=60000
gumtree.seller.jdbc.read.pool.connection.timeout=60000
gumtree.seller.database.provider=H2
gumtree.seller.emailSender.synchronous=true
gumtree.mailgun.smtp_port=2500
gumtree.api.security.cache.enabled=false
gumtree.search.engine=stub
search.api.live_site.updates.enabled=true
search.api.full.updates.enabled=true
search.api.base_uri=http://localhost:9393/api
net.sf.ehcache.disabled=true
gumtree.notify.async.enabled=false
gumtree.seller.price.cache.warming.enabled=false
gumtree.notify.sapi.parallel=true
spring.profiles.active=category-api-stub
gumtree.replies.replyts1.platformid=gumtreeuk
gumtree.replies.replyts2.platformid=gumtreeuk2
gumtree.replies.replyts2.groupsize=0
gumtree.replies.split.override.prefix=gumtreeOverridePrefix

# Pricing API
gumtree.pricing.api.host=priceapi.gt-staging.ams1.cloud
gumtree.pricing.api.port=80
gumtree.pricing.api.basepath=http://${gumtree.pricing.api.host}:${gumtree.pricing.api.port}
gumtree.pricing.api.connection.timeout=5000
gumtree.pricing.api.read.timeout=30000