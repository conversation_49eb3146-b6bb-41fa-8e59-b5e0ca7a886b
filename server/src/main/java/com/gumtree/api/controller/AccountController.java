package com.gumtree.api.controller;

import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.BooleanResult;
import com.gumtree.api.CreditPackages;
import com.gumtree.api.SellerType;
import com.gumtree.api.ValueResult;
import com.gumtree.api.controller.editor.AccountReplyTypeEditor;
import com.gumtree.api.controller.exception.NewAdvertStatusFailedException;
import com.gumtree.api.converter.advert.AdvertToApiAdvertConverter;
import com.gumtree.api.domain.account.CreateAccountBean;
import com.gumtree.api.domain.account.DefaultImageBean;
import com.gumtree.api.domain.advert.NewStatusBean;
import com.gumtree.seller.domain.account.AccountCommand;
import com.gumtree.seller.domain.account.type.AccountReplyType;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.domain.user.entity.User;
import com.gumtree.seller.domain.user.exception.UserNotFoundException;
import com.gumtree.seller.service.account.AccountService;
import com.gumtree.seller.service.advert.AdvertService;
import com.gumtree.seller.service.user.UserService;
import com.gumtree.seller.util.validation.ValidationErrorProcessor;
import com.gumtree.wallet.WalletService;
import com.gumtree.wallet.packages.domain.entity.CreditPackage;
import com.gumtree.wallet.packages.service.CreditPackageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.ConversionService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.codahale.metrics.MetricRegistry.name;

/**
 * Accounts Endpoint Controller
 */
@Controller
public final class AccountController extends BaseExceptionHandlingController {

    private static final Logger LOG = LoggerFactory.getLogger(AccountController.class);

    private CreditPackageService creditPackageService;

    private WalletService walletService;

    private AdvertService advertService;

    private ConversionService conversionService;

    private AdvertToApiAdvertConverter advertConverter;

    private AccountService accountService;

    private UserService userService;

    private ValidationErrorProcessor validationErrorProcessor;

    private final Meter updateAccountMeter;

    private List<ProductName> products = Arrays.asList(ProductName.INSERTION, ProductName.BUMP_UP,
            ProductName.FEATURE_14_DAY, ProductName.FEATURE_3_DAY, ProductName.FEATURE_7_DAY, ProductName.URGENT,
            ProductName.HOMEPAGE_SPOTLIGHT);

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(AccountReplyType.class, new AccountReplyTypeEditor());
    }

    /**
     * Massive auto wired constructor
     *  @param creditPackageService credit package service, credit stuff
     * @param walletService wallet service, credit stuff
     * @param advertService advert service, performs actions on adverts
     * @param conversionService conversion service, converts objects to domain objects
     * @param advertConverter advert converter, converts adverts to domain adverts
     * @param accountService account service, performs actions on accounts
     * @param userService user service, performs actions on users
     * @param validationErrorProcessor validation error processor, handles validations errors
     */
    @Autowired
    public AccountController(CreditPackageService creditPackageService,
                             WalletService walletService, AdvertService advertService,
                             ConversionService conversionService,
                             AdvertToApiAdvertConverter advertConverter,
                             AccountService accountService,
                             UserService userService,
                             ValidationErrorProcessor validationErrorProcessor,
                             MetricRegistry metricRegistry) {
        this.creditPackageService = creditPackageService;
        this.walletService = walletService;
        this.advertService = advertService;
        this.conversionService = conversionService;
        this.advertConverter = advertConverter;
        this.accountService = accountService;
        this.userService = userService;
        this.validationErrorProcessor = validationErrorProcessor;
        this.updateAccountMeter = metricRegistry.meter(name(AccountController.class, "requests_updateAccount"));
    }

    /**
     * Create and return a new account
     *
     * @param createAccountBean account details
     * @return account
     */
    @RequestMapping(value = "/accounts", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.CREATED)
    @ResponseBody
    public Account createProAccount(@RequestBody CreateAccountBean createAccountBean) {
        validationErrorProcessor.validate(createAccountBean);
        AccountCommand command = conversionService.convert(createAccountBean, AccountCommand.class);

        LOG.info("createProAccount for {}", createAccountBean.getPrimaryEmail());

        com.gumtree.seller.domain.account.entity.Account account;

        try {
            User user = userService.getUser(createAccountBean.getPrimaryEmail());
            if (user.getAccounts().size() == 1 && !user.getAccounts().get(0).isProAccount()) {
                LOG.info("createProAccount for {} - updating account", createAccountBean.getPrimaryEmail());
                account = accountService.updateAccount(user.getAccounts().get(0).getId().longValue(), command);
            } else {
                LOG.info("createProAccount for {} - creating account", createAccountBean.getPrimaryEmail());
                account = accountService.createAccount(command);
            }
        } catch (UserNotFoundException ex) {
            LOG.info("createProAccount for {} - user not found", createAccountBean.getPrimaryEmail());
            account = accountService.createAccount(command);
        }

        LOG.info("createProAccount for {} - created account {}", createAccountBean.getPrimaryEmail(), account.getId());

        return conversionService.convert(account, Account.class);
    }

    /**
     * Update and return an account
     *
     * @param accountId         the account ID
     * @param createAccountBean account details
     * @return account
     */
    @RequestMapping(value = "/accounts/{accountId}", method = RequestMethod.PUT)
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public Account updateAccount(@PathVariable("accountId") Long accountId,
                                 @RequestBody CreateAccountBean createAccountBean) {
        updateAccountMeter.mark();
        validationErrorProcessor.validate(createAccountBean);
        AccountCommand command = conversionService.convert(createAccountBean, AccountCommand.class);

        com.gumtree.seller.domain.account.entity.Account account;

        account = accountService.updateAccount(accountId, command);

        return conversionService.convert(account, Account.class);
    }

    /**
     * Return list of packages associated with the given account
     *
     * @param accountId account id to check
     * @return list of CreditPackage
     */
    @RequestMapping(value = "/accounts/{id}/packages", method = RequestMethod.GET)
    @ResponseBody
    public CreditPackages getPackages(@PathVariable("id") String accountId,
                                      @RequestParam(value = "includeCapabilities",
                                              required = false, defaultValue = "false") Boolean includeCapabilities) {
        List<CreditPackage> creditPackages = walletService.getPackagesForAccount(Long.valueOf(accountId), includeCapabilities);
        CreditPackages packages = new CreditPackages();
        for (CreditPackage creditPackage : creditPackages) {
            packages.addPackage(conversionService.convert(creditPackage, com.gumtree.api.CreditPackage.class));
        }
        return packages;
    }

    /**
     * Checks whether account, category and location combination has assigned or is eligible for any credit packages
     *
     * @param accountId  account id to check
     * @param locationId location id
     * @param categoryId category id
     * @return boolean
     */
    @RequestMapping(value = "/accounts/{accountId}/eligible_packages", method = RequestMethod.GET)
    @ResponseBody
    public BooleanResult hasEligiblePackages(@PathVariable("accountId") Long accountId,
                                             @RequestParam(value = "location_id", required = true) Long locationId,
                                             @RequestParam(value = "category_id", required = true) Long categoryId) {
        BooleanResult result = new BooleanResult();
        boolean hasPackages = walletService.isCoveredByPackage(locationId, categoryId, accountId, ProductName.INSERTION);
        result.setValue(hasPackages);
        return result;
    }

    /**
     * Checks whether account, category and location combination has assigned or is eligible for website url credit packages
     *
     * @param accountId  account id to check
     * @param locationId location id
     * @param categoryId category id
     * @return boolean
     */
    @RequestMapping(value = "/accounts/{accountId}/url_package", method = RequestMethod.GET)
    @ResponseBody
    public BooleanResult hasUrlPackage(@PathVariable("accountId") Long accountId,
                                       @RequestParam(value = "location_id") Long locationId,
                                       @RequestParam(value = "category_id") Long categoryId) {
        boolean hasEligiblePackages = walletService.isCoveredByPackage(locationId, categoryId, accountId, ProductName.WEBSITE_URL);
        return new BooleanResult(hasEligiblePackages);
    }

    /**
     * Return list of adverts associated with the given account
     *
     * @param accountId account id to check
     * @return list of Ad
     */
    @RequestMapping(value = "/accounts/{id}/adverts", method = RequestMethod.GET)
    @ResponseBody
    public List<Ad> getAdverts(@PathVariable("id") String accountId) {
        List<Advert> adverts = advertService.getByAccountId(Long.valueOf(accountId));
        List<Ad> apiAd;
        apiAd = new ArrayList<>();
        for (Advert advert : adverts) {
            apiAd.add(advertConverter.convert(advert));
        }
        return apiAd;
    }

    /**
     * Get all Account associated with the id
     *
     * @param accountId account id
     * @return associated account
     */
    @RequestMapping(value = "/accounts/{id}", method = RequestMethod.GET)
    @ResponseBody
    public Account getAccount(@PathVariable("id") String accountId) {
        com.gumtree.seller.domain.account.entity.Account sellerTypeAccount =
                accountService.getAccountRO(Long.valueOf(accountId));
        return conversionService.convert(sellerTypeAccount, Account.class);

    }

    /**
     * Get all email addresses associated with the id
     *
     * @param accountId account id
     * @return associated email addresses
     */
    @RequestMapping(value = "/accounts/{id}/emails", method = RequestMethod.GET)
    @ResponseBody
    public List<String> getEmailAddress(@PathVariable("id") String accountId) {
        List<String> accountEmails = accountService.getAccountEmails(Long.valueOf(accountId));
        return accountEmails;
    }

    /**
     * Get an Account associated with the public id (hash)
     *
     * @param publicId public account id
     * @return associated account
     */
    @RequestMapping(value = "/accounts/public_id/{publicId}", method = RequestMethod.GET)
    @ResponseBody
    public Account getAccountByPublicId(@PathVariable("publicId") String publicId) {
        com.gumtree.seller.domain.account.entity.Account sellerTypeAccount =
                accountService.getAccountByPublicId(publicId);
        return conversionService.convert(sellerTypeAccount, Account.class);
    }

    /**
     * Handler for setting a new status on all adverts for an account
     *
     * @param accountId  the id of account
     * @param statusBean the new status for all adverts
     */
    @RequestMapping(value = "/accounts/{accountId}/advert_statuses", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    public void newAdvertStatuses(@PathVariable("accountId") Long accountId, @RequestBody NewStatusBean statusBean) {
        try {
            validationErrorProcessor.validate(statusBean);
            AdvertStatus status = AdvertStatus.valueOf(statusBean.getStatus());
            if (status == AdvertStatus.DELETED_USER) {
                advertService.retractAdvertsForAccount(accountId);
            } else {
                throw new UnsupportedOperationException();
            }
        } catch (RuntimeException ex) {
            throw new NewAdvertStatusFailedException(ex, accountId.toString(), statusBean);
        }

    }

    /**
     * Return the current set default image for an account
     *
     * @param accountId the account id
     * @return the image (id and url)
     */
    @RequestMapping(value = "/accounts/{accountId}/defaultImage", method = RequestMethod.GET)
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseEntity<Image> getDefaultImage(@PathVariable("accountId") Long accountId) {
        Image defaultImage = accountService.getDefaultImage(accountId);
        if (defaultImage == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } else {
            return new ResponseEntity<>(conversionService.convert(defaultImage, Image.class), HttpStatus.OK);
        }
    }

    /**
     * Update the default image for an account.
     *
     * @param accountId the account id
     * @param imageBean the image id
     * @return the account that's been updated
     */
    @RequestMapping(value = "/accounts/{accountId}/defaultImage", method = RequestMethod.PUT)
    @ResponseStatus(value = HttpStatus.OK)
    public Account setDefaultImage(
            @PathVariable("accountId") Long accountId,
            @RequestBody DefaultImageBean imageBean) {

        return conversionService.convert(
                accountService.setDefaultImage(accountId, imageBean.getImageId()), Account.class);
    }

    @RequestMapping(value = "/accounts/{accountId}/packages/all", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, BooleanResult> getPackagesByCategoryLocation(@PathVariable("accountId") Long accountId,
                                                             @RequestParam(value = "category_id", required = true)
                                                                     Long categoryId,
                                                             @RequestParam(value = "location_id", required = true)
                                                                     Long locationId) {
        Map<String, BooleanResult> result = new HashMap<>();
        for (ProductName productName : products) {
            boolean hasEligiblePackages = walletService.isCoveredByPackage(locationId, categoryId, accountId, productName);
            result.put(productName.name(), new BooleanResult(hasEligiblePackages));
        }
        return result;
    }

    /**
     * Get postcode associated with the latest advert posted by this account
     *
     * @param accountId account id
     * @return account postcode if found
     */
    @RequestMapping(value = "/accounts/{id}/postcode", method = RequestMethod.GET)
    @ResponseBody
    public List<String> getPostcode(@PathVariable("id") Long accountId) {
        return Collections.emptyList();
    }

    /**
     * Get seller type(business or private) for the account in the given category
     *
     * @param accountId  account id
     * @param categoryId category id
     * @return account's seller type
     */
    @RequestMapping(value = "/accounts/{accountId}/sellerType/{categoryId}", method = RequestMethod.GET)
    @ResponseBody
    public ValueResult<SellerType> getSellerType(@PathVariable("accountId") Long accountId,
                                                 @PathVariable("categoryId") Long categoryId) {
        SellerType sellerType = accountService.getSellerType(accountId, categoryId);
        return new ValueResult<>(sellerType);
    }

    /**
     * Updates reply type for account
     *
     * @param accountId The ID of account to update
     * @param replyType The reply type to set
     */
    @RequestMapping(value = "/accounts/{accountId}/reply_type/{replyType}", method = RequestMethod.PUT)
    @ResponseBody
    public void updateReplyType(@PathVariable("accountId") Long accountId,
                                @PathVariable("replyType") AccountReplyType replyType)  {
        accountService.updateReplyType(accountId, replyType);
    }
}
